class EnableRlsOnAllTables < ActiveRecord::Migration[7.1]
  def up
    # Check if we're running on Supabase (has service_role) or local PostgreSQL
    is_supabase = role_exists?('service_role')
    
    # Enable RLS on all tables that need it
    tables_to_secure = [
      'switch_users',
      'addresses', 
      'oauth_applications',
      'oauth_access_grants',
      'oauth_access_tokens',
      'security_events',
      'suppliers',
      'energy_tariffs',
      'energy_tariff_rates',
      'payment_methods',
      'supplier_submissions',
      'energy_switches',
      'user_tariffs',
      'admins',
      'xoserve_electricity_records'
    ]

    tables_to_secure.each do |table_name|
      # Enable RLS on the table
      execute "ALTER TABLE #{table_name} ENABLE ROW LEVEL SECURITY;"
      
      if is_supabase
        # On Supabase: Create policies for service_role and block API access
        execute <<-SQL
          CREATE POLICY "service_role_only_#{table_name}" ON #{table_name}
          FOR ALL 
          TO service_role
          USING (true)
          WITH CHECK (true);
        SQL
        
        # Block direct API access for anon and authenticated users
        execute <<-SQL
          CREATE POLICY "block_api_access_#{table_name}" ON #{table_name}
          FOR ALL 
          TO anon, authenticated
          USING (false)
          WITH CHECK (false);
        SQL
      else
        # Local development: Create a permissive policy for the current database user
        # This allows Rails to continue working normally
        execute <<-SQL
          CREATE POLICY "local_dev_access_#{table_name}" ON #{table_name}
          FOR ALL 
          USING (true)
          WITH CHECK (true);
        SQL
      end
    end

    # Special handling for Rails internal tables
    rails_internal_tables = ['schema_migrations', 'ar_internal_metadata']
    
    rails_internal_tables.each do |table_name|
      execute "ALTER TABLE #{table_name} ENABLE ROW LEVEL SECURITY;"
      
      if is_supabase
        execute <<-SQL
          CREATE POLICY "service_role_only_#{table_name}" ON #{table_name}
          FOR ALL 
          TO service_role
          USING (true)
          WITH CHECK (true);
        SQL
        
        execute <<-SQL
          CREATE POLICY "block_all_access_#{table_name}" ON #{table_name}
          FOR ALL 
          TO anon, authenticated
          USING (false)
          WITH CHECK (false);
        SQL
      else
        execute <<-SQL
          CREATE POLICY "local_dev_access_#{table_name}" ON #{table_name}
          FOR ALL 
          USING (true)
          WITH CHECK (true);
        SQL
      end
    end
  end

  private

  def role_exists?(role_name)
    result = execute("SELECT 1 FROM pg_roles WHERE rolname = '#{role_name}';")
    result.count > 0
  rescue
    false
  end

  def down
    # Disable RLS on all tables
    all_tables = [
      'switch_users',
      'addresses', 
      'oauth_applications',
      'oauth_access_grants',
      'oauth_access_tokens',
      'security_events',
      'suppliers',
      'energy_tariffs',
      'energy_tariff_rates',
      'payment_methods',
      'supplier_submissions',
      'energy_switches',
      'user_tariffs',
      'admins',
      'xoserve_electricity_records',
      'schema_migrations',
      'ar_internal_metadata'
    ]

    all_tables.each do |table_name|
      # Drop all possible policies
      execute "DROP POLICY IF EXISTS \"service_role_only_#{table_name}\" ON #{table_name};"
      execute "DROP POLICY IF EXISTS \"block_api_access_#{table_name}\" ON #{table_name};"
      execute "DROP POLICY IF EXISTS \"block_all_access_#{table_name}\" ON #{table_name};"
      execute "DROP POLICY IF EXISTS \"local_dev_access_#{table_name}\" ON #{table_name};"
      
      # Disable RLS
      execute "ALTER TABLE #{table_name} DISABLE ROW LEVEL SECURITY;"
    end
  end
end 