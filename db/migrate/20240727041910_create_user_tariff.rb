class CreateUserTariff < ActiveRecord::Migration[7.1]
  def change
    create_table :user_tariffs, id: :uuid, default: 'gen_random_uuid()' do |t|
      t.references :switch_user, type: :uuid, index: true
      t.integer :energy_type, null: false
      t.string :meter_point_reference_number
      t.string :meter_point_administration_number
      t.string :meter_serial_number
      t.decimal :unit_rate
      t.decimal :standing_charge
      t.string :supplier_name
      t.string :payment_method
      t.string :tariff_type
      t.decimal :gas_estimated_annual_usage
      t.decimal :gas_monthly_usage
      t.decimal :electricity_est_annual_usage
      t.decimal :electricity_monthly_usage

      t.timestamps
    end
  end
end