class CreateSecurityEvents < ActiveRecord::Migration[7.1]
  def change
    create_table :security_events do |t|
      t.string :event_type, null: false
      t.string :email
      t.string :ip_address
      t.text :details

      t.timestamps
    end
    
    # Add indexes for commonly queried fields
    add_index :security_events, :event_type
    add_index :security_events, :email
    add_index :security_events, :ip_address
    add_index :security_events, :created_at
    
    # Composite indexes for common query patterns
    add_index :security_events, [:email, :event_type]
    add_index :security_events, [:ip_address, :event_type]
  end
end
