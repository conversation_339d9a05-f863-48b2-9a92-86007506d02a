class ChangePaymentMethodToPaymentMethodsArray < ActiveRecord::Migration[7.1]
  def up
    if column_exists?(:energy_tariffs, :payment_method)
      rename_column :energy_tariffs, :payment_method, :payment_methods
      change_column :energy_tariffs, :payment_methods, :string, array: true, default: [], using: "(string_to_array(payment_methods, ','))"
    elsif !column_exists?(:energy_tariffs, :payment_methods)
      add_column :energy_tariffs, :payment_methods, :string, array: true, default: []
    end
  end

  def down
    if column_exists?(:energy_tariffs, :payment_methods)
      change_column :energy_tariffs, :payment_methods, :string
      rename_column :energy_tariffs, :payment_methods, :payment_method
    elsif column_exists?(:energy_tariffs, :payment_method)
      # Do nothing, it's already in the original state
    else
      remove_column :energy_tariffs, :payment_methods
    end
  end
end
