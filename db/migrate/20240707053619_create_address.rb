class CreateAddress < ActiveRecord::Migration[7.1]
  def change
    # Check if the addresses table already exists
    unless table_exists?(:addresses)
      create_table :addresses, id: :uuid, default: 'gen_random_uuid()' do |t|
        t.references :switch_user, type: :uuid, index: true
        t.string :postcode, null: false
        t.string :full_address, null: false
        t.string :posttown, null: false
        t.string :street
        t.string :number
        t.string :flat

        t.timestamps
      end
    end
  end
end