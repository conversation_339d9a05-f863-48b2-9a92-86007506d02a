class CreatePaymentMethod < ActiveRecord::Migration[7.1]
  def change
    create_table :payment_methods, id: :uuid, default: 'gen_random_uuid()' do |t|
      t.references :switch_user, type: :uuid, null: false, foreign_key: true
      t.string :type, null: false
      t.decimal :estimated_monthly_payment, precision: 10, scale: 2
      t.string :account_holder_name
      t.string :sort_code
      t.string :account_number
      t.boolean :billing_address_same_as_supply, default: true
      t.string :billing_address
      t.boolean :switch_within_5_days, default: false
      t.boolean :understand_cooling_off_period, default: false

      t.timestamps
    end
  end
end