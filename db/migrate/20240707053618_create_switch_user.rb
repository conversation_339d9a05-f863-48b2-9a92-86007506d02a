class CreateSwitchUser < ActiveRecord::Migration[7.1]
  def change
    enable_extension 'pgcrypto' unless extension_enabled?('pgcrypto')

    create_table :switch_users, id: :uuid, default: 'gen_random_uuid()' do |t|
      t.string :email, null: false
      t.string :first_name
      t.string :last_name
      t.string :title
      t.string :phone_number
      t.date :date_of_birth

      t.timestamps
    end

    add_index :switch_users, :email, unique: true
  end
end
