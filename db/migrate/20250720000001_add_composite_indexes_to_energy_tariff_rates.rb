class AddCompositeIndexesToEnergyTariffRates < ActiveRecord::Migration[7.1]
  def change
    # Most critical index for the common query pattern:
    # WHERE energy_tariff_id = ? AND fuel_type = ? AND gsp_code = ? AND profile_class = ?
    add_index :energy_tariff_rates, 
              [:energy_tariff_id, :fuel_type, :gsp_code, :profile_class], 
              name: 'idx_energy_tariff_rates_lookup'
    
    # Index for available_tariffs query:
    # WHERE fuel_type = ? AND profile_class = ? AND gsp_code = ?
    add_index :energy_tariff_rates, 
              [:fuel_type, :profile_class, :gsp_code], 
              name: 'idx_energy_tariff_rates_fuel_profile_gsp'
    
    # Index for gas queries with gsp_code filtering:
    # WHERE fuel_type = 'gas' AND gsp_code = ?
    add_index :energy_tariff_rates, 
              [:fuel_type, :gsp_code], 
              name: 'idx_energy_tariff_rates_fuel_gsp'
    
    # Index for existence checks:
    # WHERE energy_tariff_id = ? AND fuel_type = ?
    add_index :energy_tariff_rates, 
              [:energy_tariff_id, :fuel_type], 
              name: 'idx_energy_tariff_rates_tariff_fuel'
  end
end
