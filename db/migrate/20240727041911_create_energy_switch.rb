class CreateEnergySwitch < ActiveRecord::Migration[7.1]
  def change
    create_table :energy_switches, id: :uuid, default: 'gen_random_uuid()' do |t|
      t.references :switch_user, type: :uuid, index: true
      t.references :supplier, type: :uuid, index: true
      t.references :gas_energy_tariff, type: :uuid, index: true, foreign_key: { to_table: :user_tariffs }
      t.references :electricity_energy_tariff, type: :uuid, index: true, foreign_key: { to_table: :user_tariffs }
      t.integer :status, default: 0, null: false


      t.timestamps
    end
  end
end
