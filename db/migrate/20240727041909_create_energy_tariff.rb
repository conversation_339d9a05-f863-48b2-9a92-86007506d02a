class CreateEnergyTariff < ActiveRecord::Migration[7.1]
  def change
    create_table :energy_tariffs, id: :uuid, default: 'gen_random_uuid()' do |t|
      t.integer :energy_type, null: false
      t.string :tariff_name, null: false
      t.string :tariff_types, array: true, default: [], null: false

      t.float :unit_rate, null: false
      t.float :exit_fees, default: 0.0, null: false
      t.float :standing_charge, null: false # p/day

      t.string :payment_method, null: false

      t.references :supplier, index: true
      t.timestamps
    end
  end
end
