class UpdateEnergyTariffColumns < ActiveRecord::Migration[7.1]
  def up
    # Remove the old tariff_types column
    remove_column :energy_tariffs, :tariff_types

    # Add new tariff_type column as integer
    add_column :energy_tariffs, :tariff_type, :integer, default: 0, null: false

    # Add is_green column
    add_column :energy_tariffs, :is_green, :boolean, default: false
  end

  def down
    # Remove new columns
    remove_column :energy_tariffs, :is_green
    remove_column :energy_tariffs, :tariff_type

    # Add back the original tariff_types column
    add_column :energy_tariffs, :tariff_types, :string, array: true
  end
end
