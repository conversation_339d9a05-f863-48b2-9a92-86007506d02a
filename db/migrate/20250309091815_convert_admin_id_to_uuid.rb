class ConvertAdminIdToUuid < ActiveRecord::Migration[7.1]
  def change
    
    # Since resource_owner_id is already a UUID type in oauth_access_tokens,
    # we need to update the tokens to use the admin's uuid instead of the old id
    puts "Updating access tokens for admin users..."
    
    # Get the admin application
    admin_app_id = execute("SELECT id FROM oauth_applications WHERE name = 'Admin'").to_a.first&.dig('id')
    
    if admin_app_id
      # Update tokens for the admin application to use the admin UUID
      execute <<-SQL
        UPDATE oauth_access_tokens
        SET resource_owner_id = (
          SELECT a.uuid
          FROM admins a 
          WHERE a.id::text = resource_owner_id::text
        )
        WHERE application_id = #{admin_app_id}
          AND scopes = 'admin'
      SQL
    end
  end
  
  # def down
  #   # Only revert if we're using UUID as primary key
  #   if primary_key(:admins) == 'id' && column_exists?(:admins, :old_id)
  #     # 1. Remove the primary key constraint
  #     execute "ALTER TABLE admins DROP CONSTRAINT admins_pkey;"
      
  #     # 2. Rename id to uuid
  #     rename_column :admins, :id, :uuid
      
  #     # 3. Rename old_id to id
  #     rename_column :admins, :old_id, :id
      
  #     # 4. Add the primary key constraint back to id
  #     execute "ALTER TABLE admins ADD PRIMARY KEY (id);"
      
  #     # 5. Remove the uuid column if we added it
  #     remove_column :admins, :uuid if column_exists?(:admins, :uuid)
  #   end
    
  #   # Get the admin application
  #   admin_app_id = execute("SELECT id FROM oauth_applications WHERE name = 'Admin'").to_a.first&.dig('id')
    
  #   if admin_app_id
  #     # Revert tokens back to using the old integer admin id
  #     execute <<-SQL
  #       UPDATE oauth_access_tokens
  #       SET resource_owner_id = (
  #         SELECT a.old_id::uuid
  #         FROM admins a 
  #         WHERE a.id = resource_owner_id
  #       )
  #       WHERE application_id = #{admin_app_id}
  #         AND scopes = 'admin'
  #     SQL
  #   end
  # end
end
