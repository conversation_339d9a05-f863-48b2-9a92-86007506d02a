class ChangeResourceOwnerIdToUuidInOauthAccessTokens < ActiveRecord::Migration[6.1]
  def up
    # Temporarily change resource_owner_id to string
    change_column :oauth_access_tokens, :resource_owner_id, :string

    # Update existing records to have valid UUIDs
    Doorkeeper::AccessToken.where.not(resource_owner_id: nil).find_each do |token|
      token.update_column(:resource_owner_id, SecureRandom.uuid)
    end

    # Change column type to uuid
    change_column :oauth_access_tokens, :resource_owner_id, :uuid, using: 'resource_owner_id::uuid'
  end

  def down
    change_column :oauth_access_tokens, :resource_owner_id, :integer
  end
end