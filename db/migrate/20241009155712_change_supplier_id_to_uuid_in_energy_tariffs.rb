class ChangeSupplierIdToUuidInEnergyTariffs < ActiveRecord::Migration[7.1]
  def up
    # Remove the existing index and foreign key if it exists
    remove_index :energy_tariffs, :supplier_id if index_exists?(:energy_tariffs, :supplier_id)
    remove_foreign_key :energy_tariffs, :suppliers if foreign_key_exists?(:energy_tariffs, :suppliers)

    # Add a new uuid column
    add_column :energy_tariffs, :supplier_uuid, :uuid

    # Copy data from the old column to the new column
    execute <<-SQL
      UPDATE energy_tariffs
      SET supplier_uuid = suppliers.id::uuid
      FROM suppliers
      WHERE energy_tariffs.supplier_id::text = suppliers.id::text
    SQL

    # Remove the old column
    remove_column :energy_tariffs, :supplier_id

    # Rename the new column to supplier_id
    rename_column :energy_tariffs, :supplier_uuid, :supplier_id

    # Add the index back
    add_index :energy_tariffs, :supplier_id

    # Add the foreign key back
    add_foreign_key :energy_tariffs, :suppliers
  end

  def down
    raise ActiveRecord::IrreversibleMigration
  end
end
