class CreateEnergyTariffRates < ActiveRecord::Migration[7.1]
  def change
    create_table :energy_tariff_rates, id: :uuid do |t|
      t.string :region
      t.string :region_id
      t.string :gsp_code
      t.decimal :standing_charge_ex_vat, precision: 10, scale: 4
      t.decimal :standing_charge_inc_vat, precision: 10, scale: 4

      # For profile class 1
      t.decimal :unit_rate_ex_vat, precision: 10, scale: 4
      t.decimal :unit_rate_inc_vat, precision: 10, scale: 4

      # For profile class 2
      t.decimal :day_unit_rate_ex_vat, precision: 10, scale: 4
      t.decimal :day_unit_rate_inc_vat, precision: 10, scale: 4
      t.decimal :night_unit_rate_ex_vat, precision: 10, scale: 4
      t.decimal :night_unit_rate_inc_vat, precision: 10, scale: 4
      t.decimal :weekend_unit_rate_ex_vat, precision: 10, scale: 4
      t.decimal :weekend_unit_rate_inc_vat, precision: 10, scale: 4

      t.decimal :annual_bill, precision: 10, scale: 2

      t.string :fuel_type, null: false
      t.decimal :exit_fees, precision: 10, scale: 2, default: 0
      t.integer :profile_class, null: false
 
      t.references :energy_tariff, type: :uuid, foreign_key: true

      t.timestamps
    end

    add_index :energy_tariff_rates, :fuel_type
  end
end
