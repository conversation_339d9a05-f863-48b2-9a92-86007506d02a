class CreateSupplierSubmissions < ActiveRecord::Migration[7.1]
  def change
    create_table :supplier_submissions, id: :uuid do |t|
      t.references :energy_switch, type: :uuid, null: false, foreign_key: true
      t.references :supplier, type: :uuid, null: false, foreign_key: true
      t.string :submission_type, null: false
      t.integer :status, null: false, default: 0
      t.string :supplier_reference
      t.datetime :submitted_at
      t.datetime :processed_at
      t.text :raw_request
      t.text :raw_response
      t.text :error_message
      t.string :rejection_reason
      t.integer :attempt_number, null: false, default: 1
      t.text :notes

      t.timestamps
    end
    
    add_index :supplier_submissions, :status
    add_index :supplier_submissions, :supplier_reference
    add_index :supplier_submissions, :submitted_at
  end
end 