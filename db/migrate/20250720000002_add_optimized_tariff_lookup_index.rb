class AddOptimizedTariffLookupIndex < ActiveRecord::Migration[7.1]
  def change
    # Add a covering index for the most expensive query
    add_index :energy_tariffs, 
              [:energy_type, :supplier_id], 
              name: 'idx_energy_tariffs_type_supplier'
    
    # Add index for energy_tariff_rates with all commonly queried columns
    add_index :energy_tariff_rates,
              [:fuel_type, :profile_class, :gsp_code, :energy_tariff_id],
              name: 'idx_energy_tariff_rates_covering'
    
    # Add index for user_tariffs lookup
    add_index :user_tariffs,
              [:id, :energy_type],
              name: 'idx_user_tariffs_id_type'
  end
end
