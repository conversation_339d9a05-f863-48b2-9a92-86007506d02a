class AddReferenceNumberToEnergySwitches < ActiveRecord::Migration[7.1]
  def up
    add_column :energy_switches, :reference_number, :string

    # Add a unique index
    add_index :energy_switches, :reference_number, unique: true

    # Generate reference numbers for existing records
    EnergySwitch.find_each do |switch|
      switch.send(:ensure_unique_reference_number)
      switch.save(validate: false)
    end

    # Add the null: false constraint
    change_column_null :energy_switches, :reference_number, false
  end

  def down
    remove_column :energy_switches, :reference_number
  end
end
