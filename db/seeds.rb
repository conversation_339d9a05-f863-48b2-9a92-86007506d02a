# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

# Create default admin user
if Admin.count.zero?
  Admin.create!(
    email: '<EMAIL>',
    password: 'password123!',
    first_name: '<PERSON><PERSON>',
    last_name: '<PERSON>'
  )
end

# Create onboarding application
Doorkeeper::Application.create!(name: "Onboarding", redirect_uri: "urn:ietf:wg:oauth:2.0:oob", scopes: ["onboarding"])

# Create admin application
Doorkeeper::Application.create!(name: "Admin", redirect_uri: "urn:ietf:wg:oauth:2.0:oob", scopes: ["admin"])

# Create chatbot application
chatapp = Doorkeeper::Application.create!(name: "Chat<PERSON><PERSON>", redirect_uri: "urn:ietf:wg:oauth:2.0:oob", scopes: ["chatbot"])

# Create chatbot access token
Doorkeeper::AccessToken.create!(
  application_id: chatapp.id,
  scopes: ["chatbot"]
)
