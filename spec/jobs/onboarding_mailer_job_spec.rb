require 'rails_helper'
require 'sidekiq/testing'

RSpec.describe SendOnboardingVerificationCodeJob, type: :job do
  describe '#perform' do
    let(:email) { '<EMAIL>' }
    let(:code) { 123456 }

    it 'sends an onboarding email' do
      expect(OnboardingMailer).to receive_message_chain(:with, :onboarding_email, :deliver_now!)
      
      SendOnboardingVerificationCodeJob.new.perform(email, code)
    end

    it 'calls OnboardingMailer with correct parameters' do
      expect(OnboardingMailer).to receive(:with).with(
        email: email,
        code: code
      ).and_return(double(onboarding_email: double(deliver_now!: true)))
      
      SendOnboardingVerificationCodeJob.new.perform(email, code)
    end

    it 'is enqueued with correct queue' do
      Sidekiq::Testing.fake! do
        expect {
          SendOnboardingVerificationCodeJob.perform_async(email, code)
        }.to change(SendOnboardingVerificationCodeJob.jobs, :size).by(1)
      end
    end

    it 'is enqueued with correct queue name' do
      expect(SendOnboardingVerificationCodeJob.sidekiq_options['queue']).to eq('priority')
    end
  end
end
