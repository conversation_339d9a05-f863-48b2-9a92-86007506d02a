require 'rails_helper'

RSpec.describe SendSecurityReportJob, type: :job do
  before do
    # Mock Rails.logger
    allow(Rails.logger).to receive(:info).and_return(true)
    
    # Mock the mailer
    mail_double = double('Mail::Message', deliver_now: true)
    allow(SecurityMailer).to receive(:suspicious_activity_report).and_return(mail_double)
  end
  
  describe '#perform' do
    context 'when there are security events to report' do
      before do
        # Mock the SecurityEvent count to return events
        allow(SecurityEvent).to receive_message_chain(:where, :count).and_return(10)
      end
      
      it 'sends a daily report by default' do
        job = SendSecurityReportJob.new
        job.perform
        
        expect(SecurityMailer).to have_received(:suspicious_activity_report).with('daily')
      end
      
      it 'sends a report for the specified period' do
        job = SendSecurityReportJob.new
        job.perform('hourly')
        
        expect(SecurityMailer).to have_received(:suspicious_activity_report).with('hourly')
      end
      
      it 'logs that the report was sent' do
        job = SendSecurityReportJob.new
        job.perform
        
        expect(Rails.logger).to have_received(:info).with(/Security report \(daily\) sent with 10 events/)
      end
    end
    
    context 'when there are no security events to report' do
      before do
        # Mock the SecurityEvent count to return no events
        allow(SecurityEvent).to receive_message_chain(:where, :count).and_return(0)
      end
      
      it 'does not send a report' do
        job = SendSecurityReportJob.new
        job.perform
        
        expect(SecurityMailer).not_to have_received(:suspicious_activity_report)
      end
      
      it 'logs that no report was sent' do
        job = SendSecurityReportJob.new
        job.perform
        
        expect(Rails.logger).to have_received(:info).with(/No security events found for daily report/)
      end
    end
    
    context 'with invalid period argument' do
      it 'raises an ArgumentError' do
        job = SendSecurityReportJob.new
        
        expect {
          job.perform('invalid_period')
        }.to raise_error(ArgumentError, /Invalid period/)
      end
    end
  end
  
  describe 'Sidekiq options' do
    it 'uses the security queue' do
      expect(SendSecurityReportJob.sidekiq_options_hash['queue']).to eq('security')
    end
    
    it 'allows retries' do
      expect(SendSecurityReportJob.sidekiq_options_hash['retry']).to eq(3)
    end
  end
end 