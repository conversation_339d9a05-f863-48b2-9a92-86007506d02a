require 'rails_helper'

RSpec.describe ResubmitPendingSwitchesJob, type: :job do
  describe '#perform' do
    let(:supplier) { create(:supplier, name: 'Tulo Energy') }
    let(:energy_tariff) { create(:energy_tariff, supplier: supplier) }
    
    before do
      allow(SubmitEnergySwitchJob).to receive(:perform_async)
    end

    context 'when there are confirmed energy switches without supplier submissions' do
      let!(:energy_switch) do
        create(:energy_switch, 
               :with_gas,
               status: :confirmed,
               switching_to_tariff: energy_tariff)
      end

      it 'enqueues a SubmitEnergySwitchJob for each switch' do
        expect {
          ResubmitPendingSwitchesJob.new.perform
        }.not_to raise_error

        expect(SubmitEnergySwitchJob).to have_received(:perform_async).with(energy_switch.id)
      end
    end

    context 'when there are confirmed energy switches with supplier submissions' do
      let!(:energy_switch) do
        switch = create(:energy_switch, 
                        :with_gas,
                        status: :confirmed,
                        switching_to_tariff: energy_tariff)
        
        # Create a supplier submission for this switch
        create(:supplier_submission, 
               energy_switch: switch,
               supplier: supplier,
               status: :pending)
               
        switch
      end

      it 'does not enqueue a SubmitEnergySwitchJob' do
        ResubmitPendingSwitchesJob.new.perform
        expect(SubmitEnergySwitchJob).not_to have_received(:perform_async).with(energy_switch.id)
      end
    end

    context 'when there are no confirmed energy switches' do
      before do
        # Create a switch that's not confirmed
        create(:energy_switch, 
               :with_gas,
               status: :draft,
               switching_to_tariff: energy_tariff)
      end

      it 'does not enqueue any jobs' do
        ResubmitPendingSwitchesJob.new.perform
        expect(SubmitEnergySwitchJob).not_to have_received(:perform_async)
      end
    end
    
    context 'when there are failed or rejected energy switches' do
      let!(:failed_switch) do
        switch = create(:energy_switch, 
                      :with_gas,
                      status: :submitted_to_supplier,
                      switching_to_tariff: energy_tariff)
        
        # Create a failed supplier submission
        create(:supplier_submission, 
              energy_switch: switch,
              supplier: supplier,
              status: :failed,
              attempt_number: 1)
              
        switch
      end
      
      let!(:rejected_switch) do
        switch = create(:energy_switch, 
                      :with_gas,
                      status: :rejected_by_supplier,
                      switching_to_tariff: energy_tariff)
        
        # Create a rejected supplier submission
        create(:supplier_submission, 
              energy_switch: switch,
              supplier: supplier,
              status: :rejected,
              attempt_number: 2)
              
        switch
      end
      
      it 'enqueues SubmitEnergySwitchJob for failed and rejected switches' do
        ResubmitPendingSwitchesJob.new.perform
        
        expect(SubmitEnergySwitchJob).to have_received(:perform_async).with(failed_switch.id)
        expect(SubmitEnergySwitchJob).to have_received(:perform_async).with(rejected_switch.id)
      end
    end
    
    context 'when there are failed switches with pending submissions' do
      let!(:switch_with_mixed_submissions) do
        switch = create(:energy_switch, 
                      :with_gas,
                      status: :submitted_to_supplier,
                      switching_to_tariff: energy_tariff)
        
        # Create a failed submission
        create(:supplier_submission, 
              energy_switch: switch,
              supplier: supplier,
              status: :failed,
              attempt_number: 1)
              
        # Also create a pending submission
        create(:supplier_submission, 
              energy_switch: switch,
              supplier: supplier,
              status: :pending,
              attempt_number: 2)
              
        switch
      end
      
      it 'does not enqueue SubmitEnergySwitchJob for switches with pending submissions' do
        ResubmitPendingSwitchesJob.new.perform
        
        expect(SubmitEnergySwitchJob).not_to have_received(:perform_async).with(switch_with_mixed_submissions.id)
      end
    end
    
    context 'when there are failed switches with max retry attempts' do
      let!(:switch_with_max_retries) do
        switch = create(:energy_switch, 
                      :with_gas,
                      status: :submitted_to_supplier,
                      switching_to_tariff: energy_tariff)
        
        # Create a failed submission with max retries
        create(:supplier_submission, 
              energy_switch: switch,
              supplier: supplier,
              status: :failed,
              attempt_number: 5)
              
        switch
      end
      
      it 'does not enqueue SubmitEnergySwitchJob for switches with max retry attempts' do
        ResubmitPendingSwitchesJob.new.perform
        
        expect(SubmitEnergySwitchJob).not_to have_received(:perform_async).with(switch_with_max_retries.id)
      end
    end
  end

  describe 'sidekiq options' do
    it 'has retry disabled' do
      expect(ResubmitPendingSwitchesJob.sidekiq_options_hash['retry']).to eq(0)
    end
  end
end 