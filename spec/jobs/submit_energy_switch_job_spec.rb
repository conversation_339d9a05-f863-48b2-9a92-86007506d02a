require 'rails_helper'

RSpec.describe SubmitEnergySwitchJob, type: :job do
  include ActiveJob::TestHelper
  
  let(:supplier) { create(:supplier, name: 'Tulo Energy') }
  let(:energy_tariff) { create(:energy_tariff, supplier: supplier) }
  let(:energy_switch) { create(:energy_switch, :with_gas, switching_to_tariff: energy_tariff) }
  let(:service_instance) { instance_double(TuloSwitchSubmissionService) }
  
  describe '#perform' do
    before do
      allow(TuloSwitchSubmissionService).to receive(:new).and_return(service_instance)
      allow(service_instance).to receive(:submit).and_return(true)
    end
    
    it 'calls appropriate service based on supplier name' do
      expect(TuloSwitchSubmissionService).to receive(:new).with(energy_switch)
      expect(service_instance).to receive(:submit)
      
      subject.perform(energy_switch.id)
    end
    
    context 'when energy switch has no associated supplier' do
      let(:energy_switch) { create(:energy_switch, :with_gas, switching_to_tariff: nil) }
      
      it 'logs an error and returns' do
        expect(Rails.logger).to receive(:error).with(/no associated supplier/)
        expect(service_instance).not_to receive(:submit)
        
        subject.perform(energy_switch.id)
      end
    end
    
    context 'when there is already a valid submission' do
      let!(:valid_submission) do
        create(:supplier_submission, :successful, 
          energy_switch: energy_switch,
          supplier: supplier,
          created_at: 30.minutes.ago
        )
      end
      
      it 'does not create a new submission' do
        expect(service_instance).not_to receive(:submit)
        
        subject.perform(energy_switch.id)
      end
      
      it 'logs info about existing submission' do
        expect(Rails.logger).to receive(:info).with(/already has a valid submission/)
        
        subject.perform(energy_switch.id)
      end
    end
    
    context 'when the supplier is not supported' do
      let(:supplier) { create(:supplier, name: 'Unsupported Supplier') }
      
      it 'creates a failed submission record' do
        expect {
          expect { subject.perform(energy_switch.id) }.to raise_error(/Unsupported supplier/)
        }.to change(SupplierSubmission, :count).by(1)
        
        submission = SupplierSubmission.last
        expect(submission.status).to eq('failed')
        expect(submission.error_message).to eq("Unsupported supplier: #{supplier.name}")
      end
    end
  end
  
  describe 'retry behavior' do
    it 'configures Sidekiq retry with exponential backoff' do
      expect(described_class.sidekiq_options_hash['retry']).to eq(5)
    end
    
    it 'has correct retry intervals' do
      # Directly test the formula from the job class
      # (count ** 2) * 5 * 60
      expect(5 * 60).to eq(300)        # 5 minutes for count=1
      expect(20 * 60).to eq(1200)      # 20 minutes for count=2
      expect(45 * 60).to eq(2700)      # 45 minutes for count=3
      expect(80 * 60).to eq(4800)      # 80 minutes for count=4
      expect(125 * 60).to eq(7500)     # 125 minutes for count=5
      
      # Verify these match our retry_in implementation in the job
      retry_proc = described_class.new.method(:calculate_retry_delay)
      expect(retry_proc.call(1)).to eq(300)
      expect(retry_proc.call(2)).to eq(1200)
      expect(retry_proc.call(3)).to eq(2700)
      expect(retry_proc.call(4)).to eq(4800)
      expect(retry_proc.call(5)).to eq(7500)
    end
  end
end 