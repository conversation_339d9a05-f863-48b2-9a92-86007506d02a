require 'rails_helper'

RSpec.describe SendSecurityAlertJob, type: :job do
  describe '#perform' do
    let(:message) { "Test security alert message" }
    let(:logger) { instance_double(ActiveSupport::Logger) }
    let(:slack_notifier) { instance_double(Slack::Notifier) }
    let(:security_mailer) { instance_double(ActionMailer::MessageDelivery) }

    before do
      allow(Rails).to receive(:logger).and_return(logger)
      allow(logger).to receive(:error)
    end

    it 'logs the security alert message' do
      expect(logger).to receive(:error).with("SECURITY ALERT: #{message}")
      
      described_class.new.perform(message)
    end

    context 'with Slack notifications' do
      before do
        stub_const('Slack', Module.new)
        allow(ENV).to receive(:[]).with('SLACK_SECURITY_WEBHOOK_URL').and_return('https://hooks.slack.com/test-webhook')
        allow(Slack::Notifier).to receive(:new).and_return(slack_notifier)
        allow(slack_notifier).to receive(:ping)
      end

      it 'sends a Slack notification' do
        expect(Slack::Notifier).to receive(:new).with('https://hooks.slack.com/test-webhook')
        expect(slack_notifier).to receive(:ping).with("🚨 SECURITY ALERT: #{message}")
        
        described_class.new.perform(message)
      end

      it 'handles Slack notification errors gracefully' do
        error = StandardError.new('Slack API Error')
        allow(slack_notifier).to receive(:ping).and_raise(error)
        
        expect(logger).to receive(:error).with("SECURITY ALERT: #{message}")
        expect(logger).to receive(:error).with("Failed to send Slack notification: Slack API Error")
        
        # This should not raise an error
        expect { described_class.new.perform(message) }.not_to raise_error
      end
    end

    context 'without Slack configured' do
      before do
        allow(ENV).to receive(:[]).with('SLACK_SECURITY_WEBHOOK_URL').and_return(nil)
      end

      it 'skips Slack notification' do
        # The job should run without errors even when Slack is not available
        expect { described_class.new.perform(message) }.not_to raise_error
      end
    end

    context 'with security team email' do
      before do
        allow(ENV).to receive(:[]).with('SECURITY_TEAM_EMAIL').and_return('<EMAIL>')
        allow(SecurityMailer).to receive(:alert).and_return(security_mailer)
        allow(security_mailer).to receive(:deliver_now)
      end

      it 'sends a security email alert' do
        expect(SecurityMailer).to receive(:alert).with(message)
        expect(security_mailer).to receive(:deliver_now)
        
        described_class.new.perform(message)
      end

      it 'handles email sending errors gracefully' do
        error = StandardError.new('Email Delivery Error')
        allow(security_mailer).to receive(:deliver_now).and_raise(error)
        
        expect(logger).to receive(:error).with("SECURITY ALERT: #{message}")
        expect(logger).to receive(:error).with("Failed to send security email: Email Delivery Error")
        
        # This should not raise an error
        expect { described_class.new.perform(message) }.not_to raise_error
      end
    end

    context 'without security team email configured' do
      before do
        allow(ENV).to receive(:[]).with('SECURITY_TEAM_EMAIL').and_return(nil)
      end

      it 'skips sending email alert' do
        # The job should run without errors even when email is not configured
        expect { described_class.new.perform(message) }.not_to raise_error
      end
    end

    context 'job execution' do
      it 'is enqueued with priority' do
        # Test that the job gets enqueued with the expected queue
        expect {
          described_class.perform_async(message)
        }.to change(described_class.jobs, :size).by(1)
        
        expect(described_class.queue_as).to eq(:priority)
      end
    end
  end
end 