require 'spec_helper'
require_relative '../../lib/api_clients/tulo_api_client'
require 'webmock/rspec'

# Need to stub `ENV` before requiring the class to be tested
RSpec.describe TuloApiClient do
  # Define test values
  let(:test_url) { 'https://test-api.tulo.energy' }
  let(:test_auth_key) { 'test-auth-key-123' }
  
  # Set up environment stubs before initializing client
  before(:each) do
    # Stub environment variables
    allow(ENV).to receive(:[]).and_call_original
    allow(ENV).to receive(:[]).with('TULO_ENERGY_API_KEY').and_return(test_auth_key)
    allow(ENV).to receive(:[]).with('TULO_ENERGY_API_URL').and_return(test_url)
    
    # Disable real HTTP connections
    WebMock.disable_net_connect!
  end
  
  # Initialize client after environment is stubbed
  let(:client) { TuloApiClient.new }

  describe '#initialize' do
    it 'sets up the client with the API URL from environment variable' do
      expect(client.instance_variable_get(:@base_url)).to eq(test_url)
    end

    it 'stores the auth key from environment variable' do
      expect(client.instance_variable_get(:@auth_key)).to eq(test_auth_key)
    end
    
    it 'raises an error if TULO_ENERGY_API_URL is not set' do
      allow(ENV).to receive(:[]).with('TULO_ENERGY_API_URL').and_return(nil)
      
      expect { TuloApiClient.new }.to raise_error(ArgumentError, /TULO_ENERGY_API_URL environment variable is not set/)
    end
  end

  describe '#sale_made' do
    let(:valid_params) do
      {
        'SaleMadeBy' => 'Test Agent',
        'SaleMadeOn' => '2023-01-01T12:00:00Z',
        'SaleValidatedBy' => 'Validator',
        'SaleValidatedOn' => '2023-01-01T12:30:00Z',
        'SaleValidationStatus' => 'Validated',
        'SaleChannel' => 'Direct',
        'SaleType' => 'CoS',
        'SaleCustomerType' => 1,
        'CustomerReference' => 'CUST123',
        'ContractReference' => 'CONT123',
        'CustomerTitle' => 'Mr',
        'CustomerFirstName' => 'John',
        'CustomerLastName' => 'Doe',
        'CustomerAddressLine1' => '123 Test Street',
        'CustomerAddressLine2' => 'Suite 101',
        'CustomerTownCity' => 'Test City',
        'CustomerCounty' => 'Test County',
        'CustomerPostcode' => 'TE1 1ST',
        'CustomerEmail' => '<EMAIL>',
        'CustomerPhoneNumber' => '0********90',
        'PaymentMethod' => 'DIRECTDEBIT',
        'AnticipatedStartDate' => '2023-02-01T00:00:00Z',
        'ElectricityTariffName' => 'Test Electricity Tariff',
        'GasTariffName' => 'Test Gas Tariff',
        'MPANs' => [
          {
            'MPAN' => '********90123',
            'UseIndustryDataLookup' => true
          }
        ],
        'MPRNs' => [
          {
            'MPRN' => '********90',
            'UseIndustryDataLookup' => true
          }
        ],
        'BankAccountName' => 'John Doe',
        'BankAccountNumber' => '********',
        'BankSortCode' => '123456'
      }
    end

    context 'with valid parameters' do
      it 'returns the successful response' do
        expected_response = {
          'success' => true,
          'customerQuoteId' => 1234,
          'errorMessage' => ''
        }

        stub_request(:post, "#{test_url}/api/CustomerQuote/SaleMadeV2")
          .with(
            headers: { 'Content-Type' => 'application/json' }
          )
          .to_return(status: 200, body: expected_response.to_json, headers: { 'Content-Type' => 'application/json' })

        response = client.sale_made(valid_params)
        expect(response).to eq(expected_response)
      end
    end

    context 'with error responses' do
      it 'raises an error for 400 response with error message' do
        error_response = {
          'errorMessage' => 'Customer FirstName Missing'
        }

        stub_request(:post, "#{test_url}/api/CustomerQuote/SaleMadeV2")
          .with(
            headers: { 'Content-Type' => 'application/json' }
          )
          .to_return(status: 400, body: error_response.to_json, headers: { 'Content-Type' => 'application/json' })

        expect { client.sale_made(valid_params) }.to raise_error(/Customer FirstName Missing/)
      end

      it 'raises an error for non-200 responses' do
        stub_request(:post, "#{test_url}/api/CustomerQuote/SaleMadeV2")
          .with(
            headers: { 'Content-Type' => 'application/json' }
          )
          .to_return(status: 500, body: 'Server Error', headers: { 'Content-Type' => 'text/plain' })

        expect { client.sale_made(valid_params) }.to raise_error(/Unexpected response: 500/)
      end

      it 'raises an error for invalid JSON responses' do
        stub_request(:post, "#{test_url}/api/CustomerQuote/SaleMadeV2")
          .with(
            headers: { 'Content-Type' => 'application/json' }
          )
          .to_return(status: 200, body: '{invalid json}', headers: { 'Content-Type' => 'application/json' })

        expect { client.sale_made(valid_params) }.to raise_error(/Invalid JSON response/)
      end
    end
  end

  describe 'parameter validation' do
    context 'with missing required fields' do
      it 'raises an error for missing top-level required fields' do
        invalid_params = {
          'SaleMadeBy' => 'Test Agent',
          'SaleMadeOn' => '2023-01-01T12:00:00Z',
          'SaleType' => 'CoS',
          # Missing other required fields
        }

        expect { client.sale_made(invalid_params) }.to raise_error(ArgumentError, /Missing required fields/)
      end

      it 'raises an error for invalid SaleType' do
        invalid_params = {
          'SaleMadeBy' => 'Test Agent',
          'SaleMadeOn' => '2023-01-01T12:00:00Z',
          'SaleType' => 'Invalid', # Invalid SaleType
          'SaleCustomerType' => 1,
          'CustomerTitle' => 'Mr',
          'CustomerFirstName' => 'John',
          'CustomerLastName' => 'Doe',
          'CustomerAddressLine1' => '123 Test Street',
          'CustomerPostcode' => 'TE1 1ST',
          'CustomerPhoneNumber' => '0********90',
          'PaymentMethod' => 'DIRECTDEBIT',
        }

        expect { client.sale_made(invalid_params) }.to raise_error(ArgumentError, /SaleType must be one of/)
      end
    end

    context 'with invalid MPAN data' do
      it 'raises an error for missing required MPAN fields when not using industry lookup' do
        invalid_params = {
          # Include all required top-level fields here
          'SaleMadeBy' => 'Test Agent',
          'SaleMadeOn' => '2023-01-01T12:00:00Z',
          'SaleType' => 'CoS',
          'SaleCustomerType' => 1,
          'CustomerTitle' => 'Mr',
          'CustomerFirstName' => 'John',
          'CustomerLastName' => 'Doe',
          'CustomerAddressLine1' => '123 Test Street',
          'CustomerPostcode' => 'TE1 1ST',
          'CustomerPhoneNumber' => '0********90',
          'PaymentMethod' => 'DIRECTDEBIT',
          'MPANs' => [
            {
              'MPAN' => '********90123',
              'UseIndustryDataLookup' => false,
              # Missing MeterType, GSP, PC, MTC, LLF, SSC, EnergisationStatus
            }
          ]
        }

        expect { client.sale_made(invalid_params) }.to raise_error(ArgumentError, /Missing required MPAN fields/)
      end
    end

    context 'with invalid MPRN data' do
      it 'raises an error for missing required MPRN fields when not using industry lookup' do
        invalid_params = {
          # Include all required top-level fields here
          'SaleMadeBy' => 'Test Agent',
          'SaleMadeOn' => '2023-01-01T12:00:00Z',
          'SaleType' => 'CoS',
          'SaleCustomerType' => 1,
          'CustomerTitle' => 'Mr',
          'CustomerFirstName' => 'John',
          'CustomerLastName' => 'Doe',
          'CustomerAddressLine1' => '123 Test Street',
          'CustomerPostcode' => 'TE1 1ST',
          'CustomerPhoneNumber' => '0********90',
          'PaymentMethod' => 'DIRECTDEBIT',
          'MPRNs' => [
            {
              'MPRN' => '********90',
              'UseIndustryDataLookup' => false,
              # Missing MeterType, LDZ
            }
          ]
        }

        expect { client.sale_made(invalid_params) }.to raise_error(ArgumentError, /Missing required MPRN fields/)
      end
    end
  end
end