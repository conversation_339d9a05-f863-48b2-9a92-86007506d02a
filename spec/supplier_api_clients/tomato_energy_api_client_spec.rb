require 'spec_helper'
require 'webmock/rspec'
require_relative '../../lib/api_clients/tomato_energy_api_client'

RSpec.describe TomatoEnergyApiClient do
  let(:client) { described_class.new }
  
  # Define a helper method for deep clone since we don't have <PERSON>s's deep_dup
  def deep_clone(obj)
    return obj.clone if obj.is_a?(String) || obj.is_a?(Numeric) || [true, false, nil].include?(obj)
    return obj.map { |item| deep_clone(item) } if obj.is_a?(Array)
    return obj.keys.each_with_object({}) { |key, result| result[key] = deep_clone(obj[key]) } if obj.is_a?(Hash)
    obj.clone
  end

  # Stub environment variables
  before(:each) do
    allow(ENV).to receive(:[]).and_call_original
    allow(ENV).to receive(:[]).with('TOMATO_ENERGY_API_KEY').and_return('test-api-key')
    allow(ENV).to receive(:[]).with('TOMATO_ENERGY_API_URL').and_return('https://test-api.tomato.energy')
    
    # Disable real HTTP connections
    WebMock.disable_net_connect!
  end

  describe '#initialize' do
    it 'sets up the client with the API URL from environment variable' do
      expect(client.instance_variable_get(:@base_url)).to eq('https://test-api.tomato.energy')
    end

    it 'stores the API key from environment variable' do
      expect(client.instance_variable_get(:@api_key)).to eq('test-api-key')
    end
    
    it 'raises an error if TOMATO_ENERGY_API_URL is not set' do
      allow(ENV).to receive(:[]).with('TOMATO_ENERGY_API_URL').and_return(nil)
      
      expect { TomatoEnergyApiClient.new }.to raise_error(ArgumentError, /TOMATO_ENERGY_API_URL environment variable is not set/)
    end
  end

  describe '#create_application' do
    let(:valid_params) do
      {
        'externalReference' => 'MEETGEORGE-TEST-REF-1',
        'submittedDateTime' => '2024-01-20T10:00:00Z',
        'type' => 'SWITCH',
        'sender' => { 'reference' => 'SENDER_REF' },
        'channel' => { 'reference' => 'CHANNEL_REF' },
        'customer' => {
          'title' => 'Mr',
          'firstName' => 'John',
          'lastName' => 'Doe',
          'dateOfBirth' => '1990-01-01',
          'emailAddress' => '<EMAIL>',
          'phoneNumber' => '07700900000'
        },
        'supplyAddress' => {
          'postcode' => 'SW1A 1AA',
          'thoroughfare' => '10 Downing Street'
        },
        'isBillingAddressSameAsSupplyAddress' => true,
        'consent' => {
          'creditCheck' => true,
          'coolingOffPeriodPayment' => true
        },
        'electricitySupply' => {
          'mpan' => '200000653506',
          'isEconomy7' => false,
          'isSmartMeter' => false
        },
        'newElectricityTariff' => {
          'supplierReference' => 'SUPPLIER_REF',
          'reference' => 'TARIFF_REF',
          'name' => 'Standard Variable',
          'paymentType' => 'Variable Direct Debit only',
          'billingMethod' => 'MONTHLY'
        }
      }
    end

    context 'with successful API call' do
      before do
        stub_request(:post, "https://test-api.tomato.energy/api/v1/applications")
          .with(
            headers: {
              'Content-Type' => 'application/json',
              'x-api-key' => 'test-api-key'
            },
            body: { application: valid_params }.to_json
          )
          .to_return(
            status: 200,
            body: {
              application: {
                electricityReference: 'ELEC123',
                gasReference: 'GAS123'
              }
            }.to_json
          )
      end

      it 'creates an application successfully' do
        response = client.create_application(valid_params)
        expect(response['application']['electricityReference']).to eq('ELEC123')
        expect(response['application']['gasReference']).to eq('GAS123')
      end
    end

    context 'with validation errors' do
      context 'with missing top-level required fields' do
        it 'raises ArgumentError' do
          invalid_params = valid_params.reject { |k, _| ['externalReference', 'type'].include?(k) }
          expect { client.create_application(invalid_params) }
            .to raise_error(ArgumentError, /Missing required fields: externalReference, type/)
        end
      end

      context 'with invalid customer details' do
        it 'raises ArgumentError for missing customer fields' do
          invalid_params = deep_clone(valid_params)
          invalid_params['customer'] = { 'title' => 'Mr' }
          expect { client.create_application(invalid_params) }
            .to raise_error(ArgumentError, /Missing required customer fields:/)
        end
      end

      context 'with invalid address' do
        it 'raises ArgumentError for missing postcode' do
          invalid_params = deep_clone(valid_params)
          invalid_params['supplyAddress'] = { 'thoroughfare' => '10 Downing Street' }
          expect { client.create_application(invalid_params) }
            .to raise_error(ArgumentError, /Missing required address fields: postcode/)
        end
      end

      context 'with invalid billing address configuration' do
        it 'raises ArgumentError when billing address is required but missing' do
          invalid_params = deep_clone(valid_params)
          invalid_params['isBillingAddressSameAsSupplyAddress'] = false
          expect { client.create_application(invalid_params) }
            .to raise_error(ArgumentError, /billingAddress is required/)
        end
      end

      context 'with invalid consent' do
        it 'raises ArgumentError for missing consent fields' do
          invalid_params = deep_clone(valid_params)
          invalid_params['consent'] = { 'creditCheck' => true }
          expect { client.create_application(invalid_params) }
            .to raise_error(ArgumentError, /Missing required consent fields:/)
        end
      end
    end

    context 'with electricity supply details' do
      let(:electricity_params) do
        valid_params.merge(
          'newElectricityTariff' => {
            'supplierReference' => 'SUPPLIER_REF',
            'reference' => 'TARIFF_REF',
            'name' => 'Standard Variable',
            'paymentType' => 'DIRECT_DEBIT',
            'billingMethod' => 'MONTHLY'
          },
          'electricitySupply' => {
            'mpan' => '1234567890',
            'isEconomy7' => false,
            'isSmartMeter' => true
          }
        )
      end

      before do
        stub_request(:post, "https://test-api.tomato.energy/api/v1/applications")
          .with(
            headers: {
              'Content-Type' => 'application/json',
              'x-api-key' => 'test-api-key'
            },
            body: hash_including(application: hash_including('newElectricityTariff'))
          )
          .to_return(
            status: 200,
            body: {
              application: {
                electricityReference: 'ELEC123',
                gasReference: nil
              }
            }.to_json
          )
      end

      it 'validates electricity fields successfully' do
        expect { client.create_application(electricity_params) }.not_to raise_error
      end

      it 'raises ArgumentError for missing electricity tariff fields' do
        invalid_params = deep_clone(electricity_params)
        invalid_params['newElectricityTariff'].delete('paymentType')
        expect { client.create_application(invalid_params) }
          .to raise_error(ArgumentError, /Missing required electricity tariff fields:/)
      end

      it 'raises ArgumentError for missing electricity supply fields' do
        invalid_params = deep_clone(electricity_params)
        invalid_params['electricitySupply'].delete('isEconomy7')
        expect { client.create_application(invalid_params) }
          .to raise_error(ArgumentError, /Missing required electricity supply fields:/)
      end
    end

    context 'with gas supply details' do
      let(:gas_params) do
        valid_params.merge(
          'newGasTariff' => {
            'supplierReference' => 'SUPPLIER_REF',
            'reference' => 'TARIFF_REF',
            'name' => 'Standard Variable',
            'paymentType' => 'DIRECT_DEBIT',
            'billingMethod' => 'MONTHLY'
          },
          'gasSupply' => {
            'mprn' => '1234567890'
          }
        )
      end

      before do
        stub_request(:post, "https://test-api.tomato.energy/api/v1/applications")
          .with(
            headers: {
              'Content-Type' => 'application/json',
              'x-api-key' => 'test-api-key'
            },
            body: hash_including(application: hash_including('newGasTariff'))
          )
          .to_return(
            status: 200,
            body: {
              application: {
                electricityReference: 'ELEC123',
                gasReference: 'GAS123'
              }
            }.to_json
          )
      end

      it 'validates gas fields successfully' do
        expect { client.create_application(gas_params) }.not_to raise_error
      end

      it 'raises ArgumentError for missing gas tariff fields' do
        invalid_params = deep_clone(gas_params)
        invalid_params['newGasTariff'].delete('paymentType')
        expect { client.create_application(invalid_params) }
          .to raise_error(ArgumentError, /Missing required gas tariff fields:/)
      end

      it 'raises ArgumentError for missing gas supply fields' do
        invalid_params = deep_clone(gas_params)
        invalid_params['gasSupply'].delete('mprn')
        expect { client.create_application(invalid_params) }
          .to raise_error(ArgumentError, /Missing required gas supply fields:/)
      end
    end
  end

  describe 'error handling' do
    # Define valid_params for the error handling tests
    let(:valid_params) do
      {
        'externalReference' => 'MEETGEORGE-TEST-REF-1',
        'submittedDateTime' => '2024-01-20T10:00:00Z',
        'type' => 'SWITCH',
        'sender' => { 'reference' => 'SENDER_REF' },
        'channel' => { 'reference' => 'CHANNEL_REF' },
        'customer' => {
          'title' => 'Mr',
          'firstName' => 'John',
          'lastName' => 'Doe',
          'dateOfBirth' => '1990-01-01',
          'emailAddress' => '<EMAIL>',
          'phoneNumber' => '07700900000'
        },
        'supplyAddress' => {
          'postcode' => 'SW1A 1AA',
          'thoroughfare' => '10 Downing Street'
        },
        'isBillingAddressSameAsSupplyAddress' => true,
        'consent' => {
          'creditCheck' => true,
          'coolingOffPeriodPayment' => true
        },
        'electricitySupply' => {
          'mpan' => '200000653506',
          'isEconomy7' => false,
          'isSmartMeter' => false
        },
        'newElectricityTariff' => {
          'supplierReference' => 'SUPPLIER_REF',
          'reference' => 'TARIFF_REF',
          'name' => 'Standard Variable',
          'paymentType' => 'Variable Direct Debit only',
          'billingMethod' => 'MONTHLY'
        }
      }
    end

    let(:error_params) { valid_params }

    context 'when there is a client error' do
      before do
        stub_request(:post, "https://test-api.tomato.energy/api/v1/applications")
          .to_return(status: 400, body: 'Bad Request')
      end

      it 'raises a client error' do
        expect { client.create_application(error_params) }
          .to raise_error(/Client error: 400/)
      end
    end

    context 'when there is a server error' do
      before do
        stub_request(:post, "https://test-api.tomato.energy/api/v1/applications")
          .to_return(status: 500, body: 'Internal Server Error')
      end

      it 'raises a server error' do
        expect { client.create_application(error_params) }
          .to raise_error(/Server error: 500/)
      end
    end

    context 'when there is an unknown error' do
      before do
        stub_request(:post, "https://test-api.tomato.energy/api/v1/applications")
          .to_return(status: 600, body: 'Unknown Error')
      end

      it 'raises an unknown error' do
        expect { client.create_application(error_params) }
          .to raise_error(/Unknown error: 600/)
      end
    end
  end
end
