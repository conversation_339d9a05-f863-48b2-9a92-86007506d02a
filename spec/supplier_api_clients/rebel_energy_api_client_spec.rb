require 'rails_helper'
require 'webmock/rspec'

RSpec.describe RebelEnergyApiClient do
  let(:client) { described_class.new }
  let(:base_url) { ENV['REBEL_ENERGY_API_URL'] }
  let(:token) { 'fake_token' }

  before do
    stub_request(:post, "#{base_url}/oauth/token")
      .to_return(status: 200, body: { access_token: token, expires_in: 3600 }.to_json)
  end

  describe 'authentication' do
    it 'authenticates and sets the token' do
      client.send(:authenticate)
      expect(client.instance_variable_get(:@token)).to eq(token)
    end

    it 'refreshes the token when expired' do
      client.instance_variable_set(:@token_expires_at, Time.now - 1)
      client.send(:ensure_token)
      expect(client.instance_variable_get(:@token)).to eq(token)
    end
  end

  describe 'API calls' do
    before do
      stub_request(:get, /#{base_url}.*/).to_return(status: 200, body: '{"data": "success"}')
      stub_request(:post, /#{base_url}.*/).to_return(status: 200, body: '{"data": "success"}')
    end

    context 'V2 Endpoints' do
      it 'gets tariffs' do
        expect(client.get_tariffs('A')).to eq({ 'data' => 'success' })
      end

      it 'gets MPXNs' do
        expect(client.get_mpxns({ postcode: 'SW1A 1AA' })).to eq({ 'data' => 'success' })
      end

      it 'gets addresses' do
        expect(client.get_addresses('SW1A 1AA')).to eq({ 'data' => 'success' })
      end

      it 'gets a quote' do
        expect(client.get_quote({ some: 'params' })).to eq({ 'data' => 'success' })
      end
    end

    context 'Sys Endpoints' do
      it 'gets customer by phone' do
        expect(client.get_customer_by_phone('1234567890')).to eq({ 'data' => 'success' })
      end

      it 'gets customer by custref' do
        expect(client.get_customer_by_custref('REF123')).to eq({ 'data' => 'success' })
      end

      # Add tests for other sys endpoints...
    end

    context 'Customer Endpoints' do
      it 'gets customer status' do
        expect(client.get_customer_status('EXT123')).to eq({ 'data' => 'success' })
      end

      it 'gets customer reads' do
        expect(client.get_customer_reads({ some: 'params' })).to eq({ 'data' => 'success' })
      end

      # Add tests for other customer endpoints...
    end

    context 'General Endpoints' do
      it 'gets halfhourly auth test' do
        expect(client.get_halfhourly_auth_test).to eq({ 'data' => 'success' })
      end

      it 'gets AQ' do
        expect(client.get_aq('1234567890')).to eq({ 'data' => 'success' })
      end

      # Add tests for other general endpoints...
    end
  end

  describe 'error handling' do
    context 'when authentication fails' do
      before do
        stub_request(:get, "#{base_url}/api/v2/tariffs/current")
          .to_return(status: 401, body: 'Unauthorized')
      end

      it 'raises an authentication error' do
        expect { client.get_tariffs('A') }.to raise_error('Authentication failed')
      end
    end

    context 'when there is a client error' do
      before do
        stub_request(:get, "#{base_url}/api/v2/tariffs/current")
          .to_return(status: 400, body: 'Bad Request')
      end

      it 'raises a client error' do
        expect { client.get_tariffs('A') }.to raise_error(/Client error: 400/)
      end
    end

    context 'when there is a server error' do
      before do
        stub_request(:get, "#{base_url}/api/v2/tariffs/current")
          .to_return(status: 500, body: 'Internal Server Error')
      end

      it 'raises a server error' do
        expect { client.get_tariffs('A') }.to raise_error(/Server error: 500/)
      end
    end
  end

  describe '#register_customer' do
    let(:valid_params) do
      {
        salesChannel: 'web',
        externalRef: 'REF123',
        surname: 'Doe',
        firstname: 'John',
        productCode: 'PROD1',
        supplyAddressStreetAddress: '123 Main St',
        supplyAddressPostcode: 'AB12 3CD',
        fuelType: 'dualFuel',
        paymentAmountGas: 50,
        paymentAmountElectricity: 75,
        contactMobilePhone: '07123456789'
      }
    end

    context 'with valid parameters' do
      it 'does not raise an error' do
        expect { client.send(:validate_customer_params, valid_params) }.not_to raise_error
      end
    end

    context 'with missing required fields' do
      it 'raises an ArgumentError' do
        invalid_params = valid_params.except(:salesChannel, :externalRef)
        expect { client.send(:validate_customer_params, invalid_params) }
          .to raise_error(ArgumentError, /Missing required fields: salesChannel, externalRef/)
      end
    end

    context 'with invalid fuel type' do
      it 'raises an ArgumentError' do
        invalid_params = valid_params.merge(fuelType: 'invalidFuel')
        expect { client.send(:validate_customer_params, invalid_params) }
          .to raise_error(ArgumentError, /Invalid fuelType: invalidFuel/)
      end
    end

    context 'with missing fuel-specific payment amount' do
      it 'raises an ArgumentError for gasOnly without paymentAmountGas' do
        invalid_params = valid_params.merge(fuelType: 'gasOnly').except(:paymentAmountGas, :paymentAmountElectricity)
        expect { client.send(:validate_customer_params, invalid_params) }
          .to raise_error(ArgumentError, /Missing paymentAmountGas for gasOnly or dualFuel/)
      end

      it 'raises an ArgumentError for electricityOnly without paymentAmountElectricity' do
        invalid_params = valid_params.merge(fuelType: 'electricityOnly').except(:paymentAmountGas, :paymentAmountElectricity)
        expect { client.send(:validate_customer_params, invalid_params) }
          .to raise_error(ArgumentError, /Missing paymentAmountElectricity for electricityOnly or dualFuel/)
      end
    end

    context 'with missing contact phone' do
      it 'raises an ArgumentError when both contact phones are missing' do
        invalid_params = valid_params.except(:contactMobilePhone)
        expect { client.send(:validate_customer_params, invalid_params) }
          .to raise_error(ArgumentError, /Either contactMobilePhone or contactSecondaryPhone must be provided/)
      end

      it 'does not raise an error when contactSecondaryPhone is provided' do
        valid_params_with_secondary = valid_params.except(:contactMobilePhone).merge(contactSecondaryPhone: '01234567890')
        expect { client.send(:validate_customer_params, valid_params_with_secondary) }.not_to raise_error
      end
    end
  end
end
