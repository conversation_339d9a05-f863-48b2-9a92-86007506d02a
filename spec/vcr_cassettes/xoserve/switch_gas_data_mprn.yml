---
http_interactions:
- request:
    method: post
    uri: https://login.microsoftonline.com/correlab2c.onmicrosoft.com/oauth2/v2.0/token
    body:
      encoding: UTF-8
      string: client_id=<CLIENT_ID>&client_secret=<CLIENT_SECRET>&grant_type=client_credentials&scope=https%3A%2F%2Fcorrelab2c.onmicrosoft.com%2Fa330c648-d145-4783-a331-2e3cf5611218%2F.default
    headers:
      User-Agent:
      - Faraday v2.11.0
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-store, no-cache
      Pragma:
      - no-cache
      Content-Type:
      - application/json; charset=utf-8
      Expires:
      - "-1"
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Content-Type-Options:
      - nosniff
      P3p:
      - CP="DSP CUR OTPi IND OTRi ONL FIN"
      X-Ms-Request-Id:
      - 204e655e-2fe4-4b53-bfd8-f8db3bad1200
      X-Ms-Ests-Server:
      - 2.1.20139.6 - SEC ProdSlices
      X-Ms-Srs:
      - 1.P
      Content-Security-Policy-Report-Only:
      - object-src 'none'; base-uri 'self'; script-src 'self' 'nonce-b3HlM7Uy1Sj4mEnSM43lVA'
        'unsafe-inline' 'unsafe-eval' https://*.msauth.net https://*.msftauth.net
        https://*.msftauthimages.net https://*.msauthimages.net https://*.msidentity.com
        https://*.microsoftonline-p.com https://*.microsoftazuread-sso.com https://*.azureedge.net
        https://*.outlook.com https://*.office.com https://*.office365.com https://*.microsoft.com
        https://*.bing.com 'report-sample'; report-uri https://csp.microsoft.com/report/ESTS-UX-All
      X-Xss-Protection:
      - '0'
      Set-Cookie:
      - fpc=AplJBq4LYZFBoeXSctWoUMn6JxYDAQAAAAJ7X98OAAAA; expires=Tue, 08-Apr-2025
        12:05:22 GMT; path=/; secure; HttpOnly; SameSite=None
      - stsservicecookie=estsfd; path=/; secure; samesite=none; httponly
      - x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly
      Date:
      - Sun, 09 Mar 2025 12:05:22 GMT
      Content-Length:
      - '1424'
    body:
      encoding: UTF-8
      string: '{"token_type":"Bearer","expires_in":3599,"ext_expires_in":3599,"access_token":"<ACCESS_TOKEN>"}'
  recorded_at: Sun, 09 Mar 2025 12:05:22 GMT
- request:
    method: get
    uri: https://desapi-css-prod.xoserveapis.com/query/v1/switch?mprn=1234567890
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Meet George Ltd APP1
      X-Api-Key:
      - "<SUBSCRIPTION_KEY>"
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Content-Length:
      - '167'
      Content-Type:
      - application/json; charset=utf-8
      Strict-Transport-Security:
      - max-age=2592000
      Request-Context:
      - appId=cid-v1:02fb6af1-33c0-485c-a9c3-38e18082a73d
      Api-Supported-Versions:
      - '1'
      X-Powered-By:
      - ASP.NET
      Date:
      - Sun, 09 Mar 2025 12:05:23 GMT
    body:
      encoding: UTF-8
      string: '{"fault":[{"faultString":"Request cannot be processed because Mandatory
        parameters are missing","detail":{"errorCode":"XSCH6001","item":"MeterPointReferenceNumber"}}]}'
  recorded_at: Sun, 09 Mar 2025 12:05:24 GMT
- request:
    method: get
    uri: https://desapi-css-prod.xoserveapis.com/query/v1/switch?mprn=9999999999
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Meet George Ltd APP1
      X-Api-Key:
      - "<SUBSCRIPTION_KEY>"
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 401
      message: Unauthorized
    headers:
      Content-Length:
      - '76'
      Content-Type:
      - application/json
      Request-Context:
      - appId=cid-v1:02fb6af1-33c0-485c-a9c3-38e18082a73d
      Date:
      - Sun, 09 Mar 2025 12:18:13 GMT
    body:
      encoding: UTF-8
      string: '{ "statusCode": 401, "message": "Unauthorized. Sorry, I can''t let
        you in." }'
  recorded_at: Sun, 09 Mar 2025 12:18:13 GMT
recorded_with: VCR 6.2.0
