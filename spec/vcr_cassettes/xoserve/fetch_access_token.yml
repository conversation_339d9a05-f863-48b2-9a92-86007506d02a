---
http_interactions:
- request:
    method: post
    uri: https://login.microsoftonline.com/correlab2c.onmicrosoft.com/oauth2/v2.0/token
    body:
      encoding: UTF-8
      string: client_id=<CLIENT_ID>&client_secret=<CLIENT_SECRET>&grant_type=client_credentials&scope=https%3A%2F%2Fcorrelab2c.onmicrosoft.com%2Fa330c648-d145-4783-a331-2e3cf5611218%2F.default
    headers:
      User-Agent:
      - Faraday v2.11.0
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-store, no-cache
      Pragma:
      - no-cache
      Content-Type:
      - application/json; charset=utf-8
      Expires:
      - "-1"
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Content-Type-Options:
      - nosniff
      P3p:
      - CP="DSP CUR OTPi IND OTRi ONL FIN"
      X-Ms-Request-Id:
      - 6d218c17-ce18-4ac8-b90d-1f6503380d00
      X-Ms-Ests-Server:
      - 2.1.20203.5 - WEULR1 ProdSlices
      X-Ms-Srs:
      - 1.P
      Content-Security-Policy-Report-Only:
      - object-src 'none'; base-uri 'self'; script-src 'self' 'nonce-mgYaIR1deXJiBePlFre3jw'
        'unsafe-inline' 'unsafe-eval' https://*.msauth.net https://*.msftauth.net
        https://*.msftauthimages.net https://*.msauthimages.net https://*.msidentity.com
        https://*.microsoftonline-p.com https://*.microsoftazuread-sso.com https://*.azureedge.net
        https://*.outlook.com https://*.office.com https://*.office365.com https://*.microsoft.com
        https://*.bing.com 'report-sample'; report-uri https://csp.microsoft.com/report/ESTS-UX-All
      X-Xss-Protection:
      - '0'
      Set-Cookie:
      - fpc=AnTAoJW7L2ZOh-tL3fo42-_6JxYDAQAAAKV9X98OAAAA; expires=Tue, 08-Apr-2025
        12:16:38 GMT; path=/; secure; HttpOnly; SameSite=None
      - stsservicecookie=estsfd; path=/; secure; samesite=none; httponly
      - x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly
      Date:
      - Sun, 09 Mar 2025 12:16:38 GMT
      Content-Length:
      - '1424'
    body:
      encoding: UTF-8
      string: '{"token_type":"Bearer","expires_in":3599,"ext_expires_in":3599,"access_token":"<ACCESS_TOKEN>"}'
  recorded_at: Sun, 09 Mar 2025 12:16:38 GMT
recorded_with: VCR 6.2.0
