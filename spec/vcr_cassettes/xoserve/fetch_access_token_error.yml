---
http_interactions:
- request:
    method: post
    uri: https://login.microsoftonline.com/correlab2c.onmicrosoft.com/oauth2/v2.0/token
    body:
      encoding: UTF-8
      string: client_id=<CLIENT_ID>&client_secret=<CLIENT_SECRET>&grant_type=client_credentials&scope=https%3A%2F%2Fcorrelab2c.onmicrosoft.com%2Fa330c648-d145-4783-a331-2e3cf5611218%2F.default
    headers:
      User-Agent:
      - Faraday v2.11.0
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 401
      message: Unauthorized
    headers:
      Cache-Control:
      - no-store, no-cache
      Pragma:
      - no-cache
      Content-Type:
      - application/json; charset=utf-8
      Expires:
      - "-1"
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Content-Type-Options:
      - nosniff
      P3p:
      - CP="DSP CUR OTPi IND OTRi ONL FIN"
      X-Ms-Request-Id:
      - 190eaab4-3d5f-4e2f-a5eb-7bcfc0e00000
      X-Ms-Ests-Server:
      - 2.1.20139.6 - NEULR1 ProdSlices
      X-Ms-Srs:
      - 1.P
      Content-Security-Policy-Report-Only:
      - object-src 'none'; base-uri 'self'; script-src 'self' 'nonce-lGRg0ghzEX2hx6ASm-ZdUg'
        'unsafe-inline' 'unsafe-eval' https://*.msauth.net https://*.msftauth.net
        https://*.msftauthimages.net https://*.msauthimages.net https://*.msidentity.com
        https://*.microsoftonline-p.com https://*.microsoftazuread-sso.com https://*.azureedge.net
        https://*.outlook.com https://*.office.com https://*.office365.com https://*.microsoft.com
        https://*.bing.com 'report-sample'; report-uri https://csp.microsoft.com/report/ESTS-UX-All
      X-Xss-Protection:
      - '0'
      Set-Cookie:
      - fpc=AnHwPBLqmSxLhHYfQGf6rAT6JxYDAQAAAPo5X98OAAAA; expires=Tue, 08-Apr-2025
        07:27:55 GMT; path=/; secure; HttpOnly; SameSite=None
      - stsservicecookie=estsfd; path=/; secure; samesite=none; httponly
      - x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly
      Date:
      - Sun, 09 Mar 2025 07:27:55 GMT
      Content-Length:
      - '623'
    body:
      encoding: UTF-8
      string: '{"error":"invalid_client","error_description":"AADSTS7000215: Invalid
        client secret provided. Ensure the secret being sent in the request is the
        client secret value, not the client secret ID, for a secret added to app ''<CLIENT_ID>''.
        Trace ID: 190eaab4-3d5f-4e2f-a5eb-7bcfc0e00000 Correlation ID: b8dc8e2f-87c6-4e24-9a13-f43114d0e39d
        Timestamp: 2025-03-09 07:27:55Z","error_codes":[7000215],"timestamp":"2025-03-09
        07:27:55Z","trace_id":"190eaab4-3d5f-4e2f-a5eb-7bcfc0e00000","correlation_id":"b8dc8e2f-87c6-4e24-9a13-f43114d0e39d","error_uri":"https://login.microsoftonline.com/error?code=7000215"}'
  recorded_at: Sun, 09 Mar 2025 07:27:55 GMT
recorded_with: VCR 6.2.0
