---
http_interactions:
- request:
    method: post
    uri: https://login.microsoftonline.com/correlab2c.onmicrosoft.com/oauth2/v2.0/token
    body:
      encoding: UTF-8
      string: client_id=<CLIENT_ID>&client_secret=<CLIENT_SECRET>&grant_type=client_credentials&scope=https%3A%2F%2Fcorrelab2c.onmicrosoft.com%2Fa330c648-d145-4783-a331-2e3cf5611218%2F.default
    headers:
      User-Agent:
      - Faraday v2.11.0
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-store, no-cache
      Pragma:
      - no-cache
      Content-Type:
      - application/json; charset=utf-8
      Expires:
      - "-1"
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Content-Type-Options:
      - nosniff
      P3p:
      - CP="DSP CUR OTPi IND OTRi ONL FIN"
      X-Ms-Request-Id:
      - bb1ceac5-5f50-4ae3-8524-8b50482b0500
      X-Ms-Ests-Server:
      - 2.1.20203.5 - WEULR1 ProdSlices
      X-Ms-Srs:
      - 1.P
      Content-Security-Policy-Report-Only:
      - object-src 'none'; base-uri 'self'; script-src 'self' 'nonce-lli8-8AFNGTWQtmTdTr5uw'
        'unsafe-inline' 'unsafe-eval' https://*.msauth.net https://*.msftauth.net
        https://*.msftauthimages.net https://*.msauthimages.net https://*.msidentity.com
        https://*.microsoftonline-p.com https://*.microsoftazuread-sso.com https://*.azureedge.net
        https://*.outlook.com https://*.office.com https://*.office365.com https://*.microsoft.com
        https://*.bing.com 'report-sample'; report-uri https://csp.microsoft.com/report/ESTS-UX-All
      X-Xss-Protection:
      - '0'
      Set-Cookie:
      - fpc=AuH2gWBN24ZGiDAE9JAH_UP6JxYDAQAAAKp9X98OAAAA; expires=Tue, 08-Apr-2025
        12:16:42 GMT; path=/; secure; HttpOnly; SameSite=None
      - stsservicecookie=estsfd; path=/; secure; samesite=none; httponly
      - x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly
      Date:
      - Sun, 09 Mar 2025 12:16:42 GMT
      Content-Length:
      - '1424'
    body:
      encoding: UTF-8
      string: '{"token_type":"Bearer","expires_in":3599,"ext_expires_in":3599,"access_token":"<ACCESS_TOKEN>"}'
  recorded_at: Sun, 09 Mar 2025 12:16:42 GMT
- request:
    method: get
    uri: https://desapi-css-prod.xoserveapis.com/query/v1/supply-point-address?postcode=SN12%206GB
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Meet George Ltd APP1
      X-Api-Key:
      - "<SUBSCRIPTION_KEY>"
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 401
      message: Access Denied
    headers:
      Content-Length:
      - '143'
      Content-Type:
      - application/json
      Request-Context:
      - appId=cid-v1:02fb6af1-33c0-485c-a9c3-38e18082a73d
      Www-Authenticate:
      - AzureApiManagementKey realm="https://desapi-css-prod.xoserveapis.com/query/v1/supply-point-address",name="x-api-key",type="header"
      Date:
      - Sun, 09 Mar 2025 12:16:43 GMT
    body:
      encoding: UTF-8
      string: '{ "statusCode": 401, "message": "Access denied due to invalid subscription
        key. Make sure to provide a valid key for an active subscription." }'
  recorded_at: Sun, 09 Mar 2025 12:16:43 GMT
- request:
    method: get
    uri: https://desapi-css-prod.xoserveapis.com/query/v1/supply-point-address?postcode=SW1A%201AA
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Meet George Ltd APP1
      X-Api-Key:
      - "<SUBSCRIPTION_KEY>"
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 401
      message: Unauthorized
    headers:
      Content-Length:
      - '76'
      Content-Type:
      - application/json
      Request-Context:
      - appId=cid-v1:02fb6af1-33c0-485c-a9c3-38e18082a73d
      Date:
      - Sun, 09 Mar 2025 12:18:14 GMT
    body:
      encoding: UTF-8
      string: '{ "statusCode": 401, "message": "Unauthorized. Sorry, I can''t let
        you in." }'
  recorded_at: Sun, 09 Mar 2025 12:18:14 GMT
recorded_with: VCR 6.2.0
