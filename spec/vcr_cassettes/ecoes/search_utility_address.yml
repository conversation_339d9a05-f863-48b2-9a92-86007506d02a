---
http_interactions:
- request:
    method: post
    uri: https://uit.ecoes.co.uk/WebServices/Service/ECOESAPI.svc/RESTful/JSON/SearchUtilityAddress
    body:
      encoding: UTF-8
      string: '{"Authentication":{"Key":"<ECOES_SUBSCRIPTION_KEY>"},"ParameterSets":[{"Parameters":[{"Key":"MeterSerialNumber","Value":"MSN12345"}]}]}'
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
      Host:
      - uit.ecoes.co.uk
      Content-Type:
      - application/json
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Type:
      - application/xml; charset=utf-8
      Referrer-Policy:
      - strict-origin-when-cross-origin
      X-Content-Type-Option:
      - nosniff
      X-Content-Type-Options:
      - nosniff
      X-Frame-Option:
      - sameorigin
      X-Frame-Options:
      - sameorigin
      X-Permitted-Cross-Domain-Policies:
      - none
      X-Xss-Protection:
      - 1; mode=block
      Date:
      - Sun, 09 Mar 2025 12:30:40 GMT
      Content-Length:
      - '638'
      X-Lb-Server:
      - 745ad2cb
      Strict-Transport-Security:
      - max-age=15552000
      Set-Cookie:
      - X-LB-707010b8=!106am7RCY1S1ySxEQG0PCFE6ajnF7qhSXP5gMvUOiYcgeaJ1fJyTSG5fankOm2eXepvY0zGhDM/Jeik=;
        path=/; Httponly; Secure; SameSite=Lax
    body:
      encoding: UTF-8
      string: <Fault xmlns="http://schemas.microsoft.com/ws/2005/05/envelope/none"><Code><Value>Sender</Value></Code><Reason><Text
        xml:lang="en-GB">Subscriber licence key is not valid</Text></Reason><Detail><SubscriptionLicenceKeyNotValidFault
        xmlns="http://schemas.datacontract.org/2004/07/CandC.WSSUDS.SecurityModel.Faults"
        xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ErrorCode>VAL1000</ErrorCode><ErrorText>Subscriber
        licence key is not valid</ErrorText><Variables xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:string><ECOES_SUBSCRIPTION_KEY></a:string></Variables></SubscriptionLicenceKeyNotValidFault></Detail></Fault>
  recorded_at: Sun, 09 Mar 2025 12:30:40 GMT
- request:
    method: post
    uri: https://www.ecoes.co.uk/WebServices/Service/ECOESAPI.svc/RESTful/JSON/SearchUtilityAddress
    body:
      encoding: UTF-8
      string: '{"Authentication":{"Key":"<ECOES_SUBSCRIPTION_KEY>"},"ParameterSets":[{"Parameters":[{"Key":"MeterSerialNumber","Value":"MSN12345"}]}]}'
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
      Host:
      - www.ecoes.co.uk
      Content-Type:
      - application/json
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Type:
      - application/json; charset=utf-8
      Referrer-Policy:
      - strict-origin-when-cross-origin
      X-Content-Type-Option:
      - nosniff
      X-Content-Type-Options:
      - nosniff
      X-Frame-Option:
      - sameorigin
      X-Frame-Options:
      - sameorigin
      X-Permitted-Cross-Domain-Policies:
      - none
      X-Xss-Protection:
      - 1; mode=block
      Date:
      - Sun, 09 Mar 2025 12:34:39 GMT
      Content-Length:
      - '347'
      X-Lb-Server:
      - 5ddd65b5
      Strict-Transport-Security:
      - max-age=15552000
      Set-Cookie:
      - X-LB-e8e25693=!I+0GzRaW9obaIrUJkKSDCT+OEYnZoTCPvmLIfICNpf0CywgVd3Y4U259nC2Yyk2DadzqEHK84/DPzFI=;
        path=/; Httponly; Secure
    body:
      encoding: UTF-8
      string: '{"Header":{"RequestDate":"20250309 12:34:39.PM","RequestId":802204381,"ResponseTime":"78.0691","VersionNumber":"*******;Instance=LIVE"},"Results":[{"Errors":[{"Code":"DAT2010","Description":"No
        address found for the specified criteria."}],"ParameterSet":{"Parameters":[{"Key":"MeterSerialNumber","Value":"MSN12345"}]},"UtilityAddressMatches":[]}]}'
  recorded_at: Sun, 09 Mar 2025 12:34:39 GMT
recorded_with: VCR 6.2.0
