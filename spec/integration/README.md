# Integration Tests

This directory contains integration tests that make real API calls to external services. These tests are disabled by default and will be skipped unless explicitly enabled.

## Running Integration Tests

To run integration tests, you need to:

1. Set up the required environment variables for the services being tested
2. Run the tests with the `RUN_INTEGRATION_TESTS=true` flag

### Using Helper Scripts

We provide helper scripts to run integration tests more easily:

```bash
# Run Tulo integration tests with your API key
TULO_ENERGY_API_KEY=your-api-key ./bin/run_tulo_integration_tests.sh
```

### Manual Execution

You can also run the tests manually:

```bash
# For Tulo API tests, set required environment variables
export TULO_ENERGY_API_URL=https://test-api.tulo.energy
export TULO_ENERGY_API_KEY=your-api-key

# Run all integration tests
RUN_INTEGRATION_TESTS=true bundle exec rspec spec/integration

# Run specific integration tests
RUN_INTEGRATION_TESTS=true bundle exec rspec spec/integration/supplier_api_clients/tulo_api_client_integration_spec.rb
```

## Integration vs Unit Tests

- **Unit tests** (in the main `spec/` directory) use stubbed API calls and don't require real credentials
- **Integration tests** (in this directory) make real API calls and require actual credentials

## Adding New Integration Tests

When adding integration tests for new external services:

1. Create a new file in an appropriate subdirectory of `spec/integration/`
2. Use the `skip` guard pattern to prevent tests from running without the flag:

```ruby
before(:each) do
  skip "Skipping integration tests. Run with RUN_INTEGRATION_TESTS=true" unless ENV['RUN_INTEGRATION_TESTS'] == 'true'
end
```

3. Document the required environment variables at the top of the test file
4. Consider using VCR for recording and replaying API interactions when appropriate
5. Create a helper script in the `bin` directory to make running the tests easier 