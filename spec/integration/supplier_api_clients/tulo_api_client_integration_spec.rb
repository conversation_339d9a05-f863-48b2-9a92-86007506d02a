require 'spec_helper'
require_relative '../../../lib/api_clients/tulo_api_client'
require 'webmock/rspec'

# These tests make real API calls to the Tulo test environment
# Required environment variables in .env.test:
# - TULO_ENERGY_API_URL: The URL of the Tulo test API
# - TULO_ENERGY_API_KEY: Valid API key for the Tulo test environment
#
# Run with: RUN_INTEGRATION_TESTS=true bundle exec rspec spec/integration
RSpec.describe TuloApiClient, :integration do
  # Check for required environment variables
  before(:all) do
    @env_missing = []
    @env_missing << 'TULO_ENERGY_API_URL' if ENV['TULO_ENERGY_API_URL'].nil? || ENV['TULO_ENERGY_API_URL'].empty?
    @env_missing << 'TULO_ENERGY_API_KEY' if ENV['TULO_ENERGY_API_KEY'].nil? || ENV['TULO_ENERGY_API_KEY'].empty?
  end

  # Skip tests if not running integration tests or if environment variables are missing
  before(:each) do
    skip "Skipping integration tests. Run with RUN_INTEGRATION_TESTS=true" unless ENV['RUN_INTEGRATION_TESTS'] == 'true'
    
    unless @env_missing.empty?
      skip "Missing required environment variables in .env.test: #{@env_missing.join(', ')}"
    end
    
    # Allow real HTTP connections by disabling WebMock and VCR
    WebMock.disable!
    VCR.turn_off! if defined?(VCR)
    
    puts "WebMock and VCR disabled for integration tests"
  end
  
  after(:each) do
    # Re-enable WebMock and VCR after tests
    WebMock.enable!
    VCR.turn_on! if defined?(VCR)
    
    puts "WebMock and VCR re-enabled after integration tests"
  end

  # Initialize client directly with environment variables from .env.test
  let(:client) { TuloApiClient.new }
  
  # Print API base URL (for troubleshooting)
  before(:each) do
    puts "Integration test using Tulo API URL: #{ENV['TULO_ENERGY_API_URL']}"
  end
  
  # Test data for making a sale - based on scripts/test_tulo.rb
  let(:valid_params) do
    {
      "SaleMadeBy" => "MeetGeorgeTest",
      "SaleMadeOn" => Time.now.strftime("%Y-%m-%dT%H:%M:%S"),
      "SaleValidatedBy" => "Integration Test",
      "SaleValidatedOn" => Time.now.strftime("%Y-%m-%dT%H:%M:%S.%LZ"),
      "SaleValidationStatus" => "V",
      "SaleChannel" => "IntegrationTest",
      "SaleType" => "CoS",
      "CustomerReference" => "IntTest#{Time.now.to_i}", # Unique reference
      "SaleCustomerType" => 1,
      "CustomerTitle" => "Mr",
      "CustomerFirstName" => "IntTest",
      "CustomerLastName" => "User",
      "CustomerAddressLine1" => "74",
      "CustomerAddressLine2" => "LEYBOURNE ROAD",
      "CustomerTownCity" => "UXBRIDGE",
      "CustomerCounty" => nil,
      "CustomerPostcode" => "UB10 9HF",
      "CustomerEmail" => "<EMAIL>",
      "CustomerPhoneNumber" => "07501454545",
      "CustomerDateOfBirth" => "1974-07-13T00:00:00",
      "PaymentMethod" => "Direct Debit",
      "ElectricEAC" => 3200,
      "GasEAC" => 7407,
      "ElectricityTariffName" => "Tulo Vari-One",
      "GasTariffName" => "Tulo Vari-One",
      "MPANs" => [{
        "MPAN" => "*************",
        "UseIndustryDataLookup" => true,
      }],
      "MPRNs" => [{
        "MPRN" => "**********",
        "UseIndustryDataLookup" => true
      }],
      "DDGasMonthlyAmount" => 47,
      "DDGasPaymentOn" => 1,
      "DDElectricMonthlyAmount" => 74,
      "DDElectricPaymentOn" => 1,
      "BankAccountName" => "Test Account Name",
      "BankAccountNumber" => "********",
      "BankSortCode" => "000000"
    }
  end

  describe '#sale_made' do
    it 'can make a successful sale request' do
      puts "Submitting sale with customer reference: #{valid_params['CustomerReference']}"
      
      # This will use actual API credentials and make a real API call
      response = client.sale_made(valid_params)
      
      # Log the response
      puts "Received response: #{response.inspect}"
      
      # Verify response has expected structure (note case sensitivity!)
      expect(response).to be_a(Hash)
      
      # Check for customerQuoteId (lowercase) as that's what the API returns
      expect(response).to have_key('customerQuoteId')
      expect(response['customerQuoteId']).to be_a(Numeric)
      
      # Check for success key (lowercase)
      expect(response).to have_key('success')
      expect(response['success']).to be true
      
      puts "Successfully created customer quote with ID: #{response['customerQuoteId']}"
    end
    
    context 'with invalid data' do
      # This test will be marked as pending because we don't want
      # to deliberately make bad API calls in integration tests
      # which might affect rate limits or trigger alerts
      # This is better tested in unit tests
      it 'handles error responses appropriately' do
        # Set as skipped instead of pending since we're not actually testing this
        skip "Not testing error cases in integration environment"
        
        invalid_data = valid_params.dup
        invalid_data.delete('CustomerFirstName') # Remove required field
        
        expect { client.sale_made(invalid_data) }.to raise_error(/Missing required fields/)
      end
    end
  end
end 