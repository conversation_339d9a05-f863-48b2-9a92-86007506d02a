require 'spec_helper'
require_relative '../../../lib/api_clients/tomato_energy_api_client'
require 'webmock/rspec'
require 'securerandom'

# These tests make real API calls to the Tomato Energy test environment
# Required environment variables in .env.test:
# - TOMATO_ENERGY_API_URL: The URL of the Tomato Energy test API
# - TOMATO_ENERGY_API_KEY: Valid API key for the Tomato Energy test environment
#
# Run with:
# 1. Real API testing (requires valid API credentials and MPAN/MPRN values):
#    RUN_INTEGRATION_TESTS=true bundle exec rspec spec/integration/supplier_api_clients/tomato_energy_api_client_integration_spec.rb
#
# 2. Mocked API responses (good for CI/CD pipelines):
#    MOCK_TOMATO_API=true RUN_INTEGRATION_TESTS=true bundle exec rspec spec/integration/supplier_api_clients/tomato_energy_api_client_integration_spec.rb
#
# 3. Debug a specific test:
#    MOCK_TOMATO_API=true RUN_INTEGRATION_TESTS=true bundle exec rspec spec/integration/supplier_api_clients/tomato_energy_api_client_integration_spec.rb -e "can successfully create an application"
RSpec.describe TomatoEnergyApiClient, :integration do
  # Helper method for deep cloning objects
  def deep_clone(obj)
    return obj.clone if obj.is_a?(String) || obj.is_a?(Numeric) || [true, false, nil].include?(obj)
    return obj.map { |item| deep_clone(item) } if obj.is_a?(Array)
    return obj.keys.each_with_object({}) { |key, result| result[key] = deep_clone(obj[key]) } if obj.is_a?(Hash)
    obj.clone
  end
  
  # Manually load environment variables from .env.test
  before(:all) do
    # Load environment variables from .env.test
    env_file = File.join(File.dirname(__FILE__), '../../../.env.test')
    if File.exist?(env_file)
      File.foreach(env_file) do |line|
        next if line.strip.empty? || line.start_with?('#')
        
        key, value = line.strip.split('=', 2)
        ENV[key] = value.gsub(/\A["']|["']\Z/, '') if value
      end
      
      puts "Loaded environment variables from .env.test"
    else
      puts "Warning: .env.test file not found at #{env_file}"
    end
    
    # Debug: print environment variables
    puts "Environment variables:"
    puts "TOMATO_ENERGY_API_URL: #{ENV['TOMATO_ENERGY_API_URL'].inspect}"
    puts "TOMATO_ENERGY_API_KEY: #{ENV['TOMATO_ENERGY_API_KEY'].inspect}"
    
    @env_missing = []
    @env_missing << 'TOMATO_ENERGY_API_URL' if ENV['TOMATO_ENERGY_API_URL'].nil? || ENV['TOMATO_ENERGY_API_URL'].empty?
    @env_missing << 'TOMATO_ENERGY_API_KEY' if ENV['TOMATO_ENERGY_API_KEY'].nil? || ENV['TOMATO_ENERGY_API_KEY'].empty?
    
    puts "Missing environment variables: #{@env_missing.inspect}"
  end

  # Skip tests if not running integration tests or if environment variables are missing
  before(:each) do
    skip "Skipping integration tests. Run with RUN_INTEGRATION_TESTS=true" unless ENV['RUN_INTEGRATION_TESTS'] == 'true'
    
    unless @env_missing.empty?
      skip "Missing required environment variables in .env.test: #{@env_missing.join(', ')}"
    end
  end

  # Initialize client directly with environment variables from .env.test
  let(:client) { TomatoEnergyApiClient.new }
  
  # Print API base URL (for troubleshooting)
  before(:each) do
    puts "Integration test using Tomato Energy API URL: #{ENV['TOMATO_ENERGY_API_URL']}"
  end

  # Generate a unique external reference for each test
  let(:unique_reference) { "MG-TEST-#{SecureRandom.uuid}" }
  
  # Use a valid MPAN format - using example values that hopefully won't conflict
  let(:test_mpan) { "1200012345678" }
  
  # Test data for creating an application
  let(:valid_params) do
    {
      'externalReference' => unique_reference,
      'submittedDateTime' => Time.now.strftime('%Y-%m-%dT%H:%M:%SZ'),
      'type' => 'NEW_CONNECTION', # Changed from SWITCH to NEW_CONNECTION based on the error
      'sender' => { 'reference' => 'meetgeorge' },
      'channel' => { 'reference' => 'Internet' },
      'customer' => {
        'title' => 'Mr',
        'firstName' => 'Integration',
        'lastName' => 'Test',
        'dateOfBirth' => '1990-01-01',
        'emailAddress' => '<EMAIL>',
        'phoneNumber' => '07700900000'
      },
      'supplyAddress' => {
        'postcode' => 'SN12 6GB',
        'thoroughfare' => '28 PRIMROSE DRIVE, MELKSHAM'
      },
      'isBillingAddressSameAsSupplyAddress' => true,
      'consent' => {
        'creditCheck' => true,
        'coolingOffPeriodPayment' => true
      },
      'electricitySupply' => {
        'mpan' => test_mpan, # Using a potentially valid MPAN format
        'isEconomy7' => true,
        'isSmartMeter' => true
      },
      'currentElectricityTariff' => {
        'supplierName' => 'Outfox The Market',
        'name' => 'Standard Dual',
        'paymentType' => 'Monthly Direct Debit'
      },
      'newElectricityTariff' => {
        'supplierReference' => 'Tomato.energy',
        'reference' => '**********',
        'name' => 'Prime',
        'paymentType' => 'Monthly Direct Debit',
        'billingMethod' => 'eBilling'
      },
      'directDebit' => {
        'bankAccountSortCode' => '042909',
        'bankAccountNumber' => '********',
        'bankAccountName' => 'Integration Test',
        'paymentDayOfMonth' => 1,
        'regularPaymentAmount' => 80.65
      }
    }
  end

  describe '#create_application', :vcr do
    it 'can successfully create an application', vcr: { cassette_name: "tomato_energy/create_application_#{Time.now.to_i}", record: :new_episodes } do
      puts "Submitting application with external reference: #{valid_params['externalReference']}"
      puts "Using MPAN: #{valid_params['electricitySupply']['mpan']}"
      
      # Mock a successful response if we're in the testing environment
      if ENV['MOCK_TOMATO_API'] == 'true'
        # Use a simple WebMock to stub the API call
        stub_request(:post, ENV['TOMATO_ENERGY_API_URL'])
          .to_return(
            status: 200,
            body: {
              application: {
                electricityReference: "ELEC-#{SecureRandom.hex(4)}",
                gasReference: nil
              }
            }.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )
        puts "Using mocked API response for the test"
      end
      
      # This will use actual API credentials or the mock
      response = client.create_application(valid_params)
      
      # Log the response
      puts "Received response: #{response.inspect}"
      
      # Verify response has expected structure
      expect(response).to be_a(Hash)
      expect(response).to have_key('application')
      expect(response['application']).to have_key('electricityReference')
      
      # Store the reference for potential follow-up tests
      @electricity_reference = response['application']['electricityReference']
      
      puts "Successfully created application with electricity reference: #{@electricity_reference}"
    end
    
    context 'with gas supply' do
      it 'can create a dual fuel application', vcr: { cassette_name: "tomato_energy/create_dual_fuel_application_#{Time.now.to_i}", record: :new_episodes } do
        # Use valid MPAN and MPRN formats
        dual_fuel_mpan = "1200012345679" # Slightly different from the electricity-only test
        dual_fuel_mprn = "3456789012"   # Example MPRN format
        
        # Add gas supply details to the params
        dual_fuel_params = valid_params.merge({
          'gasSupply' => {
            'mprn' => dual_fuel_mprn
          },
          'currentGasTariff' => {
            'supplierName' => 'Outfox The Market',
            'name' => 'Standard Dual',
            'paymentType' => 'Monthly Direct Debit'
          },
          'newGasTariff' => {
            'supplierReference' => 'Tomato.energy',
            'reference' => '**********',
            'name' => 'Prime',
            'paymentType' => 'Monthly Direct Debit',
            'billingMethod' => 'eBilling'
          }
        })
        
        # Update the MPAN to be unique as well
        dual_fuel_params['electricitySupply']['mpan'] = dual_fuel_mpan
        
        # Give it a unique reference
        dual_fuel_params['externalReference'] = "MG-DUAL-#{SecureRandom.uuid}"
        
        puts "Submitting dual fuel application with external reference: #{dual_fuel_params['externalReference']}"
        puts "Using MPAN: #{dual_fuel_params['electricitySupply']['mpan']}"
        puts "Using MPRN: #{dual_fuel_params['gasSupply']['mprn']}"
        
        # Mock a successful response if we're in the testing environment
        if ENV['MOCK_TOMATO_API'] == 'true'
          # Use a simple WebMock to stub the API call
          stub_request(:post, ENV['TOMATO_ENERGY_API_URL'])
            .to_return(
              status: 200,
              body: {
                application: {
                  electricityReference: "ELEC-#{SecureRandom.hex(4)}",
                  gasReference: "GAS-#{SecureRandom.hex(4)}"
                }
              }.to_json,
              headers: { 'Content-Type' => 'application/json' }
            )
          puts "Using mocked API response for the dual fuel test"
        end
        
        response = client.create_application(dual_fuel_params)
        
        puts "Received response: #{response.inspect}"
        
        expect(response).to be_a(Hash)
        expect(response).to have_key('application')
        expect(response['application']).to have_key('electricityReference')
        expect(response['application']).to have_key('gasReference')
        
        puts "Successfully created dual fuel application with references: " \
             "Electricity: #{response['application']['electricityReference']}, " \
             "Gas: #{response['application']['gasReference']}"
      end
    end
    
    context 'with validation errors' do
      # This test will use local validation and won't make an API call
      it 'validates required fields before making API call' do
        invalid_params = valid_params.dup
        invalid_params.delete('externalReference')
        
        expect { client.create_application(invalid_params) }
          .to raise_error(ArgumentError, /Missing required fields: externalReference/)
      end
      
      # Record an intentional API error for testing error handling
      it 'handles API error responses appropriately', vcr: { cassette_name: "tomato_energy/error_response_#{Time.now.to_i}", record: :new_episodes } do
        # Create params with a known issue (invalid MPAN format)
        error_params = deep_clone(valid_params)
        error_params['electricitySupply']['mpan'] = '999999999' # Invalid MPAN format (too short)
        
        # Mock the error response
        if ENV['MOCK_TOMATO_API'] == 'true'
          stub_request(:post, ENV['TOMATO_ENERGY_API_URL'])
            .to_return(
              status: 400,
              body: {
                error: {
                  message: "Validation failed",
                  details: {
                    validationFailures: [
                      {
                        reference: "InvalidMpan",
                        message: "MPAN format is invalid: 999999999"
                      }
                    ]
                  }
                }
              }.to_json,
              headers: { 'Content-Type' => 'application/json' }
            )
          puts "Using mocked API error response"
        end
        
        # Expect to receive an error from the API
        expect { client.create_application(error_params) }
          .to raise_error(/Client error: 400/)
      end
    end
  end
end 