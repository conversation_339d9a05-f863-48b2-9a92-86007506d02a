require 'rails_helper'

RSpec.describe Admin, type: :model do
  # Create a valid admin instance to use in tests
  subject { build(:admin) }

  # Basic validations
  describe "validations" do
    it "is valid with valid attributes" do
      expect(subject).to be_valid
    end

    it "is not valid without an email" do
      subject.email = nil
      expect(subject).not_to be_valid
    end

    it "requires a unique email" do
      # Create a user with the same email to test uniqueness
      duplicate_admin = create(:admin)
      subject.email = duplicate_admin.email
      expect(subject).not_to be_valid
    end

    it "enforces email format" do
      subject.email = "invalid_email"
      expect(subject).not_to be_valid
      
      subject.email = "<EMAIL>"
      expect(subject).to be_valid
    end

    it "requires a password" do
      subject.password = nil
      expect(subject).not_to be_valid
    end

    it "requires password confirmation to match password" do
      subject.password = "password123"
      subject.password_confirmation = "different_password"
      expect(subject).not_to be_valid
    end

    it "requires minimum password length" do
      subject.password = "short"
      subject.password_confirmation = "short"
      expect(subject).not_to be_valid
    end

    it "requires first name" do
      subject.first_name = nil
      expect(subject).not_to be_valid
    end

    it "requires last name" do
      subject.last_name = nil
      expect(subject).not_to be_valid
    end
  end

  # Authentication
  describe "authentication" do
    it "can authenticate with correct password" do
      admin = create(:admin, password: "secure_password123", password_confirmation: "secure_password123")
      expect(admin.authenticate("secure_password123")).to eq(admin)
    end

    it "cannot authenticate with incorrect password" do
      admin = create(:admin, password: "secure_password123", password_confirmation: "secure_password123")
      expect(admin.authenticate("wrong_password")).to be_falsey
    end
  end

  # Instance methods
  describe "instance methods" do
    it "returns full name" do
      admin = build(:admin, first_name: "John", last_name: "Smith")
      expect(admin.full_name).to eq("John Smith")
    end

    describe "#generate_password_reset_token" do
      it "generates a reset token" do
        expect(subject.reset_token).to be_nil
        subject.generate_password_reset_token
        expect(subject.reset_token).not_to be_nil
      end

      it "sets reset token expiry" do
        expect(subject.reset_token_expiry).to be_nil
        subject.generate_password_reset_token
        expect(subject.reset_token_expiry).not_to be_nil
        expect(subject.reset_token_expiry).to be > Time.now
      end
    end

    describe "#valid_reset_token?" do
      it "returns true for valid token before expiry" do
        subject.reset_token = "valid_token"
        subject.reset_token_expiry = 1.hour.from_now
        expect(subject.valid_reset_token?("valid_token")).to be true
      end

      it "returns false for invalid token" do
        subject.reset_token = "valid_token"
        subject.reset_token_expiry = 1.hour.from_now
        expect(subject.valid_reset_token?("invalid_token")).to be false
      end

      it "returns false for expired token" do
        subject.reset_token = "valid_token"
        subject.reset_token_expiry = 1.hour.ago
        expect(subject.valid_reset_token?("valid_token")).to be false
      end
    end

    describe "#clear_reset_token" do
      it "clears reset token and expiry" do
        subject.reset_token = "some_token"
        subject.reset_token_expiry = 1.hour.from_now
        subject.clear_reset_token
        expect(subject.reset_token).to be_nil
        expect(subject.reset_token_expiry).to be_nil
      end
    end
  end

  # Class methods
  describe "class methods" do
    describe ".verify_email" do
      it "returns admin with matching email" do
        admin = create(:admin, email: "<EMAIL>")
        found_admin = Admin.verify_email("<EMAIL>")
        expect(found_admin).to eq(admin)
      end

      it "returns nil when no matching email" do
        expect(Admin.verify_email("<EMAIL>")).to be_nil
      end

      it "handles case insensitivity" do
        admin = create(:admin, email: "<EMAIL>")
        found_admin = Admin.verify_email("<EMAIL>")
        expect(found_admin).to eq(admin)
      end
    end
  end
end
