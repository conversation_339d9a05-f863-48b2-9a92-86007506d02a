require 'rails_helper'

RSpec.describe EnergySwitch, type: :model do
  let(:switch_user) { create(:switch_user) }
  let(:address) { create(:address, switch_user: switch_user) }
  let(:supplier) { create(:supplier, name: 'Test Supplier', logo: 'test_logo.png', trustpilot_rating: 4.5) }
  let(:payment_method) { create(:payment_method) }
  
  let(:gas_user_tariff) do
    create(:user_tariff, :gas,
      meter_serial_number: 'G123456789',
      gas_monthly_usage: 800,
      gas_estimated_annual_usage: 10000,
      unit_rate: 8.0,
      standing_charge: 20.0,
      switch_user: switch_user
    )
  end
  
  let(:electricity_user_tariff) do
    create(:user_tariff, :electricity,
      meter_serial_number: 'E123456789',
      electricity_monthly_usage: 250,
      electricity_est_annual_usage: 3000,
      unit_rate: 22.0,
      standing_charge: 25.0,
      profile_class: 1,
      gsp_code: '_C',
      switch_user: switch_user
    )
  end
  
  let(:energy_tariff) do
    create(:energy_tariff,
      supplier: supplier,
      tariff_name: 'Test Tariff',
      energy_type: 'both',
      tariff_type: 'fixed',
      exit_fees: 0,
      payment_methods: [payment_method]
    )
  end
  
  let(:electricity_tariff_rate) do
    create(:energy_tariff_rate,
      energy_tariff: energy_tariff,
      fuel_type: 'electricity',
      profile_class: 1,
      region: 'London',
      region_id: '12',
      gsp_code: '_C',
      standing_charge_inc_vat: 36.7109,
      standing_charge_ex_vat: 34.9628,
      unit_rate_inc_vat: 21.1769,
      unit_rate_ex_vat: 20.1685,
      annual_bill: 705.77
    )
  end
  
  let(:gas_tariff_rate) do
    create(:energy_tariff_rate,
      energy_tariff: energy_tariff,
      fuel_type: 'gas',
      profile_class: 1,
      region: 'London',
      region_id: '12',
      standing_charge_inc_vat: 28.5,
      standing_charge_ex_vat: 27.1429,
      unit_rate_inc_vat: 7.5,
      unit_rate_ex_vat: 7.1429,
      annual_bill: 800.0
    )
  end
  
  let(:energy_switch) do
    create(:energy_switch,
      switch_user: switch_user,
      gas_energy_tariff: gas_user_tariff,
      electricity_energy_tariff: electricity_user_tariff,
      address: address,
      payment_method: payment_method,
      switching_to_tariff: nil,
      status: :draft
    )
  end
  
  describe 'validations' do
    subject(:energy_switch_for_validation) do
      create(:energy_switch, 
             switch_user: switch_user,
             address: address,
             gas_energy_tariff: gas_user_tariff)
    end
    
    it { is_expected.to validate_uniqueness_of(:reference_number) }
    
    it 'requires at least one energy tariff' do
      switch = build(:energy_switch, gas_energy_tariff: nil, electricity_energy_tariff: nil)
      expect(switch).not_to be_valid
      expect(switch.errors[:base]).to include("At least one energy tariff (gas or electricity) must be present")
    end
    
    it 'requires switching_to_tariff unless status is draft' do
      # Build a new switch instead of using the let-defined one
      switch = build(:energy_switch, 
        switch_user: switch_user,
        gas_energy_tariff: gas_user_tariff,
        address: address,
        status: :confirmed,
        switching_to_tariff: nil
      )
      
      expect(switch).not_to be_valid
      expect(switch.errors[:switching_to_tariff]).to include("must be present unless the status is draft")
    end
    
    it 'allows draft status without switching_to_tariff' do
      energy_switch.status = :draft
      energy_switch.switching_to_tariff = nil
      expect(energy_switch).to be_valid
    end
  end
  
  describe 'associations' do
    it { should belong_to(:switch_user) }
    it { should belong_to(:gas_energy_tariff).class_name('UserTariff').optional }
    it { should belong_to(:electricity_energy_tariff).class_name('UserTariff').optional }
    it { should belong_to(:address) }
    it { should belong_to(:payment_method).optional }
    it { should belong_to(:switching_to_tariff).class_name('EnergyTariff').optional }
  end
  
  describe 'scopes' do
    describe '.with_matching_meters' do
      it 'finds switches with matching meter serial numbers' do
        # Create the energy switch
        energy_switch
        
        # Test the scope
        result = described_class.with_matching_meters(
          switch_user: switch_user,
          gas_msn: 'G123456789',
          electricity_msn: 'E123456789'
        )
        
        expect(result).to include(energy_switch)
      end
      
      it 'finds switches with only gas meter matching' do
        # Create the energy switch
        energy_switch
        
        # Test the scope
        result = described_class.with_matching_meters(
          switch_user: switch_user,
          gas_msn: 'G123456789',
          electricity_msn: 'NONEXISTENT'
        )
        
        expect(result).to include(energy_switch)
      end
      
      it 'finds switches with only electricity meter matching' do
        # Create the energy switch
        energy_switch
        
        # Test the scope
        result = described_class.with_matching_meters(
          switch_user: switch_user,
          gas_msn: 'NONEXISTENT',
          electricity_msn: 'E123456789'
        )
        
        expect(result).to include(energy_switch)
      end
      
      it 'does not find switches with non-matching meters' do
        # Create the energy switch
        energy_switch
        
        # Test the scope
        result = described_class.with_matching_meters(
          switch_user: switch_user,
          gas_msn: 'NONEXISTENT',
          electricity_msn: 'NONEXISTENT'
        )
        
        expect(result).not_to include(energy_switch)
      end
    end
  end
  
  describe 'callbacks' do
    describe '#ensure_unique_reference_number' do
      it 'generates a reference number before saving' do
        new_switch = build(:energy_switch, 
                          gas_energy_tariff: gas_user_tariff,
                          electricity_energy_tariff: nil,
                          reference_number: nil)
        new_switch.save
        expect(new_switch.reference_number).to be_present
        expect(new_switch.reference_number).to match(/^ES\d{8}[A-Z0-9]{6}$/)
      end
      
      it 'does not change existing reference numbers' do
        energy_switch.reference_number = 'TEST123456'
        energy_switch.save
        expect(energy_switch.reference_number).to eq('TEST123456')
      end
      
      it 'ensures unique reference numbers' do
        # Mock SecureRandom to return the same value twice, then a different one
        allow(SecureRandom).to receive(:hex).and_return('ABC123', 'ABC123', 'XYZ789')
        
        # Fix the Time.current mocking to use a proper time object
        time_now = Time.zone.local(2024, 1, 1)
        allow(Time).to receive(:current).and_return(time_now)
        
        # Set up a duplicate reference number scenario
        existing_switch = create(:energy_switch,
                               gas_energy_tariff: gas_user_tariff,
                               electricity_energy_tariff: nil,
                               reference_number: 'ES20240101ABC123')
        
        # New switch should get a different reference number
        new_switch = build(:energy_switch,
                          gas_energy_tariff: gas_user_tariff,
                          electricity_energy_tariff: nil,
                          reference_number: nil)
        new_switch.save
        
        expect(new_switch.reference_number).to eq('ES20240101XYZ789')
      end
    end
  end
  
  describe '#current_stage' do
    it 'returns 1 for draft status' do
      energy_switch.status = :draft
      expect(energy_switch.current_stage).to eq(1)
    end
    
    it 'returns 1 for confirmed status' do
      energy_switch.status = :confirmed
      expect(energy_switch.current_stage).to eq(1)
    end
    
    it 'returns 2 for submitted_to_supplier status' do
      energy_switch.status = :submitted_to_supplier
      expect(energy_switch.current_stage).to eq(2)
    end
    
    it 'returns 3 for supplier_processing status' do
      energy_switch.status = :supplier_processing
      expect(energy_switch.current_stage).to eq(3)
    end
    
    it 'returns 4 for switched status' do
      energy_switch.status = :switched
      expect(energy_switch.current_stage).to eq(4)
    end
    
    it 'returns 4 for rejected_by_supplier status' do
      energy_switch.status = :rejected_by_supplier
      expect(energy_switch.current_stage).to eq(4)
    end
  end
  
  describe '#stages' do
    it 'returns the array of stage names' do
      expect(energy_switch.stages).to eq(["Submitted", "Processing", "Switching", "Complete"])
    end
  end
  
  describe '#available_tariffs' do
    before do
      electricity_tariff_rate
      gas_tariff_rate
      energy_switch
    end
    
    it 'returns tariffs matching both fuel types when both are present' do
      # Both gas and electricity tariffs are present
      tariffs = energy_switch.available_tariffs
      
      # Ensure we get tariffs back
      expect(tariffs).not_to be_empty
      
      # Check that the returned tariff includes both fuel types
      first_tariff = tariffs.first
      expect(first_tariff[:fuel_types]).to include('electricity', 'gas')
      expect(first_tariff[:fuel_type]).to eq('Electricity & Gas')
    end
    
    it 'returns only electricity tariffs when gas is not present' do
      # Create a switch with only electricity
      electricity_only_switch = create(:energy_switch,
                                     switch_user: switch_user,
                                     gas_energy_tariff: nil,
                                     electricity_energy_tariff: electricity_user_tariff,
                                     address: address)
      
      tariffs = electricity_only_switch.available_tariffs
      
      # Ensure we get tariffs back
      expect(tariffs).not_to be_empty
      
      # Check that we're only showing electricity tariffs
      first_tariff = tariffs.first
      expect(first_tariff[:fuel_types]).to include('electricity')
      expect(first_tariff[:fuel_type]).to eq('Electricity')
    end
    
    it 'returns only gas tariffs when electricity is not present' do
      # Create a switch with only gas
      gas_only_switch = create(:energy_switch,
                             switch_user: switch_user,
                             gas_energy_tariff: gas_user_tariff,
                             electricity_energy_tariff: nil,
                             address: address)
      
      tariffs = gas_only_switch.available_tariffs
      
      # Ensure we get tariffs back
      expect(tariffs).not_to be_empty
      
      # Check that we're only showing gas tariffs
      first_tariff = tariffs.first
      expect(first_tariff[:fuel_types]).to include('gas')
      expect(first_tariff[:fuel_type]).to eq('Gas')
    end
    
    it 'returns empty array when no fuel types are present' do
      # This shouldn't happen due to validation, but testing behavior
      allow(energy_switch).to receive(:gas_energy_tariff).and_return(nil)
      allow(energy_switch).to receive(:electricity_energy_tariff).and_return(nil)
      
      expect(energy_switch.available_tariffs).to be_empty
    end
    
    it 'includes all required tariff information' do
      tariffs = energy_switch.available_tariffs
      first_tariff = tariffs.first
      
      # Check required fields are present
      expect(first_tariff).to include(
        :id, :supplier_name, :tariff_name, :tariff_type, 
        :exit_fee, :estimated_cost, :estimated_saving, :payment_methods
      )
      
      # Check supplier info is included
      expect(first_tariff[:supplier_name]).to eq('Test Supplier')
      expect(first_tariff[:logo]).to eq('test_logo.png')
      expect(first_tariff[:trustpilot_rating]).to eq(4.5)
      
      # Check rate details are included
      expect(first_tariff).to have_key(:electricity)
      expect(first_tariff).to have_key(:gas)
    end
  end
  
  # Testing private methods with send
  describe 'private methods' do
    describe '#format_tariff_for_display' do
      before do
        electricity_tariff_rate
        gas_tariff_rate
      end
      
      it 'formats tariff data correctly' do
        # Mock the tariff calculation methods
        allow_any_instance_of(EnergyTariff).to receive(:calculate_estimated_cost).and_return({
          electricity: { monthly: 50.0, yearly: 600.0 },
          gas: { monthly: 41.67, yearly: 500.0 },
          total: { monthly: 91.67, yearly: 1100.0 }
        })
        
        allow_any_instance_of(EnergyTariff).to receive(:calculate_estimated_saving).and_return({
          electricity: { amount: 100.0, percentage: 14 },
          gas: { amount: 150.0, percentage: 23 },
          total: { amount: 250.0, percentage: 19 }
        })
        
        result = energy_switch.send(:format_tariff_for_display, energy_tariff, ['electricity', 'gas'])
        
        # Check basic tariff info
        expect(result[:supplier_name]).to eq('Test Supplier')
        expect(result[:tariff_name]).to eq('Test Tariff')
        expect(result[:tariff_type]).to eq('Fixed')
        expect(result[:exit_fee]).to eq(0)
        
        # Check cost and saving estimates
        expect(result[:estimated_cost][:total][:yearly]).to eq(1100.0)
        expect(result[:estimated_saving][:total][:amount]).to eq(250.0)
        expect(result[:estimated_saving][:total][:percentage]).to eq(19)
        
        # Check rates are included
        expect(result[:electricity]).to be_present
        expect(result[:gas]).to be_present
        
        # Check rates values are formatted correctly with VAT-inclusive prices
        expect(result[:electricity][:unit_rate]).to eq(21.18)
        expect(result[:electricity][:standing_charge]).to eq(36.71)
        expect(result[:gas][:unit_rate]).to eq(7.5)
        expect(result[:gas][:standing_charge]).to eq(28.5)
      end
    end
    
    describe '#set_fuel_type_info' do
      it 'sets both fuel types when gas and electricity are present' do
        tariff_info = {}
        electricity_rate = double('EnergyTariffRate', fuel_type: 'electricity')
        gas_rate = double('EnergyTariffRate', fuel_type: 'gas')
        
        energy_switch.send(:set_fuel_type_info, tariff_info, electricity_rate, gas_rate)
        
        expect(tariff_info[:fuel_types]).to eq(['electricity', 'gas'])
        expect(tariff_info[:fuel_type]).to eq('Electricity & Gas')
      end
      
      it 'sets electricity only when gas is not present' do
        tariff_info = {}
        electricity_rate = double('EnergyTariffRate', fuel_type: 'electricity')
        
        energy_switch.send(:set_fuel_type_info, tariff_info, electricity_rate, nil)
        
        expect(tariff_info[:fuel_types]).to eq(['electricity'])
        expect(tariff_info[:fuel_type]).to eq('Electricity')
      end
      
      it 'sets gas only when electricity is not present' do
        tariff_info = {}
        gas_rate = double('EnergyTariffRate', fuel_type: 'gas')
        
        energy_switch.send(:set_fuel_type_info, tariff_info, nil, gas_rate)
        
        expect(tariff_info[:fuel_types]).to eq(['gas'])
        expect(tariff_info[:fuel_type]).to eq('Gas')
      end
    end
    
    describe '#add_electricity_rate_details' do
      it 'adds standard rate details for profile class 1' do
        tariff_info = {}
        electricity_rate = double('EnergyTariffRate', 
                                profile_class: 1,
                                unit_rate_inc_vat: 21.1769,
                                standing_charge_inc_vat: 36.7109)
        
        energy_switch.send(:add_electricity_rate_details, tariff_info, electricity_rate)
        
        expect(tariff_info[:electricity]).to include(
          unit_rate: 21.18,
          standing_charge: 36.71
        )
      end
      
      it 'adds economy7 rate details for profile class 2' do
        tariff_info = {}
        electricity_rate = double('EnergyTariffRate', 
                                profile_class: 2,
                                standing_charge_inc_vat: 36.7109,
                                day_unit_rate_inc_vat: 22.5,
                                night_unit_rate_inc_vat: 12.5,
                                weekend_unit_rate_inc_vat: 15.0)
        
        energy_switch.send(:add_electricity_rate_details, tariff_info, electricity_rate)
        
        expect(tariff_info[:electricity]).to include(
          standing_charge: 36.71,
          day_unit_rate: 22.5,
          night_unit_rate: 12.5,
          weekend_unit_rate: 15.0
        )
      end
    end
    
    describe '#add_gas_rate_details' do
      it 'adds standard rate details for gas' do
        tariff_info = {}
        gas_rate = double('EnergyTariffRate', 
                        unit_rate_inc_vat: 7.5,
                        standing_charge_inc_vat: 28.5)
        
        energy_switch.send(:add_gas_rate_details, tariff_info, gas_rate)
        
        expect(tariff_info[:gas]).to include(
          unit_rate: 7.5,
          standing_charge: 28.5
        )
      end
    end
  end
end 