require 'rails_helper'

RSpec.describe SupplierSubmission, type: :model do
  describe 'associations' do
    it { should belong_to(:energy_switch) }
    it { should belong_to(:supplier) }
  end

  describe 'validations' do
    it { should validate_presence_of(:submission_type) }
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(
      pending: 0,
      submitted: 1,
      successful: 2,
      failed: 3,
      rejected: 4,
      retry_scheduled: 5
    ) }
  end

  describe 'scopes' do
    let(:supplier) { create(:supplier, name: 'Tulo Energy') }
    let(:energy_switch) { create(:energy_switch, :with_gas) }
    
    before do
      create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :pending)
      create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :submitted)
      create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :successful)
      create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :failed)
      create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :rejected)
    end
    
    it 'returns successful submissions' do
      expect(SupplierSubmission.successful.count).to eq(1)
    end
    
    it 'returns pending or submitted submissions' do
      expect(SupplierSubmission.pending_or_submitted.count).to eq(2)
    end
    
    it 'returns pending, submitted or successful submissions' do
      expect(SupplierSubmission.pending_or_submitted_or_successful.count).to eq(3)
    end
    
    it 'returns failed or rejected submissions' do
      expect(SupplierSubmission.failed_or_rejected.count).to eq(2)
    end
  end

  describe 'callbacks' do
    let(:energy_switch) { create(:energy_switch, :with_gas) }
    let(:supplier) { create(:supplier) }
    
    it 'sets submitted_at when status is submitted' do
      current_time = Time.current
      submission = create(:supplier_submission, 
        energy_switch: energy_switch,
        supplier: supplier,
        status: :submitted, 
        submitted_at: current_time
      )
      expect(submission.submitted_at).to be_within(0.001.seconds).of(current_time)
    end
    
    it 'does not set submitted_at when status is not submitted' do
      submission = create(:supplier_submission,
        energy_switch: energy_switch,
        supplier: supplier,
        status: :pending
      )
      expect(submission.submitted_at).to be_nil
    end
  end

  describe '#status_summary' do
    let(:supplier) { create(:supplier, name: 'Tulo Energy') }
    let(:energy_switch) { create(:energy_switch, :with_gas) }
    
    it 'returns correct summary for pending status' do
      submission = create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :pending)
      expect(submission.status_summary).to eq('Waiting to be submitted')
    end
    
    it 'returns correct summary for submitted status' do
      submission = create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :submitted)
      expect(submission.status_summary).to eq("Submitted to #{supplier.name}, awaiting response")
    end
    
    it 'returns correct summary for successful status' do
      submission = create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :successful)
      expect(submission.status_summary).to eq("Successfully processed by #{supplier.name}")
    end
    
    it 'returns correct summary for failed status' do
      error_message = 'Connection error'
      submission = create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :failed, error_message: error_message)
      expect(submission.status_summary).to eq("Failed: #{error_message}")
    end
    
    it 'returns correct summary for rejected status' do
      rejection_reason = 'Invalid address'
      submission = create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :rejected, rejection_reason: rejection_reason)
      expect(submission.status_summary).to eq("Rejected by #{supplier.name}: #{rejection_reason}")
    end
    
    it 'returns correct summary for retry_scheduled status' do
      submission = create(:supplier_submission, energy_switch: energy_switch, supplier: supplier, status: :retry_scheduled)
      expect(submission.status_summary).to eq('Failed, scheduled for retry')
    end
  end
end 