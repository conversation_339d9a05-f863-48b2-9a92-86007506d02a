require 'rails_helper'

RSpec.describe EnergyTariffRate, type: :model do
  let(:energy_tariff) { create(:energy_tariff) }
  
  describe 'validations' do
    it { should belong_to(:energy_tariff) }
    it { should validate_presence_of(:fuel_type) }

    context 'when fuel type is electricity' do
      subject { build(:energy_tariff_rate, fuel_type: 'electricity', energy_tariff: energy_tariff) }

      it { should validate_presence_of(:gsp_code) }
      it { should validate_presence_of(:profile_class) }
      
      it 'validates gsp_code format' do
        # Valid format
        valid_rate = build(:energy_tariff_rate, 
          fuel_type: 'electricity', 
          gsp_code: '_A', 
          profile_class: 1,
          energy_tariff: energy_tariff)
        expect(valid_rate).to be_valid

        # Invalid format
        invalid_rate = build(:energy_tariff_rate, 
          fuel_type: 'electricity', 
          gsp_code: 'A', 
          profile_class: 1,
          energy_tariff: energy_tariff)
        expect(invalid_rate).not_to be_valid
        expect(invalid_rate.errors[:gsp_code]).to include("must be in the format '_X' where X is a capital letter")
        
        # Another invalid format
        invalid_rate = build(:energy_tariff_rate, 
          fuel_type: 'electricity', 
          gsp_code: '_a', 
          profile_class: 1,
          energy_tariff: energy_tariff)
        expect(invalid_rate).not_to be_valid
        expect(invalid_rate.errors[:gsp_code]).to include("must be in the format '_X' where X is a capital letter")
      end
    end

    context 'when fuel type is gas' do
      subject { build(:energy_tariff_rate, fuel_type: 'gas', energy_tariff: energy_tariff) }

      it { should_not validate_presence_of(:gsp_code) }
      it { should_not validate_presence_of(:profile_class) }
    end

    context 'when profile class is 1 and fuel type is electricity' do
      subject { build(:energy_tariff_rate, profile_class: 1, fuel_type: 'electricity', energy_tariff: energy_tariff) }

      it { should validate_presence_of(:standing_charge_inc_vat) }
      it { should validate_presence_of(:unit_rate_inc_vat) }
      it { should validate_numericality_of(:standing_charge_inc_vat).is_greater_than_or_equal_to(0) }
      it { should validate_numericality_of(:unit_rate_inc_vat).is_greater_than_or_equal_to(0) }
    end

    context 'when profile class is 2 and fuel type is electricity' do
      subject { build(:energy_tariff_rate, profile_class: 2, fuel_type: 'electricity', energy_tariff: energy_tariff) }

      it { should validate_presence_of(:standing_charge_inc_vat) }
      it { should validate_presence_of(:day_unit_rate_inc_vat) }
      it { should validate_presence_of(:night_unit_rate_inc_vat) }
      it { should validate_numericality_of(:standing_charge_inc_vat).is_greater_than_or_equal_to(0) }
      it { should validate_numericality_of(:day_unit_rate_inc_vat).is_greater_than_or_equal_to(0) }
      it { should validate_numericality_of(:night_unit_rate_inc_vat).is_greater_than_or_equal_to(0) }
    end
  end
end 