require 'spec_helper'

# Load the service directly without Rails dependencies
require_relative '../../lib/services/supabase_storage_service'

# Mock Rails logger
module Rails
  def self.logger
    @logger ||= Logger.new('/dev/null')
  end
end

RSpec.describe Services::SupabaseStorageService do
  let(:service) { described_class.new }
  let(:test_key) { 'test/file.txt' }
  let(:test_content) { 'Hello, World!' }
  let(:content_type) { 'text/plain' }

  before do
    # Set up environment variables for testing
    allow(ENV).to receive(:[]).with('SUPABASE_URL').and_return('https://test.supabase.co')
    allow(ENV).to receive(:[]).with('SUPABASE_SERVICE_ROLE_KEY').and_return('test-service-role-key')
    allow(ENV).to receive(:[]).with('SUPABASE_USER_BILLS_BUCKET_NAME').and_return('test-bucket')
    
    # Stub the Faraday client
    @faraday_stub = instance_double(Faraday::Connection)
    allow(Faraday).to receive(:new).and_return(@faraday_stub)
    allow(@faraday_stub).to receive(:post).and_return(
      instance_double(Faraday::Response, success?: true, body: '{"signedURL": "/storage/v1/test-url"}')
    )
    allow(@faraday_stub).to receive(:delete).and_return(
      instance_double(Faraday::Response, success?: true, body: '{}')
    )
  end

  describe '#initialize' do
    context 'with missing environment variables' do
      it 'raises error when SUPABASE_URL is missing' do
        allow(ENV).to receive(:[]).with('SUPABASE_URL').and_return(nil)
        expect { described_class.new }.to raise_error('Missing SUPABASE_URL environment variable')
      end

      it 'raises error when SUPABASE_SERVICE_ROLE_KEY is missing' do
        allow(ENV).to receive(:[]).with('SUPABASE_SERVICE_ROLE_KEY').and_return(nil)
        expect { described_class.new }.to raise_error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')
      end

      it 'raises error when SUPABASE_USER_BILLS_BUCKET_NAME is missing' do
        allow(ENV).to receive(:[]).with('SUPABASE_USER_BILLS_BUCKET_NAME').and_return(nil)
        expect { described_class.new }.to raise_error('Missing SUPABASE_USER_BILLS_BUCKET_NAME environment variable')
      end
    end
  end

  describe '#presigned_url' do
    it 'returns nil for blank key' do
      expect(service.presigned_url('')).to be_nil
      expect(service.presigned_url(nil)).to be_nil
    end

    it 'generates a presigned URL for valid key' do
      url = service.presigned_url(test_key)
      expect(url).to eq('https://test.supabase.co/storage/v1/test-url')
    end

    it 'handles API errors gracefully' do
      allow(@faraday_stub).to receive(:post).and_return(
        instance_double(Faraday::Response, success?: false, body: 'Error')
      )
      
      expect(Rails.logger).to receive(:error).with('Supabase presigned URL error: Error')
      expect(service.presigned_url(test_key)).to be_nil
    end
  end

  describe '#presigned_upload_url' do
    it 'returns nil for blank key' do
      expect(service.presigned_upload_url('', content_type)).to be_nil
    end

    it 'generates a presigned upload URL' do
      allow(@faraday_stub).to receive(:post).and_return(
        instance_double(Faraday::Response, success?: true, body: '{"url": "/storage/v1/upload-url"}')
      )
      
      url = service.presigned_upload_url(test_key, content_type)
      expect(url).to eq('https://test.supabase.co/storage/v1/upload-url')
    end
  end

  describe '#upload_file' do
    it 'returns false for blank key or data' do
      expect(service.upload_file('', test_content, content_type)).to be false
      expect(service.upload_file(test_key, '', content_type)).to be false
    end

    it 'uploads file successfully' do
      expect(service.upload_file(test_key, test_content, content_type)).to be true
    end

    it 'handles upload errors' do
      allow(@faraday_stub).to receive(:post).and_return(
        instance_double(Faraday::Response, success?: false, body: 'Upload failed')
      )
      
      expect(Rails.logger).to receive(:error).with('Supabase upload error: Upload failed')
      expect(service.upload_file(test_key, test_content, content_type)).to be false
    end
  end

  describe '#delete_file' do
    it 'returns false for blank key' do
      expect(service.delete_file('')).to be false
    end

    it 'deletes file successfully' do
      expect(service.delete_file(test_key)).to be true
    end

    it 'handles delete errors' do
      allow(@faraday_stub).to receive(:delete).and_return(
        instance_double(Faraday::Response, success?: false, body: 'Delete failed')
      )
      
      expect(Rails.logger).to receive(:error).with('Supabase delete error: Delete failed')
      expect(service.delete_file(test_key)).to be false
    end
  end

  describe '#list_files' do
    it 'lists files successfully' do
      allow(@faraday_stub).to receive(:post).and_return(
        instance_double(Faraday::Response, success?: true, body: '[{"name": "file1.txt"}, {"name": "file2.txt"}]')
      )
      
      files = service.list_files
      expect(files).to eq([{"name" => "file1.txt"}, {"name" => "file2.txt"}])
    end

    it 'handles list errors' do
      allow(@faraday_stub).to receive(:post).and_return(
        instance_double(Faraday::Response, success?: false, body: 'List failed')
      )
      
      expect(Rails.logger).to receive(:error).with('Supabase list files error: List failed')
      expect(service.list_files).to eq([])
    end
  end
end 