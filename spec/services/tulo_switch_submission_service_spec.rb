require 'rails_helper'

RSpec.describe TuloSwitchSubmissionService, type: :service do
  let(:supplier) { create(:supplier, name: 'Tulo Energy') }
  let(:energy_tariff) { create(:energy_tariff, supplier: supplier) }
  let(:switch_user) { create(:switch_user, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', phone_number: '***********') }
  let(:address) { create(:address, switch_user: switch_user) }
  let(:payment_method) { create(:payment_method, switch_user: switch_user, account_holder_name: '<PERSON>', account_number: '********', sort_code: '123456') }
  let(:electricity_tariff) { create(:user_tariff, energy_type: :electricity, meter_point_administration_number: '*************', switch_user: switch_user) }
  let(:gas_tariff) { create(:user_tariff, energy_type: :gas, meter_point_reference_number: '**********', switch_user: switch_user) }
  
  let(:energy_switch) do
    create(:energy_switch,
      switch_user: switch_user,
      address: address,
      gas_energy_tariff: gas_tariff,
      electricity_energy_tariff: electricity_tariff,
      payment_method: payment_method,
      switching_to_tariff: energy_tariff,
      status: :confirmed
    )
  end
  
  let(:tulo_client) { instance_double(TuloApiClient) }
  let(:service) { described_class.new(energy_switch) }
  
  before do
    allow(TuloApiClient).to receive(:new).and_return(tulo_client)
    # Stub calculate_costs methods
    allow_any_instance_of(UserTariff).to receive(:calculate_costs).with('electricity').and_return([100, 50])
    allow_any_instance_of(UserTariff).to receive(:calculate_costs).with('gas').and_return([80, 40])
  end
  
  describe '#submit' do
    context 'when submission is successful' do
      let(:successful_response) { { 'CustomerQuoteId' => 'TULO-12345' } }
      
      before do
        allow(tulo_client).to receive(:sale_made).and_return(successful_response)
      end
      
      it 'creates a submission record with pending status' do
        expect {
          service.submit
        }.to change(SupplierSubmission, :count).by(1)
        
        submission = SupplierSubmission.last
        expect(submission.status).to eq('successful')
        expect(submission.supplier).to eq(supplier)
        expect(submission.energy_switch).to eq(energy_switch)
        expect(submission.submission_type).to eq('switch_request')
        expect(submission.supplier_reference).to eq('TULO-12345')
      end
      
      it 'includes monthly DD amounts in the request' do
        service.submit
        submission = SupplierSubmission.last
        
        request_json = JSON.parse(submission.raw_request)
        expect(request_json['DDElectricMonthlyAmount']).to eq(50)
        expect(request_json['DDGasMonthlyAmount']).to eq(40)
      end
      
      it 'updates the energy switch with the supplier reference' do
        service.submit
        energy_switch.reload
        
        expect(energy_switch.supplier_reference).to eq('TULO-12345')
        expect(energy_switch.status).to eq('submitted_to_supplier')
      end
      
      it 'returns a success result' do
        result = service.submit
        
        expect(result.success?).to be true
        expect(result.energy_switch).to eq(energy_switch)
        expect(result.supplier_reference).to eq('TULO-12345')
      end
    end
    
    context 'when submission is successful with lowercase reference key' do
      let(:successful_response) { { 'customerQuoteId' => 'TULO-54321' } }
      
      before do
        allow(tulo_client).to receive(:sale_made).and_return(successful_response)
      end
      
      it 'correctly handles the lowercase reference key' do
        result = service.submit
        
        submission = SupplierSubmission.last
        expect(submission.supplier_reference).to eq('TULO-54321')
        
        energy_switch.reload
        expect(energy_switch.supplier_reference).to eq('TULO-54321')
        expect(result.supplier_reference).to eq('TULO-54321')
      end
    end
    
    context 'when submission fails with an error' do
      let(:error_message) { 'Invalid address' }
      
      before do
        allow(tulo_client).to receive(:sale_made).and_raise(StandardError.new(error_message))
      end
      
      it 'creates a submission record with failed status' do
        expect {
          service.submit rescue nil
        }.to change(SupplierSubmission, :count).by(1)
        
        submission = SupplierSubmission.last
        expect(submission.status).to eq('failed')
        expect(submission.error_message).to eq(error_message)
      end
      
      it 'returns an error result' do
        result = service.submit
        
        expect(result.success?).to be false
        expect(result.error).to eq(error_message)
      end
    end
    
    context 'when API returns a failure response' do
      let(:error_message) { "MPANs have been included with no DDElectricMonthlyAmount specified,\r\nUnable to locate Electricity Tariff 'Tulo Fixed Tariff' for GSP '_H'" }
      let(:failed_response) { { 'success' => false, 'customerQuoteId' => 1358, 'errorMessage' => error_message } }
      
      before do
        allow(tulo_client).to receive(:sale_made).and_return(failed_response)
      end
      
      it 'creates a submission record with failed status' do
        expect {
          service.submit
        }.to change(SupplierSubmission, :count).by(1)
        
        submission = SupplierSubmission.last
        expect(submission.status).to eq('failed')
        expect(submission.error_message).to eq(error_message)
      end
      
      it 'returns an error result' do
        result = service.submit
        
        expect(result.success?).to be false
        expect(result.error).to eq(error_message)
      end
    end
    
    context 'when submission is rejected by supplier with Invalid or Bad Request' do
      let(:rejection_message) { 'Bad Request: Invalid MPAN' }
      
      before do
        allow(tulo_client).to receive(:sale_made).and_raise(StandardError.new(rejection_message))
      end
      
      it 'creates a submission record with rejected status' do
        expect {
          service.submit rescue nil
        }.to change(SupplierSubmission, :count).by(1)
        
        submission = SupplierSubmission.last
        expect(submission.status).to eq('rejected')
        expect(submission.error_message).to eq(rejection_message)
        expect(submission.rejection_reason).to eq('Invalid electricity meter point details')
      end
      
      it 'updates the energy switch status to rejected_by_supplier' do
        service.submit rescue nil
        energy_switch.reload
        
        expect(energy_switch.status).to eq('rejected_by_supplier')
      end
    end
    
    context 'when API returns a rejection response' do
      let(:error_message) { "Bad Request: Invalid MPAN" }
      let(:rejection_response) { { 'success' => false, 'customerQuoteId' => 1358, 'errorMessage' => error_message } }
      
      before do
        allow(tulo_client).to receive(:sale_made).and_return(rejection_response)
      end
      
      it 'creates a submission record with rejected status' do
        expect {
          service.submit
        }.to change(SupplierSubmission, :count).by(1)
        
        submission = SupplierSubmission.last
        expect(submission.status).to eq('rejected')
        expect(submission.error_message).to eq(error_message)
        expect(submission.rejection_reason).to eq('Invalid electricity meter point details')
      end
      
      it 'updates the energy switch status to rejected_by_supplier' do
        service.submit
        energy_switch.reload
        
        expect(energy_switch.status).to eq('rejected_by_supplier')
      end
    end
    
    context 'with multiple submission attempts' do
      let(:successful_response) { { 'CustomerQuoteId' => 'TULO-12345' } }
      
      before do
        allow(tulo_client).to receive(:sale_made).and_return(successful_response)
        
        # Create a previous submission
        create(:supplier_submission, 
          energy_switch: energy_switch,
          supplier: supplier,
          status: :failed,
          attempt_number: 1
        )
      end
      
      it 'increments the attempt number' do
        service.submit
        
        latest_submission = energy_switch.supplier_submissions.order(created_at: :desc).first
        expect(latest_submission.attempt_number).to eq(2)
      end
    end
  end
end 