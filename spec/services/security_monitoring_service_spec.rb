require 'rails_helper'

RSpec.describe SecurityMonitoringService, type: :service do
  let(:email) { '<EMAIL>' }
  let(:ip_address) { '***********' }
  
  before do
    # Mock Rails.cache methods
    allow(Rails.cache).to receive(:fetch).and_return(0)
    allow(Rails.cache).to receive(:write).and_return(true)
    allow(Rails.cache).to receive(:read).and_return(nil)
    
    # Mock Rails.logger
    allow(Rails.logger).to receive(:info).and_return(true)
    allow(Rails.logger).to receive(:warn).and_return(true)
    allow(Rails.logger).to receive(:error).and_return(true)
    
    # Mock SecurityEvent creation
    allow(SecurityEvent).to receive(:create).and_return(true)
    
    # Mock SecurityMailer calls
    mail_double = double('SecurityMailer', deliver_later: true, deliver_now: true)
    allow(SecurityMailer).to receive(:alert).and_return(mail_double)
  end
  
  describe '.record_verification_attempt' do
    context 'with successful verification' do
      before do
        SecurityMonitoringService.record_verification_attempt(email, ip_address, true)
      end
      
      it 'increments the verification attempts counter' do
        expect(Rails.cache).to have_received(:fetch).with("security_monitoring:verification_attempts", anything)
        expect(Rails.cache).to have_received(:write).with("security_monitoring:verification_attempts", 1, anything)
      end
      
      it 'creates a security event with the correct event type' do
        expect(SecurityEvent).to have_received(:create).with(
          hash_including(
            event_type: 'verification_success', 
            email: email, 
            ip_address: ip_address
          )
        )
      end
      
      it 'checks for suspicious activity patterns' do
        expect(Rails.cache).to have_received(:fetch).with("verification_attempts:#{email}", anything)
        expect(Rails.cache).to have_received(:fetch).with("verification_attempts:ip:#{ip_address}", anything)
      end
    end
    
    context 'with unsuccessful verification' do
      before do
        SecurityMonitoringService.record_verification_attempt(email, ip_address, false)
      end
      
      it 'creates a security event with the failure event type' do
        expect(SecurityEvent).to have_received(:create).with(
          hash_including(
            event_type: 'verification_failure', 
            email: email, 
            ip_address: ip_address
          )
        )
      end
    end
  end
  
  describe '.record_code_send' do
    before do
      SecurityMonitoringService.record_code_send(email, ip_address)
    end
    
    it 'increments the code sends counter' do
      expect(Rails.cache).to have_received(:fetch).with("security_monitoring:code_sends", anything)
      expect(Rails.cache).to have_received(:write).with("security_monitoring:code_sends", 1, anything)
    end
    
    it 'creates a security event with the correct event type' do
      expect(SecurityEvent).to have_received(:create).with(
        hash_including(
          event_type: 'verification_code_sent', 
          email: email, 
          ip_address: ip_address
        )
      )
    end
    
    it 'checks for suspicious code send patterns' do
      expect(Rails.cache).to have_received(:fetch).with("code_sends:#{email}", anything)
      expect(Rails.cache).to have_received(:fetch).with("code_sends:ip:#{ip_address}", anything)
    end
  end
  
  describe '.record_rate_limit_triggered' do
    let(:limit_type) { 'verification_attempt' }
    
    before do
      SecurityMonitoringService.record_rate_limit_triggered(email, ip_address, limit_type)
    end
    
    it 'creates a security event with the rate limit triggered event type' do
      expect(SecurityEvent).to have_received(:create).with(
        hash_including(
          event_type: 'rate_limit_triggered', 
          email: email, 
          ip_address: ip_address,
          details: hash_including(limit_type: limit_type)
        )
      )
    end
    
    it 'logs a warning message' do
      expect(Rails.logger).to have_received(:warn).with(/Rate limit triggered/)
    end
    
    it 'triggers a security alert' do
      expect(Rails.logger).to have_received(:error).with(/SECURITY ALERT/)
      expect(SecurityMailer).to have_received(:alert)
    end
  end
  
  describe 'threshold triggers' do
    context 'when verification attempts exceed threshold' do
      before do
        # Mock that the email has exceeded threshold
        allow(Rails.cache).to receive(:fetch).with("verification_attempts:#{email}", anything).and_return(SecurityMonitoringService::MAX_VERIFICATION_ATTEMPTS_THRESHOLD)
        
        # Run the method to test
        SecurityMonitoringService.record_verification_attempt(email, ip_address, true)
      end
      
      it 'triggers a security alert' do
        expect(Rails.logger).to have_received(:error).with(/SECURITY ALERT/)
        expect(SecurityMailer).to have_received(:alert).with(/Excessive verification attempts/)
      end
    end
    
    context 'when code sends exceed threshold' do
      before do
        # Mock that the IP has exceeded threshold
        allow(Rails.cache).to receive(:fetch).with("code_sends:ip:#{ip_address}", anything).and_return(SecurityMonitoringService::MAX_CODE_SENDS_THRESHOLD)
        
        # Run the method to test
        SecurityMonitoringService.record_code_send(email, ip_address)
      end
      
      it 'triggers a security alert' do
        expect(Rails.logger).to have_received(:error).with(/SECURITY ALERT/)
        expect(SecurityMailer).to have_received(:alert).with(/Excessive verification code sends/)
      end
    end
  end
end 