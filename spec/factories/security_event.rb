FactoryBot.define do
  factory :security_event do
    event_type { ['login', 'logout', 'password_reset', 'failed_login'].sample }
    email { "test#{SecureRandom.hex(4)}@example.com" }
    ip_address { Faker::Internet.ip_v4_address }
    created_at { Time.current }
    details { {} }
    
    trait :verification_success do
      event_type { SecurityEvent::VERIFICATION_SUCCESS }
      details { { success: true, timestamp: Time.now.utc } }
    end
    
    trait :verification_failure do
      event_type { SecurityEvent::VERIFICATION_FAILURE }
      details { { success: false, timestamp: Time.now.utc } }
    end
    
    trait :with_ip_only do
      email { nil }
    end
    
    trait :with_email_only do
      ip_address { nil }
    end
  end
end 