FactoryBot.define do
  factory :doorkeeper_application, class: 'Doorkeeper::Application' do
    sequence(:name) { |n| "Application #{n}" }
    redirect_uri { 'urn:ietf:wg:oauth:2.0:oob' }
    sequence(:uid) { |n| "uid-#{n}" }
    sequence(:secret) { |n| "secret-#{n}" }
    scopes { '' }

    # Create specific applications by name
    factory :admin_application do
      name { 'Admin' }
      scopes { 'admin' }
    end

    factory :chatbot_application do
      name { 'ChatBot' }
      scopes { 'chatbot' }
    end

    factory :onboarding_application do
      name { 'Onboarding' }
      scopes { 'onboarding' }
    end
  end

  factory :doorkeeper_access_token, class: 'Doorkeeper::AccessToken' do
    sequence(:token) { |n| "access-token-#{n}" }
    association :application, factory: :doorkeeper_application
    expires_in { 2.hours }
    scopes { '' }
    created_at { Time.current }
    revoked_at { nil }

    # Create tokens with specific scopes
    factory :admin_token do
      association :application, factory: :admin_application
      scopes { 'admin' }
    end

    factory :chatbot_token do
      association :application, factory: :chatbot_application
      scopes { 'chatbot' }
    end

    factory :onboarding_token do
      association :application, factory: :onboarding_application
      scopes { 'onboarding' }
    end
  end
end 