FactoryBot.define do
  factory :energy_switch do
    reference_number { "ES#{Time.current.strftime('%Y%m%d')}#{SecureRandom.hex(3).upcase}" }
    status { :draft }
    association :switch_user
    association :address
    payment_method { nil }
    switch_date { 1.month.from_now }
    viewed_at { nil }
    
    # Define these as nil by default
    gas_energy_tariff_id { nil }
    electricity_energy_tariff_id { nil }
    
    trait :with_gas do
      association :gas_energy_tariff, factory: [:user_tariff, :gas]
    end
    
    trait :with_electricity do
      association :electricity_energy_tariff, factory: :user_tariff
    end
    
    trait :with_both do
      association :gas_energy_tariff, factory: [:user_tariff, :gas]
      association :electricity_energy_tariff, factory: :user_tariff
    end
    
    # Alternative naming for the same traits
    trait :with_gas_tariff do
      association :gas_energy_tariff, factory: [:user_tariff, :gas]
    end
    
    trait :with_electricity_tariff do
      association :electricity_energy_tariff, factory: :user_tariff
    end
    
    trait :with_both_tariffs do
      association :gas_energy_tariff, factory: [:user_tariff, :gas]
      association :electricity_energy_tariff, factory: :user_tariff
    end
    
    trait :draft do
      status { :draft }
    end
    
    trait :confirmed do
      status { :confirmed }
      association :switching_to_tariff, factory: :energy_tariff
    end
    
    trait :submitted_to_supplier do
      status { :submitted_to_supplier }
      association :switching_to_tariff, factory: :energy_tariff
    end
    
    trait :supplier_processing do
      status { :supplier_processing }
      association :switching_to_tariff, factory: :energy_tariff
    end
    
    trait :switched do
      status { :switched }
      association :switching_to_tariff, factory: :energy_tariff
    end
    
    trait :rejected_by_supplier do
      status { :rejected_by_supplier }
      association :switching_to_tariff, factory: :energy_tariff
    end
    
    trait :completed do
      status { 'completed' }
      completed_at { Time.current }
    end
    
    trait :cancelled do
      status { 'cancelled' }
      cancelled_at { Time.current }
    end

    # Define a relationship with a switching_to_tariff for UI tests
    transient do
      supplier { create(:supplier) }
    end
    
    after(:create) do |energy_switch, evaluator|
      # Only define the switching_to_tariff method if it's a draft (for non-draft, we use a real association)
      if energy_switch.status == 'draft'
        energy_switch.define_singleton_method(:switching_to_tariff) do
          OpenStruct.new(supplier: evaluator.supplier)
        end
      end
      
      # These override the actual model methods, which prevents testing the real behavior
      # Commenting them out so real methods will be used
      # energy_switch.define_singleton_method(:stages) do
      #   ["Submitted", "Processing", "Switching", "Complete"]
      # end
      
      # energy_switch.define_singleton_method(:rejected_by_supplier?) do
      #   false
      # end
      
      # energy_switch.define_singleton_method(:current_stage) do
      #   1
      # end
    end
  end
end 