FactoryBot.define do
  factory :supplier_submission do
    association :supplier
    submission_type { 'switch_request' }
    status { :pending }
    attempt_number { 1 }
    
    association :energy_switch, factory: [:energy_switch, :with_gas]
    
    trait :submitted do
      status { :submitted }
      submitted_at { Time.current }
    end
    
    trait :successful do
      status { :successful }
      submitted_at { 1.hour.ago }
      processed_at { Time.current }
      supplier_reference { "REF-#{SecureRandom.hex(8).upcase}" }
    end
    
    trait :failed do
      status { :failed }
      submitted_at { 1.hour.ago }
      processed_at { Time.current }
      error_message { "Connection error" }
    end
    
    trait :rejected do
      status { :rejected }
      submitted_at { 1.hour.ago }
      processed_at { Time.current }
      error_message { "Invalid data" }
      rejection_reason { "Address validation failed" }
    end
    
    trait :retry_scheduled do
      status { :retry_scheduled }
      submitted_at { 1.hour.ago }
      processed_at { Time.current }
      error_message { "Timeout" }
    end
    
    trait :with_request_data do
      raw_request { {
        customerFirstName: "John",
        customerLastName: "Doe",
        customerEmail: "<EMAIL>"
      }.to_json }
    end
    
    trait :with_response_data do
      raw_response { {
        customerQuoteId: "QUOTE-12345",
        status: "Success"
      }.to_json }
    end
  end
end 