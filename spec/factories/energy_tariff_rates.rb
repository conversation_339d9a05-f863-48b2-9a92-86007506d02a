FactoryBot.define do
  factory :energy_tariff_rate do
    fuel_type { :electricity }
    profile_class { 1 }
    gsp_code { "_C" }
    standing_charge_ex_vat { 25.0 }
    standing_charge_inc_vat { 26.25 }
    unit_rate_ex_vat { 15.0 }
    unit_rate_inc_vat { 15.75 }
    annual_bill { 500.0 }
    exit_fees { 0 }
    association :energy_tariff
    
    trait :economy7 do
      profile_class { 2 }
      day_unit_rate_ex_vat { 18.0 }
      day_unit_rate_inc_vat { 18.9 }
      night_unit_rate_ex_vat { 10.0 }
      night_unit_rate_inc_vat { 10.5 }
      weekend_unit_rate_ex_vat { 12.0 }
      weekend_unit_rate_inc_vat { 12.6 }
    end
    
    trait :gas do
      fuel_type { :gas }
      unit_rate_ex_vat { 5.0 }
      unit_rate_inc_vat { 5.25 }
      standing_charge_ex_vat { 20.0 }
      standing_charge_inc_vat { 21.0 }
      # gsp_code can be nil for gas, but we'll keep it for compatibility
    end
  end
end 