FactoryBot.define do
  factory :energy_tariff do
    tariff_name { "Standard Energy Plan" }
    display_name { "Standard Energy Plan" }
    product_code { "PROD#{rand(100000..999999)}" }
    energy_type { :electricity }
    tariff_type { :fixed }
    exit_fees { 0 }
    payment_methods { ["Direct Debit"] }
    effective_from { Date.today }
    duration { 12 }
    association :supplier
    
    factory :gas_energy_tariff do
      energy_type { :gas }
    end
    
    factory :electricity_energy_tariff do
      energy_type { :electricity }
    end
    
    factory :dual_energy_tariff do
      energy_type { :both }
    end
  end
end 