FactoryBot.define do
  factory :user_tariff do
    energy_type { :electricity }
    tariff_name { "My Current Tariff" }
    supplier_name { "Current Energy Provider" }
    meter_serial_number { "E#{rand(100000000..999999999)}" }
    unit_rate { 22.5 }
    standing_charge { 27.0 }
    electricity_monthly_usage { 250 }
    electricity_est_annual_usage { 3000 }
    profile_class { 1 }
    gsp_code { "_C" }
    meter_point_administration_number { "#{rand(**********..**********)}" }
    association :switch_user
    
    trait :gas do
      energy_type { :gas }
      meter_serial_number { "G#{rand(100000000..999999999)}" }
      unit_rate { 7.5 }
      standing_charge { 22.0 }
      gas_monthly_usage { 800 }
      gas_estimated_annual_usage { 9600 }
      electricity_monthly_usage { nil }
      electricity_est_annual_usage { nil }
      meter_point_reference_number { "#{rand(**********..**********)}" }
      meter_point_administration_number { nil }
    end
    
    trait :electricity do
      energy_type { :electricity }
      meter_point_administration_number { "#{rand(**********..**********)}" }
    end
    
    trait :economy7 do
      profile_class { 2 }
    end
  end
end 