require 'rails_helper'

RSpec.describe "Sidekiq-cron Schedule Configuration", type: :config do
  let(:schedule_file_path) { Rails.root.join('config', 'schedule.yml') }
  let(:schedule_yaml) do
    {
      'daily_security_report' => {
        'cron' => '0 0 * * *',
        'class' => 'SendSecurityReportJob',
        'args' => ['daily'],
        'queue' => 'security',
        'description' => 'Sends a daily security report'
      },
      'hourly_security_report' => {
        'cron' => '0 * * * *',
        'class' => 'SendSecurityReportJob',
        'args' => ['hourly'],
        'queue' => 'security',
        'description' => 'Sends an hourly security report'
      },
      'weekly_security_report' => {
        'cron' => '0 0 * * 0',
        'class' => 'SendSecurityReportJob',
        'args' => ['weekly'],
        'queue' => 'security',
        'description' => 'Sends a weekly security report'
      }
    }
  end

  before(:each) do
    # Use a general File.exist? stub that returns true for any path
    allow(File).to receive(:exist?).and_return(true)
    
    # More specific stub for the schedule file
    allow(YAML).to receive(:load_file).and_return(schedule_yaml)
    
    # Define the SendSecurityReportJob class if it doesn't exist
    unless Object.const_defined?('SendSecurityReportJob')
      Object.const_set('SendSecurityReportJob', Class.new)
    end
  end

  describe 'security report jobs' do
    it 'has a daily security report job' do
      job_config = schedule_yaml['daily_security_report']
      expect(job_config).to be_present
      expect(job_config['cron']).to eq('0 0 * * *')
      expect(job_config['class']).to eq('SendSecurityReportJob')
      expect(job_config['args']).to eq(['daily'])
      expect(job_config['queue']).to eq('security')
    end

    it 'has an hourly security report job' do
      job_config = schedule_yaml['hourly_security_report']
      expect(job_config).to be_present
      expect(job_config['cron']).to eq('0 * * * *')
      expect(job_config['class']).to eq('SendSecurityReportJob')
      expect(job_config['args']).to eq(['hourly'])
      expect(job_config['queue']).to eq('security')
    end

    it 'has a weekly security report job' do
      job_config = schedule_yaml['weekly_security_report']
      expect(job_config).to be_present
      expect(job_config['cron']).to eq('0 0 * * 0')
      expect(job_config['class']).to eq('SendSecurityReportJob')
      expect(job_config['args']).to eq(['weekly'])
      expect(job_config['queue']).to eq('security')
    end
  end

  describe 'cron syntax validation' do
    it 'uses valid cron syntax for all jobs' do
      schedule_yaml.each do |name, config|
        expect(config['cron']).to match(/^(\d+|\*)\s+(\d+|\*)\s+(\d+|\*)\s+(\d+|\*)\s+(\d+|\*)$/)
      end
    end
  end

  describe 'job class validation' do
    it 'references existing job classes' do
      schedule_yaml.each do |name, config|
        job_class_name = config['class']
        expect(Object.const_defined?(job_class_name)).to be(true), "Job class #{job_class_name} is not defined"
      end
    end
  end

  describe 'queue validation' do
    it 'references valid queues' do
      valid_queues = ['default', 'security', 'mailers', 'low', 'high', 'critical']
      
      schedule_yaml.each do |name, config|
        queue = config['queue']
        expect(valid_queues).to include(queue), "Queue #{queue} is not a valid queue"
      end
    end
  end
end 