require 'rails_helper'

RSpec.describe "Application Configuration", type: :request do
  before do
    # Make sure WebMock is completely disabled for these tests
    WebMock.allow_net_connect!
  end
  
  after do
    # Re-enable WebMock after the tests
    WebMock.disable_net_connect!(allow_localhost: true)
  end
  
  describe "API Configuration" do
    it "has api_only set to true" do
      expect(Rails.application.config.api_only).to be true
    end
    
    it "has sessions enabled despite being an API-only app" do
      middleware_classes = Rails.application.middleware.middlewares.map(&:klass)
      
      # Check that the key session middlewares are included
      expect(middleware_classes).to include(ActionDispatch::Cookies)
      expect(middleware_classes).to include(ActionDispatch::Session::CookieStore)
    end
    
    it "has CSRF protection disabled" do
      expect(Rails.application.config.action_controller.default_protect_from_forgery).to be false
    end
  end
end 