require 'rails_helper'

RSpec.describe "Sidekiq Initializer", type: :config do
  describe 'schedule loading' do
    let(:schedule_file) { Rails.root.join('config', 'schedule.yml') }
    
    context 'when Sideki<PERSON> is a server' do
      before do
        allow(Sidekiq).to receive(:server?).and_return(true)
        
        # More general stub for File.exist? that works for different path formats
        allow(File).to receive(:exist?) do |path|
          path.to_s.end_with?('schedule.yml') ? true : false
        end
        
        # Mock Sidekiq::Cron::Job to track if load_from_hash was called
        @original_load_from_hash = Sidekiq::Cron::Job.method(:load_from_hash)
        @load_called = false
        
        allow(Sidekiq::Cron::Job).to receive(:load_from_hash) do |hash|
          @load_called = true
          {}
        end
        
        # Mock YAML.load_file to prevent actual file reading
        sample_schedule = {
          'daily_security_report' => {
            'cron' => '0 0 * * *',
            'class' => 'SendSecurityReportJob',
            'args' => ['daily'],
            'queue' => 'security'
          }
        }
        allow(YAML).to receive(:load_file).and_return(sample_schedule)
        
        # Don't actually load the initializer, mock its behavior
        allow(Rails.root).to receive(:join).and_call_original
        
        # Simulate loading the initializer
        if Sidekiq.server? && File.exist?(schedule_file)
          Sidekiq::Cron::Job.load_from_hash(YAML.load_file(schedule_file))
        end
      end
      
      after do
        # Restore the original method
        if @original_load_from_hash
          allow(Sidekiq::Cron::Job).to receive(:load_from_hash).and_call_original
        end
      end
      
      it 'loads the schedule from the YAML file' do
        expect(@load_called).to be true
      end
    end
    
    context 'when Sidekiq is not a server' do
      before do
        allow(Sidekiq).to receive(:server?).and_return(false)
        
        # More general stub for File.exist?
        allow(File).to receive(:exist?) do |path|
          path.to_s.end_with?('schedule.yml') ? true : false
        end
        
        # Mock Sidekiq::Cron::Job to track if load_from_hash was called
        @original_load_from_hash = Sidekiq::Cron::Job.method(:load_from_hash)
        @load_called = false
        
        allow(Sidekiq::Cron::Job).to receive(:load_from_hash) do |hash|
          @load_called = true
          {}
        end
        
        # Simulate loading the initializer
        if Sidekiq.server? && File.exist?(schedule_file)
          Sidekiq::Cron::Job.load_from_hash(YAML.load_file(schedule_file))
        end
      end
      
      after do
        # Restore the original method
        if @original_load_from_hash
          allow(Sidekiq::Cron::Job).to receive(:load_from_hash).and_call_original
        end
      end
      
      it 'does not load the schedule' do
        expect(@load_called).to be false
      end
    end
    
    context 'when schedule file does not exist' do
      before do
        allow(Sidekiq).to receive(:server?).and_return(true)
        
        # More general stub for File.exist?
        allow(File).to receive(:exist?) do |path|
          path.to_s.end_with?('schedule.yml') ? false : true
        end
        
        # Mock Sidekiq::Cron::Job to track if load_from_hash was called
        @original_load_from_hash = Sidekiq::Cron::Job.method(:load_from_hash)
        @load_called = false
        
        allow(Sidekiq::Cron::Job).to receive(:load_from_hash) do |hash|
          @load_called = true
          {}
        end
        
        # Simulate loading the initializer
        if Sidekiq.server? && File.exist?(schedule_file)
          Sidekiq::Cron::Job.load_from_hash(YAML.load_file(schedule_file))
        end
      end
      
      after do
        # Restore the original method
        if @original_load_from_hash
          allow(Sidekiq::Cron::Job).to receive(:load_from_hash).and_call_original
        end
      end
      
      it 'does not load the schedule' do
        expect(@load_called).to be false
      end
    end
  end
  
  describe 'Redis configuration' do
    before do
      # Store original ENV
      @original_redis_url = ENV['REDIS_URL']
      
      # Mock Sidekiq configuration
      allow(Sidekiq).to receive(:configure_server)
      allow(Sidekiq).to receive(:configure_client)
    end
    
    after do
      # Restore ENV
      ENV['REDIS_URL'] = @original_redis_url
    end
    
    it 'configures Redis with ENV variable if present' do
      test_url = 'redis://test-host:1234'
      ENV['REDIS_URL'] = test_url
      
      # Create spies to track configuration
      server_config = spy('server_config')
      client_config = spy('client_config')
      
      # Mock the configure methods
      allow(Sidekiq).to receive(:configure_server).and_yield(server_config)
      allow(Sidekiq).to receive(:configure_client).and_yield(client_config)
      
      # Simulate the initializer behavior
      Sidekiq.configure_server { |config| config.redis(url: ENV['REDIS_URL'] || 'redis://redis:6379') }
      Sidekiq.configure_client { |config| config.redis(url: ENV['REDIS_URL'] || 'redis://redis:6379') }
      
      # Verify both configurations used the ENV variable
      expect(server_config).to have_received(:redis).with(hash_including(url: test_url))
      expect(client_config).to have_received(:redis).with(hash_including(url: test_url))
    end
    
    it 'uses default Redis URL if ENV variable not set' do
      ENV.delete('REDIS_URL')
      
      # Create spies to track configuration
      server_config = spy('server_config')
      client_config = spy('client_config')
      
      # Mock the configure methods
      allow(Sidekiq).to receive(:configure_server).and_yield(server_config)
      allow(Sidekiq).to receive(:configure_client).and_yield(client_config)
      
      # Simulate the initializer behavior
      Sidekiq.configure_server { |config| config.redis(url: ENV['REDIS_URL'] || 'redis://redis:6379') }
      Sidekiq.configure_client { |config| config.redis(url: ENV['REDIS_URL'] || 'redis://redis:6379') }
      
      # Verify both configurations used the default URL
      expect(server_config).to have_received(:redis).with(hash_including(url: 'redis://redis:6379'))
      expect(client_config).to have_received(:redis).with(hash_including(url: 'redis://redis:6379'))
    end
  end
end 