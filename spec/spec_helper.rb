# Set the Rails environment to test
ENV['RAILS_ENV'] = 'test'

require "vcr"
require 'webmock/rspec'
require 'json'

# This file was generated by the `rails generate rspec:install` command. Conventionally, all
# specs live under a `spec` directory, which <PERSON><PERSON> adds to the `$LOAD_PATH`.
# The generated `.rspec` file contains `--require spec_helper` which will cause
# this file to always be loaded, without a need to explicitly require it in any
# files.
#
# Given that it is always loaded, you are encouraged to keep this file as
# light-weight as possible. Requiring heavyweight dependencies from this file
# will add to the boot time of your test suite on EVERY test run, even for an
# individual file that may not need all of that loaded. Instead, consider making
# a separate helper file that requires the additional dependencies and performs
# the additional setup, and require it from the spec files that actually need
# it.
#
# See https://rubydoc.info/gems/rspec-core/RSpec/Core/Configuration
# 

# Allow WebMock to pass through requests to localhost and Rails app
WebMock.disable_net_connect!(allow_localhost: true)

# Completely disable <PERSON>Mock for specific request tests
RSpec.configure do |config|
  config.before(:each, type: :request) do
    WebMock.allow_net_connect!
  end
  
  config.after(:each, type: :request) do
    WebMock.disable_net_connect!(allow_localhost: true)
  end
end

VCR.configure do |config|
  config.cassette_library_dir = 'spec/vcr_cassettes'
  config.hook_into :webmock
  config.configure_rspec_metadata!

  # Filter out sensitive information
  config.filter_sensitive_data('<SUBSCRIPTION_KEY>') { ENV['XOSERVE_SUBSCRIPTION_KEY'] }
  config.filter_sensitive_data('<CLIENT_ID>') { ENV['XOSERVE_CLIENT_ID'] }
  config.filter_sensitive_data('<CLIENT_SECRET>') { ENV['XOSERVE_CLIENT_SECRET'] }
  config.filter_sensitive_data('<AUTH_KEY>') { ENV['TULO_API_KEY'] }
  config.filter_sensitive_data('<TOMATO_API_KEY>') { ENV['TOMATO_ENERGY_API_KEY'] }
  config.filter_sensitive_data('<ACCESS_TOKEN>') do |interaction|
    if interaction.response.body.match?(/access_token/)
      JSON.parse(interaction.response.body)['access_token']
    end
  end

  # Ignore certain headers in the cassettes
  config.before_record do |interaction|
    interaction.request.headers.delete('Authorization')
    interaction.request.headers.delete('X-API-Key')
    interaction.request.headers.delete('x-api-key')
  end
end

RSpec.configure do |config|
  # Use color in STDOUT
  config.color = true

  # Use color not only in STDOUT but also in pagers and files
  config.tty = true

  # Use the specified formatter
  config.formatter = :documentation # :progress, :html, :json, custom

  # Run specs in random order to surface order dependencies
  config.order = :random
  Kernel.srand config.seed

  # Disable RSpec exposing methods globally on `Module` and `main`
  config.disable_monkey_patching!

  # Disable RSpec exposing methods globally on `Module` and `main`
  config.expose_dsl_globally = false

  # Many RSpec users commonly either run the entire suite or an individual
  # file, and it's useful to allow more verbose output when running an
  # individual spec file.
  if config.files_to_run.one?
    # Use the documentation formatter for detailed output
    config.formatter = :documentation
  end

  # Print the 10 slowest examples and example groups
  config.profile_examples = 10

  # Run only the specs that have been specified on the command line
  config.filter_run :focus
  config.run_all_when_everything_filtered = true

  # Allows tagging a test with `:skip` to exclude it from being run
  config.filter_run_excluding :skip

  # Exclude tests with a specific tag (e.g., :slow) by default
  # config.filter_run_excluding slow: true

  # Enable the focus filter to run only focused specs
  config.filter_run_when_matching :focus

  # Enable verbose output when running a single spec file
  config.default_formatter = 'doc' if config.files_to_run.one?

  # Enable warnings
  config.warnings = false

  # Only allow expect syntax
  config.expect_with :rspec do |expectations|
    expectations.include_chain_clauses_in_custom_matcher_descriptions = true
    expectations.syntax = :expect
  end

  # Mock with RSpec
  config.mock_with :rspec do |mocks|
    mocks.verify_partial_doubles = true
  end

  # Configure shared context and examples
  config.shared_context_metadata_behavior = :apply_to_host_groups
end
