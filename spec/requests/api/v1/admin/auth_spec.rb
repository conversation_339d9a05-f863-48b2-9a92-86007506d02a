require 'rails_helper'

RSpec.describe "Admin Authentication", type: :request do
  before do
    # Make sure WebMock is completely disabled for these tests
    WebMock.allow_net_connect!
    
    # Create the Admin application for Doorkeeper if it doesn't exist
    @admin_app = Doorkeeper::Application.find_or_create_by!(name: 'Admin', redirect_uri: 'urn:ietf:wg:oauth:2.0:oob', scopes: 'admin')
    puts "Admin app created with ID: #{@admin_app.id}"
  end
  
  after do
    # Re-enable WebMock after the tests
    WebMock.disable_net_connect!(allow_localhost: true)
  end
  
  let!(:admin) { create(:admin, email: "<EMAIL>", password: "password123") }
  
  describe "POST /api/v1/admin/auth/login" do
    it "logs in successfully with valid credentials" do
      post "/api/v1/admin/auth/login", params: { email: admin.email, password: "password123" }
      
      expect(response).to have_http_status(:ok)
      
      json = JSON.parse(response.body)
      expect(json["success"]).to be true
      puts "Login response: #{json.inspect}"
    end
    
    it "returns unauthorized with invalid credentials" do
      post "/api/v1/admin/auth/login", params: { email: admin.email, password: "wrong_password" }
      
      expect(response).to have_http_status(:unauthorized)
      
      json = JSON.parse(response.body)
      expect(json["success"]).to be false
    end
  end
  
  describe "GET /api/v1/admin/auth/me" do
    it "returns admin info when logged in" do
      # First login
      post "/api/v1/admin/auth/login", params: { email: admin.email, password: "password123" }
      
      # Get the token from the response
      json_response = JSON.parse(response.body)
      token = json_response["access_token"]
      puts "Login response for /me test: #{json_response.inspect}"
      puts "Token for /me test: #{token}"
      
      # Verify the token exists in the database
      access_token = Doorkeeper::AccessToken.find_by(token: token)
      puts "Token found in DB? #{access_token.present?}"
      puts "Token record: #{access_token.inspect}" if access_token
      puts "Application: #{access_token.application.inspect}" if access_token
      
      # Then check me endpoint with Authorization header
      get "/api/v1/admin/auth/me", headers: { "Authorization" => "Bearer #{token}" }
      
      puts "ME response status: #{response.status}"
      puts "ME response body: #{response.body}"
      
      expect(response).to have_http_status(:ok)
      
      json = JSON.parse(response.body)
      expect(json["success"]).to be true
      expect(json["admin"]["id"]).to eq(admin.id)
    end
    
    it "returns unauthorized when not logged in" do
      get "/api/v1/admin/auth/me"
      
      expect(response).to have_http_status(:unauthorized)
      
      json = JSON.parse(response.body)
      expect(json["success"]).to be false
    end
  end
  
  describe "DELETE /api/v1/admin/auth/logout" do
    it "logs out successfully" do
      # First login
      post "/api/v1/admin/auth/login", params: { email: admin.email, password: "password123" }
      
      # Get the token from the response
      token = JSON.parse(response.body)["access_token"]
      
      # Then logout with Authorization header
      delete "/api/v1/admin/auth/logout", headers: { "Authorization" => "Bearer #{token}" }
      
      expect(response).to have_http_status(:ok)
      
      json = JSON.parse(response.body)
      expect(json["success"]).to be true
      
      # Verify logged out by checking me endpoint
      get "/api/v1/admin/auth/me", headers: { "Authorization" => "Bearer #{token}" }
      expect(response).to have_http_status(:unauthorized)
    end
  end
  
  describe "Session behavior" do
    it "maintains user session across requests" do
      # Login
      post "/api/v1/admin/auth/login", params: { email: admin.email, password: "password123" }
      expect(response).to have_http_status(:ok)
      
      # Get the token from the response
      json_response = JSON.parse(response.body)
      token = json_response["access_token"]
      puts "Login response for session test: #{json_response.inspect}"
      puts "Token for session test: #{token}"
      
      # Verify the token exists in the database
      access_token = Doorkeeper::AccessToken.find_by(token: token)
      puts "Session test - Token found in DB? #{access_token.present?}"
      puts "Session test - Token record: #{access_token.inspect}" if access_token
      
      # Access protected endpoint with Authorization header
      get "/api/v1/admin/auth/me", headers: { "Authorization" => "Bearer #{token}" }
      puts "Session test - ME response status: #{response.status}"
      puts "Session test - ME response body: #{response.body}"
      
      expect(response).to have_http_status(:ok)
      
      # Logout with Authorization header
      delete "/api/v1/admin/auth/logout", headers: { "Authorization" => "Bearer #{token}" }
      expect(response).to have_http_status(:ok)
      
      # Verify session is gone
      get "/api/v1/admin/auth/me", headers: { "Authorization" => "Bearer #{token}" }
      expect(response).to have_http_status(:unauthorized)
    end
  end
end
