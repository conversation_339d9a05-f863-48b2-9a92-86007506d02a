require 'rails_helper'

RSpec.describe EmailValidator do
  describe '.valid?' do
    context 'with valid email formats' do
      it 'returns true for simple email addresses' do
        expect(EmailValidator.valid?('<EMAIL>')).to be true
      end

      it 'returns true for email addresses with subdomains' do
        expect(EmailValidator.valid?('<EMAIL>')).to be true
      end

      it 'returns true for email addresses with plus signs' do
        expect(EmailValidator.valid?('<EMAIL>')).to be true
      end

      it 'returns true for email addresses with dots in local part' do
        expect(EmailValidator.valid?('<EMAIL>')).to be true
      end

      it 'returns true for email addresses with dashes' do
        expect(EmailValidator.valid?('<EMAIL>')).to be true
      end

      it 'returns true for email addresses with underscores' do
        expect(EmailValidator.valid?('<EMAIL>')).to be true
      end

      it 'returns true for email addresses with uppercase characters' do
        expect(EmailValidator.valid?('<EMAIL>')).to be true
      end
    end

    context 'with invalid email formats' do
      it 'returns false for emails without @ symbol' do
        expect(EmailValidator.valid?('userexample.com')).to be false
      end

      it 'returns false for emails without domain part' do
        expect(EmailValidator.valid?('user@')).to be false
      end

      it 'returns false for emails without local part' do
        expect(EmailValidator.valid?('@example.com')).to be false
      end

      it 'returns false for emails with spaces' do
        expect(EmailValidator.valid?('user @example.com')).to be false
      end

      it 'returns false for emails with invalid characters' do
        expect(EmailValidator.valid?('user*@example.com')).to be false
      end

      it 'returns false for emails with multiple @ symbols' do
        expect(EmailValidator.valid?('user@<EMAIL>')).to be false
      end

      it 'returns false for emails with missing top-level domain' do
        expect(EmailValidator.valid?('user@example')).to be false
      end
    end

    context 'with edge cases' do
      it 'returns false for nil values' do
        expect(EmailValidator.valid?(nil)).to be false
      end

      it 'returns false for empty strings' do
        expect(EmailValidator.valid?('')).to be false
      end

      it 'returns false for strings with only whitespace' do
        expect(EmailValidator.valid?('   ')).to be false
      end
    end
  end
end 