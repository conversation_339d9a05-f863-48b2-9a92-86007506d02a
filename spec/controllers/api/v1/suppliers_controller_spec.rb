require 'rails_helper'

RSpec.describe Api::V1::SuppliersController, type: :controller do
  let(:admin) { create(:admin) }
  let(:admin_token) { create(:doorkeeper_access_token, resource_owner_id: admin.id, scopes: 'admin', application: create(:doorkeeper_application, name: 'Admin')) }
  let(:chatbot_token) { create(:doorkeeper_access_token, scopes: 'chatbot', application: create(:doorkeeper_application, name: 'Cha<PERSON><PERSON><PERSON>')) }

  describe 'GET #index' do
    context 'with valid admin token' do
      before do
        # Get the current count of suppliers instead of trying to reset them
        @supplier_count = Supplier.count
        
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        get :index
      end

      it 'returns 200 status code' do
        expect(response).to have_http_status(:ok)
      end

      it 'returns all suppliers' do
        json_response = JSON.parse(response.body)
        # Check that we're getting all the suppliers
        expect(json_response.size).to eq(@supplier_count)
      end
    end

    context 'with chatbot token (wrong scope)' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        get :index
      end

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with no token' do
      before { get :index }

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET #show' do
    let(:supplier) { Supplier.first || create(:supplier) } # Use an existing supplier or create one if needed

    context 'with valid admin token' do
      before do
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        get :show, params: { id: supplier.id }
      end

      it 'returns 200 status code' do
        expect(response).to have_http_status(:ok)
      end

      it 'returns the correct supplier' do
        json_response = JSON.parse(response.body)
        expect(json_response['id']).to eq(supplier.id)
      end
    end

    context 'with no token' do
      before { get :show, params: { id: supplier.id } }

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end 