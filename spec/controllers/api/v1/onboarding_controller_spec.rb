require 'rails_helper'

RSpec.describe Api::V1::OnboardingController, type: :controller do
  # Helper method to parse JSON response
  let(:json_response) { JSON.parse(response.body, symbolize_names: true) }

  let(:chatbot_token) { create(:chatbot_token) }
  let(:admin_token) { create(:admin_token, resource_owner_id: create(:admin).id) }
  let(:onboarding_token) { create(:onboarding_token, resource_owner_id: create(:switch_user).id) }

  before do
    # Mock the SendOnboardingVerificationCodeJob perform_async method
    allow(SendOnboardingVerificationCodeJob).to receive(:perform_async).and_return(true)
    
    # Mock Rails.cache methods
    allow(Rails.cache).to receive(:fetch).and_return(0)
    allow(Rails.cache).to receive(:write).and_return(true)
    allow(Rails.cache).to receive(:read).and_return(nil)
    allow(Rails.cache).to receive(:delete).and_return(true)
    
    # Mock Rails.logger
    allow(Rails.logger).to receive(:info).and_return(true)
    allow(Rails.logger).to receive(:warn).and_return(true)
    allow(Rails.logger).to receive(:error).and_return(true)
    
    # Mock EmailValidator
    allow(EmailValidator).to receive(:valid?).and_return(true)
    
    # Mock SecurityMonitoringService methods
    allow(SecurityMonitoringService).to receive(:record_verification_attempt).and_return(true)
    allow(SecurityMonitoringService).to receive(:record_code_send).and_return(true)
    allow(SecurityMonitoringService).to receive(:record_rate_limit_triggered).and_return(true)
    
    # Mock request.remote_ip
    allow_any_instance_of(ActionDispatch::Request).to receive(:remote_ip).and_return('***********')
    
    # Mock SecureRandom to generate a predictable code
    allow(SecureRandom).to receive(:random_number).and_return(123456 - 100000)
  end

  describe 'POST #send_verification_code' do
    let(:valid_params) { { onboarding: { email: '<EMAIL>' } } }

    context 'with valid chatbot token' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        post :send_verification_code, params: valid_params
      end

      it 'returns 200 status code' do
        expect(response).to have_http_status(:ok)
      end

      it 'triggers the verification code job' do
        expect(SendOnboardingVerificationCodeJob).to have_received(:perform_async).with('<EMAIL>', 123456)
      end
      
      it 'returns success message' do
        expect(json_response[:message]).to eq('Verification code sent successfully')
      end
      
      it 'logs the verification attempt' do
        expect(Rails.logger).to have_received(:info).with("Verification code sent to email: <EMAIL>")
      end
      
      it 'stores the code in cache' do
        expect(Rails.cache).to have_received(:write).with("verification_code:<EMAIL>", 123456, expires_in: 10.minutes)
      end
      
      it 'updates the rate limiting counter' do
        expect(Rails.cache).to have_received(:write).with("verification_send_attempts:<EMAIL>", 1, expires_in: 24.hours)
      end
      
      it 'records the code send in the monitoring service' do
        expect(SecurityMonitoringService).to have_received(:record_code_send).with('<EMAIL>', '***********')
      end
    end
    
    context 'with valid admin token (should also work)' do
      before do
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        
        # Mock TokenService to allow admin token for chatbot scope
        allow(TokenService).to receive(:verify_token_scope)
          .with(admin_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        post :send_verification_code, params: valid_params
      end

      it 'returns 200 status code' do
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with onboarding token (wrong scope)' do
      before do
        request.headers['Authorization'] = "Bearer #{onboarding_token.token}"
        
        # Mock TokenService to reject onboarding token for chatbot scope
        allow(TokenService).to receive(:verify_token_scope)
          .with(onboarding_token.token, 'chatbot', 'ChatBot')
          .and_return(false)
        
        post :send_verification_code, params: valid_params
      end

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with no token' do
      before { 
        # Mock TokenService to reject nil token
        allow(TokenService).to receive(:verify_token_scope)
          .with(nil, 'chatbot', 'ChatBot')
          .and_return(false)
          
        post :send_verification_code, params: valid_params 
      }

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with blank email' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
          
        post :send_verification_code, params: { onboarding: { email: '' } }
      end

      it 'returns 422 unprocessable entity' do
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns error message' do
        expect(json_response[:error]).to eq('Email is required')
      end
    end
    
    context 'with invalid email format' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        # Mock EmailValidator to reject invalid email
        allow(EmailValidator).to receive(:valid?).and_return(false)
          
        post :send_verification_code, params: { onboarding: { email: 'invalid-email' } }
      end

      it 'returns 422 unprocessable entity' do
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns error message' do
        expect(json_response[:error]).to eq('Invalid email format')
      end
    end
    
    context 'when rate limiting is exceeded' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        # Mock Rails.cache fetch to simulate exceeded limit
        allow(Rails.cache).to receive(:fetch).and_return(11)
          
        post :send_verification_code, params: valid_params
      end

      it 'returns 429 too many requests' do
        expect(response).to have_http_status(:too_many_requests)
      end

      it 'returns error message' do
        expect(json_response[:error]).to eq('Too many code requests. Please try again later')
      end
      
      it 'logs the warning' do
        expect(Rails.logger).to have_received(:warn).with(/Too many verification code requests/)
      end
      
      it 'records the rate limit in the monitoring service' do
        expect(SecurityMonitoringService).to have_received(:record_rate_limit_triggered).with('<EMAIL>', '***********', 'verification_code_send')
      end
    end
    
    context 'when within cooldown period' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        # Mock Rails.cache read to simulate recent request
        allow(Rails.cache).to receive(:read).and_return(Time.now.utc)
          
        post :send_verification_code, params: valid_params
      end

      it 'returns 429 too many requests' do
        expect(response).to have_http_status(:too_many_requests)
      end

      it 'returns error message' do
        expect(json_response[:error]).to eq('Please wait before requesting another code')
      end
    end
  end
  
  describe 'POST #verify_code' do
    let(:valid_params) { { verification: { email: '<EMAIL>', code: '123456' } } }
    
    context 'with valid token and correct code' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
          
        # Mock Rails.cache to find the stored code
        allow(Rails.cache).to receive(:read).with("verification_code:<EMAIL>").and_return(123456)
        
        # Mock secure comparison to succeed
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        
        post :verify_code, params: valid_params
      end
      
      it 'returns 200 status code' do
        expect(response).to have_http_status(:ok)
      end
      
      it 'indicates verification was successful' do
        expect(json_response[:verified]).to eq(true)
      end
      
      it 'deletes the verification code from cache' do
        expect(Rails.cache).to have_received(:delete).with("verification_code:<EMAIL>")
      end
      
      it 'deletes the attempts counter from cache' do
        expect(Rails.cache).to have_received(:delete).with("verification_attempts:<EMAIL>")
      end
      
      it 'records successful verification in the monitoring service' do
        expect(SecurityMonitoringService).to have_received(:record_verification_attempt).with('<EMAIL>', '***********', true)
      end
    end
    
    context 'with invalid code' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
          
        # Mock Rails.cache to find the stored code
        allow(Rails.cache).to receive(:read).with("verification_code:<EMAIL>").and_return(654321)
        
        # Mock secure comparison to fail
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(false)
        
        post :verify_code, params: valid_params
      end
      
      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'returns error message' do
        expect(json_response[:error]).to eq('Invalid or expired verification code')
      end
      
      it 'records failed verification in the monitoring service' do
        expect(SecurityMonitoringService).to have_received(:record_verification_attempt).with('<EMAIL>', '***********', false)
      end
    end
    
    context 'with expired code' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
          
        # Mock Rails.cache to simulate expired code
        allow(Rails.cache).to receive(:read).with("verification_code:<EMAIL>").and_return(nil)
        
        post :verify_code, params: valid_params
      end
      
      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'returns error message' do
        expect(json_response[:error]).to eq('Invalid or expired verification code')
      end
    end
    
    context 'with blank email or code' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
          
        post :verify_code, params: { verification: { email: '<EMAIL>', code: '' } }
      end
      
      it 'returns 422 unprocessable entity' do
        expect(response).to have_http_status(:unprocessable_entity)
      end
      
      it 'returns error message' do
        expect(json_response[:error]).to eq('Email and verification code are required')
      end
    end
    
    context 'with invalid email format' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        # Mock EmailValidator to reject invalid email
        allow(EmailValidator).to receive(:valid?).and_return(false)
          
        post :verify_code, params: { verification: { email: 'invalid-email', code: '123456' } }
      end
      
      it 'returns 422 unprocessable entity' do
        expect(response).to have_http_status(:unprocessable_entity)
      end
      
      it 'returns error message' do
        expect(json_response[:error]).to eq('Invalid email format')
      end
    end
    
    context 'when rate limiting is exceeded' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        
        # Mock TokenService to allow chatbot token
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        # Mock Rails.cache to simulate exceeded verification attempts
        allow(Rails.cache).to receive(:fetch).and_return(5)
          
        post :verify_code, params: valid_params
      end
      
      it 'returns 429 too many requests' do
        expect(response).to have_http_status(:too_many_requests)
      end
      
      it 'returns error message' do
        expect(json_response[:error]).to eq('Too many attempts. Please try again later')
      end
      
      it 'logs the warning' do
        expect(Rails.logger).to have_received(:warn).with(/Too many verification attempts/)
      end
      
      it 'records the rate limit in the monitoring service' do
        expect(SecurityMonitoringService).to have_received(:record_rate_limit_triggered).with('<EMAIL>', '***********', 'verification_attempt')
      end
    end
  end

  describe 'POST #create' do
    let(:valid_params) do
      {
        onboarding: {
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          phone_number: '07123456789',
          postcode: 'W1T 1JY',
          address: '1 Test Street',
          posttown: 'London',
          street: 'Test Street',
          number: '1',
          flat: '',
          fuel_type: 'both',
          supplier_name: 'Test Energy',
          payment_method: 'direct_debit',
          product_type: 'fixed',
          gas_meter_serial_number: '*********',
          gas_unit_rate: '3.5',
          gas_standing_charge: '25.0',
          gas_estimated_annual_usage: '12000',
          gas_monthly_usage: '1000',
          meter_point_reference_number: '1234567890',
          electricity_meter_serial_number: 'E12345678',
          electricity_unit_rate: '15.5',
          electricity_standing_charge: '24.0',
          electricity_est_annual_usage: '3600',
          electricity_monthly_usage: '300'
        }
      }
    end

    before do
      # Mock external API calls
      allow_any_instance_of(XoserveElectricityApiService).to receive(:search_utility_address)
        .and_return({ mpan: "1234567890123", gsp_code: "10" })
      allow_any_instance_of(XoserveElectricityApiService).to receive(:get_technical_details_by_mpan)
        .and_return({ profile_class: "01" })
      
      # Mock token generation
      allow(TokenService).to receive(:generate_onboarding_token)
        .and_return(double("AccessToken", token: "abc123"))
      
      # Authorize API request
      allow(controller).to receive(:authorize_trusted_request).and_return(true)
    end

    context 'when creating a new energy switch' do
      it 'creates a new switch_user if one does not exist' do
        expect {
          post :create, params: valid_params
        }.to change(SwitchUser, :count).by(1)
        
        expect(response).to have_http_status(:created)
        expect(json_response[:onboarding_url]).to include("/onboarding/energy-switch/")
      end

      it 'creates address, energy tariffs, and energy switch' do
        expect {
          post :create, params: valid_params
        }.to change(Address, :count).by(1)
          .and change(UserTariff, :count).by(2)
          .and change(EnergySwitch, :count).by(1)
        
        expect(response).to have_http_status(:created)
        
        # Verify the energy switch has the correct associations
        energy_switch = EnergySwitch.last
        expect(energy_switch.status).to eq("draft")
        expect(energy_switch.gas_energy_tariff).to be_present
        expect(energy_switch.electricity_energy_tariff).to be_present
      end
      
      it 'uses existing switch_user if one exists with the same email' do
        # Create user first
        existing_user = create(:switch_user, email: '<EMAIL>')
        
        expect {
          post :create, params: valid_params
        }.not_to change(SwitchUser, :count)
        
        expect(response).to have_http_status(:created)
        
        # Verify the energy switch is associated with the existing user
        energy_switch = EnergySwitch.last
        expect(energy_switch.switch_user).to eq(existing_user)
      end
    end
    
    context 'when an existing draft energy switch with matching meters exists' do
      let!(:existing_switch_user) { create(:switch_user, email: '<EMAIL>') }
      let!(:existing_address) { create(:address, switch_user: existing_switch_user) }
      let!(:existing_gas_tariff) { create(:user_tariff, 
        switch_user: existing_switch_user, 
        energy_type: :gas,
        meter_serial_number: '*********'
      )}
      let!(:existing_energy_switch) { create(:energy_switch, 
        switch_user: existing_switch_user, 
        address: existing_address,
        gas_energy_tariff: existing_gas_tariff,
        status: :draft
      )}
      
      it 'returns the existing switch instead of creating a new one' do
        # Mock TokenService to return a valid JWT token
        allow(TokenService).to receive(:generate_onboarding_jwt)
          .with(existing_energy_switch)
          .and_return('mock-jwt-token')
          
        # Mock ENV to return a valid SERVER_HOST
        allow(ENV).to receive(:[]).with('SERVER_HOST').and_return('https://example.com')
        
        expect {
          post :create, params: valid_params
        }.not_to change(EnergySwitch, :count)
        
        expect(response).to have_http_status(:ok)
        expect(json_response[:onboarding_url]).to include('mock-jwt-token')
      end
    end
    
    context 'with validation errors' do
      let(:invalid_params) do
        # Invalid postcode format
        params = valid_params.deep_dup
        params[:onboarding][:postcode] = 'INVALID'
        params
      end
      
      it 'returns validation error with proper status code' do
        post :create, params: invalid_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response[:error]).to include("Validation failed")
      end
    end
    
    context 'with general errors' do
      before do
        # Simulate a general error
        allow_any_instance_of(Api::V1::OnboardingController).to receive(:process_switch_user)
          .and_raise(StandardError, "Something went wrong")
      end
      
      it 'handles general errors gracefully' do
        post :create, params: valid_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response[:error]).to eq("An error occurred during processing")
      end
    end
  end

  describe '#process_user_tariffs' do
    let(:switch_user) { create(:switch_user) }
    let(:address) { create(:address, switch_user: switch_user) }
    let(:energy_switch) { build(:energy_switch, switch_user: switch_user, address: address) }
    let(:controller_instance) { controller }

    before do
      # Set up common parameters
      allow(controller).to receive(:onboarding_params).and_return({
        "fuel_type" => "both",
        "supplier_name" => "Test Supplier",
        "payment_method" => "direct_debit",
        "product_type" => "fixed",
        "gas_meter_serial_number" => "*********",
        "gas_unit_rate" => "3.5",
        "gas_standing_charge" => "25.0",
        "gas_estimated_annual_usage" => "12000",
        "gas_monthly_usage" => "1000",
        "meter_point_reference_number" => "1234567890",
        "electricity_meter_serial_number" => "E12345678",
        "electricity_unit_rate" => "15.5",
        "electricity_standing_charge" => "24.0",
        "electricity_est_annual_usage" => "3600",
        "electricity_monthly_usage" => "300"
      })

      # Mock external API calls in create_electricity_tariff
      allow_any_instance_of(XoserveElectricityApiService).to receive(:search_utility_address)
        .and_return({ mpan: "1234567890123", gsp_code: "10" })
      allow_any_instance_of(XoserveElectricityApiService).to receive(:get_technical_details_by_mpan)
        .and_return({ profile_class: "01" })
    end

    context 'with fuel_type "gas"' do
      before do
        allow(controller).to receive(:onboarding_params).and_return(
          controller.send(:onboarding_params).merge("fuel_type" => "gas")
        )
      end

      it 'creates only gas tariff' do
        result = controller.send(:process_user_tariffs, energy_switch)
        
        expect(result.gas_energy_tariff).to be_present
        expect(result.gas_energy_tariff.energy_type).to eq("gas")
        expect(result.gas_energy_tariff.meter_serial_number).to eq("*********")
        expect(result.electricity_energy_tariff).to be_nil
      end
    end

    context 'with fuel_type "electricity"' do
      before do
        allow(controller).to receive(:onboarding_params).and_return(
          controller.send(:onboarding_params).merge("fuel_type" => "electricity")
        )
      end

      it 'creates only electricity tariff' do
        result = controller.send(:process_user_tariffs, energy_switch)
        
        expect(result.electricity_energy_tariff).to be_present
        expect(result.electricity_energy_tariff.energy_type).to eq("electricity")
        expect(result.electricity_energy_tariff.meter_serial_number).to eq("E12345678")
        expect(result.gas_energy_tariff).to be_nil
      end
    end

    context 'with fuel_type "both"' do
      it 'creates both gas and electricity tariffs' do
        result = controller.send(:process_user_tariffs, energy_switch)
        
        expect(result.gas_energy_tariff).to be_present
        expect(result.gas_energy_tariff.energy_type).to eq("gas")
        expect(result.electricity_energy_tariff).to be_present
        expect(result.electricity_energy_tariff.energy_type).to eq("electricity")
      end
    end

    context 'with invalid fuel_type' do
      before do
        allow(controller).to receive(:onboarding_params).and_return(
          controller.send(:onboarding_params).merge("fuel_type" => "invalid")
        )
      end

      it 'defaults to "both" and creates both tariffs' do
        expect(Rails.logger).to receive(:warn).with("Invalid fuel type provided: invalid")
        
        result = controller.send(:process_user_tariffs, energy_switch)
        
        expect(result.gas_energy_tariff).to be_present
        expect(result.electricity_energy_tariff).to be_present
      end
    end
  end

  describe '#generate_onboarding_url' do
    let(:switch_user) { create(:switch_user) }
    let(:energy_switch) { create(:energy_switch, :with_electricity_tariff, switch_user: switch_user) }
    let(:jwt_token) { 'test-jwt-token' }
    let(:server_host) { 'https://example.com' }
    
    before do
      # Make the method accessible for testing
      described_class.send(:public, :generate_onboarding_url)
      
      # Mock ENV for SERVER_HOST
      allow(ENV).to receive(:[]).with('SERVER_HOST').and_return(server_host)
      
      # Mock TokenService
      allow(TokenService).to receive(:generate_onboarding_jwt)
        .with(energy_switch)
        .and_return(jwt_token)
    end
    
    after do
      # Reset method visibility
      described_class.send(:private, :generate_onboarding_url)
    end
    
    it 'returns URL with token as query parameter' do
      expected_url = "#{server_host}/onboarding?token=#{jwt_token}"
      url = controller.generate_onboarding_url(energy_switch)
      expect(url).to eq(expected_url)
    end
    
    it 'raises an error when SERVER_HOST is not set' do
      allow(ENV).to receive(:[]).with('SERVER_HOST').and_return(nil)
      
      expect {
        controller.generate_onboarding_url(energy_switch)
      }.to raise_error(StandardError, "Server configuration error")
    end
    
    it 'raises an error when token generation fails' do
      allow(TokenService).to receive(:generate_onboarding_jwt)
        .with(energy_switch)
        .and_return(nil)
      
      expect {
        controller.generate_onboarding_url(energy_switch)
      }.to raise_error(StandardError, "Failed to generate onboarding token")
    end
  end
end 