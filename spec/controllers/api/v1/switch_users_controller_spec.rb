require 'rails_helper'

RSpec.describe Api::V1::SwitchUsersController, type: :controller do
  let(:admin) { create(:admin) }
  let(:admin_token) { create(:doorkeeper_access_token, resource_owner_id: admin.id, scopes: 'admin', application: create(:doorkeeper_application, name: 'Admin')) }
  let(:chatbot_token) { create(:doorkeeper_access_token, scopes: 'chatbot', application: create(:doorkeeper_application, name: '<PERSON><PERSON><PERSON><PERSON>')) }
  let(:onboarding_token) { create(:doorkeeper_access_token, resource_owner_id: create(:switch_user).id, scopes: 'onboarding', application: create(:doorkeeper_application, name: 'Onboarding')) }

  describe 'GET #index' do
    context 'with valid admin token' do
      before do
        @switch_user_count = SwitchUser.count
        
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        get :index
      end

      it 'returns 200 status code' do
        expect(response).to have_http_status(:ok)
      end

      it 'returns all switch users' do
        json_response = JSON.parse(response.body)
        expect(json_response.size).to eq(@switch_user_count)
      end
    end

    context 'with chatbot token (wrong scope)' do
      before do
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        get :index
      end

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with onboarding token (wrong scope)' do
      before do
        request.headers['Authorization'] = "Bearer #{onboarding_token.token}"
        get :index
      end

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with no token' do
      before { get :index }

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['Authorization'] = "Bearer invalid_token"
        get :index
      end

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with expired token' do
      let(:expired_token) { create(:doorkeeper_access_token, resource_owner_id: admin.id, scopes: 'admin', application: create(:doorkeeper_application, name: 'Admin'), expires_in: -1.day) }

      before do
        request.headers['Authorization'] = "Bearer #{expired_token.token}"
        get :index
      end

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with revoked token' do
      let(:revoked_token) { create(:doorkeeper_access_token, resource_owner_id: admin.id, scopes: 'admin', application: create(:doorkeeper_application, name: 'Admin'), revoked_at: Time.current) }

      before do
        request.headers['Authorization'] = "Bearer #{revoked_token.token}"
        get :index
      end

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET #show' do
    let(:switch_user) { SwitchUser.first || create(:switch_user) }

    context 'with valid admin token' do
      before do
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        get :show, params: { id: switch_user.id }
      end

      it 'returns 200 status code' do
        expect(response).to have_http_status(:ok)
      end

      it 'returns the correct switch user' do
        json_response = JSON.parse(response.body)
        expect(json_response['id']).to eq(switch_user.id)
      end
    end

    context 'with no token' do
      before { get :show, params: { id: switch_user.id } }

      it 'returns 401 unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end 