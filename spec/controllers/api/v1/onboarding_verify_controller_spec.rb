require 'rails_helper'

RSpec.describe Api::V1::OnboardingVerifyController, type: :controller do
  describe 'GET #verify' do
    let(:switch_user) { create(:switch_user) }
    let(:energy_switch) { create(:energy_switch, :with_electricity_tariff, switch_user: switch_user) }
    let(:token) { 'valid-jwt-token' }
    
    context 'with valid token' do
      before do
        allow(TokenService).to receive(:verify_onboarding_jwt).with(token).and_return(energy_switch.id)
        
        # Mock the energy switch has_associations for JSON response
        allow_any_instance_of(EnergySwitch).to receive(:gas_energy_tariff).and_return(nil)
        allow_any_instance_of(EnergySwitch).to receive(:electricity_energy_tariff).and_return(build(:user_tariff, energy_type: :electricity))
        
        request.headers['Authorization'] = "Bearer #{token}"
        get :verify
      end
      
      it 'returns http success' do
        expect(response).to have_http_status(:ok)
      end
      
      it 'returns JSON with energy switch data' do
        json_response = JSON.parse(response.body)
        
        expect(json_response).to include(
          'success' => true,
          'energy_switch_id' => energy_switch.id,
          'user_id' => switch_user.id,
          'has_gas' => false,
          'has_electricity' => true
        )
      end
    end
    
    context 'with invalid token' do
      before do
        allow(TokenService).to receive(:verify_onboarding_jwt).with('invalid_token').and_return(nil)
        request.headers['Authorization'] = "Bearer invalid_token"
        get :verify
      end
      
      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'returns error message' do
        json_response = JSON.parse(response.body)
        expect(json_response).to include('error' => "Your access link has expired or is invalid")
      end
    end
    
    context 'with blank token' do
      before do
        request.headers['Authorization'] = "Bearer "
        get :verify
      end
      
      it 'returns unprocessable entity status' do
        expect(response).to have_http_status(:unprocessable_entity)
      end
      
      it 'returns error message' do
        json_response = JSON.parse(response.body)
        expect(json_response).to include('error' => "Invalid access link")
      end
    end
    
    context 'with missing Authorization header' do
      before do
        get :verify
      end
      
      it 'returns unprocessable entity status' do
        expect(response).to have_http_status(:unprocessable_entity)
      end
      
      it 'returns error message' do
        json_response = JSON.parse(response.body)
        expect(json_response).to include('error' => "Invalid authorization")
      end
    end
    
    context 'with invalid Authorization format' do
      before do
        request.headers['Authorization'] = "NotBearer token123"
        get :verify
      end
      
      it 'returns unprocessable entity status' do
        expect(response).to have_http_status(:unprocessable_entity)
      end
      
      it 'returns error message' do
        json_response = JSON.parse(response.body)
        expect(json_response).to include('error' => "Invalid authorization")
      end
    end
    
    context 'with rate limiting' do
      let(:remote_ip) { '***********' }
      
      before do
        # Set up fake IP
        allow_any_instance_of(ActionDispatch::Request).to receive(:remote_ip).and_return(remote_ip)
        
        # Mock rate limit exceeded (10+ attempts)
        allow(Rails.cache).to receive(:fetch)
          .with("onboarding_verify_attempts:#{remote_ip}", hash_including(:expires_in))
          .and_return(10)
        
        # Mock SecurityMonitoringService
        allow(SecurityMonitoringService).to receive(:record_rate_limit_triggered)
        
        request.headers['Authorization'] = "Bearer #{token}"
        get :verify
      end
      
      it 'returns too many requests status' do
        expect(response).to have_http_status(:too_many_requests)
      end
      
      it 'returns rate limit message' do
        json_response = JSON.parse(response.body)
        expect(json_response).to include('error' => "Too many attempts. Please try again later")
      end
      
      it 'records the rate limit trigger in monitoring' do
        expect(SecurityMonitoringService).to have_received(:record_rate_limit_triggered)
          .with('unknown', remote_ip, 'onboarding_token_verify')
      end
    end
  end

  describe '#verify with hash fragment tokens' do
    let(:energy_switch) { create(:energy_switch) }
    let(:token) { 'encrypted.hash.fragment.token' }
    let(:decrypted_token) { 'decrypted.jwt.token' }
    
    before do
      allow(TokenService).to receive(:decrypt_token).with(token).and_return(decrypted_token)
      allow(TokenService).to receive(:verify_onboarding_jwt).with(decrypted_token).and_return(energy_switch.id)
    end
    
    context 'with valid token in Authorization header' do
      it 'verifies the token and returns energy switch data' do
        request.headers['Authorization'] = "Bearer #{token}"
        get :verify
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to include(
          'success' => true,
          'energy_switch_id' => energy_switch.id
        )
      end
    end
    
    context 'with invalid token format' do
      it 'returns error for missing Authorization header' do
        get :verify
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)).to include('error' => 'Invalid authorization')
      end
      
      it 'returns error for non-Bearer token' do
        request.headers['Authorization'] = "Basic #{token}"
        get :verify
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)).to include('error' => 'Invalid authorization')
      end
    end
    
    context 'with expired or invalid token' do
      it 'returns appropriate error message' do
        allow(TokenService).to receive(:verify_onboarding_jwt).with(decrypted_token).and_return(nil)
        request.headers['Authorization'] = "Bearer #{token}"
        get :verify
        
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)).to include('error' => 'Your access link has expired or is invalid')
      end
    end
    
    context 'with multiple valid tokens' do
      let(:energy_switch_2) { create(:energy_switch) }
      let(:token_2) { 'encrypted.token.2' }
      let(:decrypted_token_2) { 'decrypted.token.2' }
      
      it 'can validate different tokens in sequence' do
        # First token
        request.headers['Authorization'] = "Bearer #{token}"
        allow(TokenService).to receive(:decrypt_token).with(token).and_return(decrypted_token)
        allow(TokenService).to receive(:verify_onboarding_jwt).with(decrypted_token).and_return(energy_switch.id)
        
        get :verify
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['energy_switch_id']).to eq(energy_switch.id)
        
        # Second token in a new request
        request.headers['Authorization'] = "Bearer #{token_2}"
        allow(TokenService).to receive(:decrypt_token).with(token_2).and_return(decrypted_token_2)
        allow(TokenService).to receive(:verify_onboarding_jwt).with(decrypted_token_2).and_return(energy_switch_2.id)
        
        get :verify
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['energy_switch_id']).to eq(energy_switch_2.id)
      end
    end
  end
end 