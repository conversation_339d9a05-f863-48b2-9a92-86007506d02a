require 'rails_helper'

# Create a test controller that inherits from ApiController to test its methods
class TestController < Api::V1::ApiController
  def admin_route
    authorize_admin
    head :ok
  end

  def chatbot_route
    authorize_trusted_request
    head :ok
  end

  def onboarding_route
    authorize_onboarding
    head :ok
  end

  def onboarding_with_owner_route
    authorize_onboarding(params[:owner_id])
    head :ok
  end
end

RSpec.describe Api::V1::ApiController, type: :controller do
  # Set the controller for testing
  controller(TestController) do
    def routes
      Rails.application.routes
    end
  end

  # Prepare test routes
  before do
    routes.draw do
      get 'admin_route', to: 'test#admin_route'
      get 'chatbot_route', to: 'test#chatbot_route'
      get 'onboarding_route', to: 'test#onboarding_route'
      get 'onboarding_with_owner_route/:owner_id', to: 'test#onboarding_with_owner_route'
    end
  end

  # Create test tokens
  let(:admin) { create(:admin) }
  let(:switch_user) { create(:switch_user) }
  
  let(:admin_token) { create(:admin_token, resource_owner_id: admin.id) }
  let(:chatbot_token) { create(:chatbot_token) }
  let(:onboarding_token) { create(:onboarding_token, resource_owner_id: switch_user.id) }
  let(:expired_token) { create(:admin_token, resource_owner_id: admin.id, expires_in: -1.day) }

  describe 'authorization methods' do
    # Mock TokenService for all tests
    before do
      allow(TokenService).to receive(:verify_token_scope).and_return(false)
    end
    
    describe '#authorize_admin' do
      it 'allows access with valid admin token' do
        # Only allow this specific call to return true
        allow(TokenService).to receive(:verify_token_scope)
          .with(admin_token.token, 'admin', 'Admin')
          .and_return(true)
        
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        get :admin_route
        expect(response).to have_http_status(:ok)
      end
      
      it 'denies access with non-admin token' do
        expect(controller).to receive(:render).with(
          json: { error: 'Unauthorized: Admin access required' }, 
          status: :unauthorized
        ).and_call_original
        
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        get :admin_route
      end
      
      it 'denies access with no token' do
        expect(controller).to receive(:render).with(
          json: { error: 'Unauthorized: Admin access required' }, 
          status: :unauthorized
        ).and_call_original
        
        get :admin_route
      end
    end
    
    describe '#authorize_trusted_request' do
      it 'allows access with valid chatbot token' do
        # Only allow this specific call to return true
        allow(TokenService).to receive(:verify_token_scope)
          .with(chatbot_token.token, 'chatbot', 'ChatBot')
          .and_return(true)
        
        request.headers['Authorization'] = "Bearer #{chatbot_token.token}"
        get :chatbot_route
        expect(response).to have_http_status(:ok)
      end
      
      it 'denies access with non-chatbot token' do
        expect(controller).to receive(:render).with(
          json: { error: 'Unauthorized' }, 
          status: :unauthorized
        ).and_call_original
        
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        get :chatbot_route
      end
      
      it 'denies access with no token' do
        expect(controller).to receive(:render).with(
          json: { error: 'Unauthorized' }, 
          status: :unauthorized
        ).and_call_original
        
        get :chatbot_route
      end
    end
    
    describe '#authorize_onboarding' do
      it 'allows access with valid onboarding token' do
        # Only allow this specific call to return true
        allow(TokenService).to receive(:verify_token_scope)
          .with(onboarding_token.token, 'onboarding', 'Onboarding', nil)
          .and_return(true)
        
        request.headers['Authorization'] = "Bearer #{onboarding_token.token}"
        get :onboarding_route
        expect(response).to have_http_status(:ok)
      end
      
      it 'denies access with non-onboarding token' do
        expect(controller).to receive(:render).with(
          json: { error: 'Unauthorized: Onboarding access required' }, 
          status: :unauthorized
        ).and_call_original
        
        request.headers['Authorization'] = "Bearer #{admin_token.token}"
        get :onboarding_route
      end
      
      it 'allows access for matching resource owner' do
        # Only allow this specific call to return true
        allow(TokenService).to receive(:verify_token_scope)
          .with(onboarding_token.token, 'onboarding', 'Onboarding', switch_user.id.to_s)
          .and_return(true)
        
        request.headers['Authorization'] = "Bearer #{onboarding_token.token}"
        get :onboarding_with_owner_route, params: { owner_id: switch_user.id }
        expect(response).to have_http_status(:ok)
      end
      
      it 'denies access for non-matching resource owner' do
        other_user = create(:switch_user)
        
        expect(controller).to receive(:render).with(
          json: { error: 'Unauthorized: Onboarding access required' }, 
          status: :unauthorized
        ).and_call_original
        
        request.headers['Authorization'] = "Bearer #{onboarding_token.token}"
        get :onboarding_with_owner_route, params: { owner_id: other_user.id }
      end
      
      it 'denies access with no token' do
        expect(controller).to receive(:render).with(
          json: { error: 'Unauthorized: Onboarding access required' }, 
          status: :unauthorized
        ).and_call_original
        
        get :onboarding_route
      end
    end
  end
end 