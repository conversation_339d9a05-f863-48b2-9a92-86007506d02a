require 'rails_helper'
require 'request_store'

RSpec.describe SecurityMailer, type: :mailer do
  let(:events) do
    [
      build_stubbed(:security_event, event_type: 'login_failure', email: '<EMAIL>', ip_address: '***********', created_at: 1.hour.ago, details: { user_agent: 'Mozilla/5.0' })
    ]
  end
  
  before do
    # Mock RequestStore for request_id
    allow(RequestStore).to receive_message_chain(:store, :[]).with(:request_id).and_return('test-request-id')
    
    # Mock Socket for hostname
    allow(Socket).to receive(:gethostname).and_return('test-server')
  end
  
  describe '#alert' do
    let(:mail) { SecurityMailer.alert('Security Alert', 'This is a security alert message', events: events) }
    
    it 'renders the subject correctly' do
      expect(mail.subject).to eq('Security Alert')
    end
    
    it 'sends to the correct recipient' do
      expect(mail.to).to eq(['<EMAIL>'])
    end
    
    it 'sends from the correct sender' do
      expect(mail.from).to eq(['<EMAIL>'])
    end
    
    it 'includes the alert message in the body' do
      expect(mail.body.encoded).to match(/This is a security alert message/)
    end
    
    it 'includes server information in the body' do
      expect(mail.body.encoded).to match(/test-server/)
    end
    
    it 'includes request ID in the body' do
      expect(mail.body.encoded).to match(/test-request-id/)
    end
    
    context 'with events' do
      it 'includes event details in the body' do
        expect(mail.body.encoded).to match(/login_failure/)
        expect(mail.body.encoded).to match(/<EMAIL>/)
        expect(mail.body.encoded).to match(/***********/)
      end
    end
    
    context 'without events' do
      let(:mail) { SecurityMailer.alert('Security Alert', 'This is a security alert message') }
      
      it 'does not include event details in the body' do
        expect(mail.body.encoded).not_to match(/Event Details:/)
      end
    end
  end
  
  describe '#suspicious_activity_report' do
    # Create some test data
    let!(:security_events) do
      # We'll mock the SecurityEvent model and its methods
      events_double = double('SecurityEvent::ActiveRecord_Relation')
      event_count_by_type = {
        'verification_success' => 10,
        'verification_failure' => 5,
        'rate_limit_triggered' => 3
      }
      suspicious_ips = { '***********' => 15, '***********' => 12 }
      suspicious_emails = { '<EMAIL>' => 8, '<EMAIL>' => 6 }
      
      # Setup the needed methods on our double
      allow(events_double).to receive(:count).and_return(18)
      allow(events_double).to receive(:group).with(:event_type).and_return(double('GroupedRelation', count: event_count_by_type))
      allow(events_double).to receive(:where).and_return(events_double)
      
      # Mock SecurityEvent.where to return our double
      allow(SecurityEvent).to receive(:where).and_return(events_double)
      
      # Setup the suspicious IPs query
      ip_query_double = double('IPQueryRelation')
      allow(SecurityEvent).to receive_message_chain(:where, :group, :having, :count, :sort_by, :first).and_return(suspicious_ips.to_a)
      
      # Setup the suspicious emails query
      email_query_double = double('EmailQueryRelation')
      allow(SecurityEvent).to receive_message_chain(:where, :where, :not, :group, :having, :count, :sort_by, :first).and_return(suspicious_emails.to_a)
      
      events_double
    end
    
    describe 'daily report' do
      let(:mail) { SecurityMailer.suspicious_activity_report('daily', events) }
      
      it 'renders the subject correctly' do
        expect(mail.subject).to eq('Daily Security Activity Report')
      end
      
      it 'sends to the correct recipient' do
        expect(mail.to).to eq(['<EMAIL>'])
      end
      
      it 'includes the period in the body' do
        expect(mail.body.encoded).to match(/Daily Security Report/)
      end
    end
    
    describe 'hourly report' do
      let(:mail) { SecurityMailer.suspicious_activity_report('hourly', events) }
      
      it 'renders the subject correctly' do
        expect(mail.subject).to eq('Hourly Security Activity Report')
      end
      
      it 'includes the period in the body' do
        expect(mail.body.encoded).to match(/Hourly Security Report/)
      end
    end
    
    describe 'weekly report' do
      let(:mail) { SecurityMailer.suspicious_activity_report('weekly', events) }
      
      it 'renders the subject correctly' do
        expect(mail.subject).to eq('Weekly Security Activity Report')
      end
      
      it 'includes the period in the body' do
        expect(mail.body.encoded).to match(/Weekly Security Report/)
      end
    end
    
    it 'includes event details in the body' do
      mail = SecurityMailer.suspicious_activity_report('daily', events)
      expect(mail.body.encoded).to match(/login_failure/)
      expect(mail.body.encoded).to match(/<EMAIL>/)
      expect(mail.body.encoded).to match(/***********/)
    end
    
    it 'includes a summary of event counts' do
      mail = SecurityMailer.suspicious_activity_report('daily', events)
      expect(mail.body.encoded).to match(/<strong>Total Events:<\/strong>\s*1/)
    end
  end
end 