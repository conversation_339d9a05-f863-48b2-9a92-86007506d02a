require 'vcr'

VCR.configure do |c|
  c.allow_http_connections_when_no_cassette = true
  c.hook_into :webmock
  c.cassette_library_dir = 'spec/vcr_cassettes'
  c.configure_rspec_metadata!
  
  # Set default record mode to new_episodes to allow recording new requests
  c.default_cassette_options = { 
    record: :new_episodes,
    match_requests_on: [:method, :uri, :body]
  }
  
  # Don't VCR our test environment
  c.ignore_localhost = true
  
  # Ignore requests to the test server
  c.ignore_request do |request|
    uri = URI(request.uri)
    uri.host == 'localhost' || uri.host == '127.0.0.1'
  end
  
  # Filter sensitive data from Xoserve Gas API
  c.filter_sensitive_data('<XOSERVE_SUBSCRIPTION_KEY>') { ENV['XOSERVE_SUBSCRIPTION_KEY'] }
  c.filter_sensitive_data('<XOSERVE_CLIENT_ID>') { ENV['XOSERVE_CLIENT_ID'] }
  c.filter_sensitive_data('<XOSERVE_CLIENT_SECRET>') { ENV['XOSERVE_CLIENT_SECRET'] }
  
  # Filter sensitive data from ECOES API (Xoserve Electricity)
  c.filter_sensitive_data('<ECOES_SUBSCRIPTION_KEY>') { ENV['ECOES_SUBSCRIPTION_KEY'] }
  
  # Filter access tokens in request bodies and responses
  c.filter_sensitive_data('<ACCESS_TOKEN>') do |interaction|
    # Extract JWT token from JSON responses
    if interaction.response.body.include?('access_token')
      begin
        json = JSON.parse(interaction.response.body)
        json['access_token'] if json['access_token']
      rescue JSON::ParserError
        nil
      end
    end
  end
  
  # Filter bearer token in Authorization header
  c.filter_sensitive_data('Bearer <ACCESS_TOKEN>') do |interaction|
    auth_header = interaction.request.headers['Authorization']&.first
    auth_header if auth_header&.start_with?('Bearer ')
  end
  
  # Filter subscription key in request bodies for ECOES API
  c.filter_sensitive_data('<ECOES_SUBSCRIPTION_KEY>') do |interaction|
    if interaction.request.body.include?('Authentication')
      begin
        json = JSON.parse(interaction.request.body)
        json['Authentication']['Key'] if json['Authentication'] && json['Authentication']['Key']
      rescue JSON::ParserError
        nil
      end
    end
  end
end

# Configure RSpec to use VCR cassettes
RSpec.configure do |config|
  # Configure VCR for specific request specs
  config.around(:each, :vcr) do |example|
    name = example.metadata[:full_description].split(/\s+/, 2).join("/").underscore.gsub(/[^\w\/]+/, "_")
    VCR.use_cassette(name) { example.call }
  end
end 