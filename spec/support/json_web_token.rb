class JsonWebToken
  def self.encode(payload)
    if payload[:admin_id]
      "fake-admin-jwt-token-for-testing-#{payload[:admin_id]}"
    else
      "fake-jwt-token-for-testing-#{payload[:user_id]}"
    end
  end

  def self.decode(token)
    if token.to_s.include?('admin')
      admin_id = token.to_s.split('-').last
      { admin_id: admin_id }
    else
      user_id = token.to_s.split('-').last
      { user_id: user_id }
    end
  end
end 