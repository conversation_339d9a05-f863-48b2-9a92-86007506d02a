# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all environment files (except templates).
/.env*
!/.env*.erb

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore storage (uploaded files in development and any SQLite databases).
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep

# Ignore master key for decrypting credentials and more.
/config/master.key

/config/credentials/production.key

/config/credentials/development.key
supplier-partners/*
# full-tomato-prime-tariff-json.json
# rebel-sample-tariff.json

.DS_Store

# Terraform secrets and local environment files
deployment/terraform/secrets.tfvars
deployment/terraform/secrets.auto.tfvars
deployment/terraform/secrets.tf
deployment/scripts/.env.local
deployment/terraform/.terraform.tfstate.lock.info
deployment/terraform/.terraform/
deployment/terraform/terraform.tfstate*
