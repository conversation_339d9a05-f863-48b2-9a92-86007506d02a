# Secure Onboarding Token PR Checklist

This PR improves the security of onboarding links by implementing JWT-based tokens with one-time use and removing sensitive information from URLs. It provides a secure API endpoint for the NextJS frontend to verify tokens.

## Implementation Steps

- [X] Add JWT-based token generation to `TokenService`
- [X] Update `generate_onboarding_url` to use JWT tokens
- [X] Create API endpoint to verify tokens for the NextJS frontend
- [X] Update routes for the new workflow
- [ ] Add environment variables to support the implementation:
  - [ ] `JWT_SECRET_KEY` - A secure random string for signing JWTs
  - [ ] `ONBOARDING_ACCESS_TOKEN_EXPIRATION_HOURS` - Already exists, controls token expiration

## Testing Steps

- [ ] Verify the token generation works with proper payload
- [ ] Test JWT token validation API endpoint properly handles:
  - [ ] Expired tokens
  - [ ] Already used tokens (one-time use)
  - [ ] Invalid tokens
  - [ ] Tampered tokens
- [ ] Verify rate limiting works for token verification attempts
- [ ] Test frontend integration with the verification API endpoint
- [ ] Test end-to-end onboarding flow with new secure URLs

## Security Improvements

1. **URL Security**: Sensitive information (energy switch ID, token) is no longer exposed in the URL path
2. **One-time use**: Tokens can only be used once, improving security if links are accidentally shared
3. **Rate limiting**: Brute force attempts are limited by IP address
4. **Token revocation**: Token IDs are stored in cache, allowing for revocation if needed
5. **Secure storage**: Using Rails cache with appropriate expiration for token status
6. **Input validation**: Server host validation has been added
7. **NextJS Integration**: Token verification happens via a secure API endpoint rather than in URL parameters

## Frontend Integration

The NextJS app should:

1. Extract the token from the URL query parameter
2. Make an API request to `/api/v1/onboarding/verify?token={token}`
3. Handle the API responses:
   - Success (200): Store the energy switch details in the session/state
   - Unauthorized (401): Display error about expired/invalid token
   - Too Many Requests (429): Show rate limit message
   - Other errors: Display appropriate message
4. After successful verification, display the tariff options to the user

## Deployment Considerations

- Requires adding `JWT_SECRET_KEY` to all environments
- Ensure CORS is properly configured to allow the NextJS frontend to access the API
- Consider a transition period where both old and new URL formats are supported
- Update documentation for the onboarding process
- Ensure monitoring is in place to track any token validation issues

## Additional Notes

The implementation uses Rails cache for token status tracking. In a high-volume production environment, consider migrating to Redis or another distributed cache if not already using one.
