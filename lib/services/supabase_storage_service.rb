require 'faraday'
require 'base64'
require 'json'

module Services
  class SupabaseStorageService
    def initialize
      @supabase_url = ENV['SUPABASE_URL']
      @service_role_key = ENV['SUPABASE_SERVICE_ROLE_KEY']
      @bucket_name = ENV['SUPABASE_USER_BILLS_BUCKET_NAME']
      
      raise "Missing SUPABASE_URL environment variable" unless @supabase_url
      raise "Missing SUPABASE_SERVICE_ROLE_KEY environment variable" unless @service_role_key
      raise "Missing SUPABASE_USER_BILLS_BUCKET_NAME environment variable" unless @bucket_name
      
      @base_url = "#{@supabase_url}/storage/v1"
      
      @client = Faraday.new(@base_url) do |f|
        # f.request :multipart
        f.request :url_encoded
        f.adapter Faraday.default_adapter
        f.headers['Authorization'] = "Bearer #{@service_role_key}"
        f.headers['apikey'] = @service_role_key
      end
    end

    # Generate a pre-signed URL for viewing/downloading a file
    def presigned_url(key, expires_in: 86400)
      return nil if key.blank?

      response = @client.post("/object/sign/#{@bucket_name}") do |req|
        req.headers['Content-Type'] = 'application/json'
        req.body = {
          expiresIn: expires_in,
          path: key
        }.to_json
      end

      if response.success?
        data = JSON.parse(response.body)
        "#{@supabase_url}#{data['signedURL']}"
      else
        Rails.logger.error("Supabase presigned URL error: #{response.body}")
        nil
      end
    rescue StandardError => e
      Rails.logger.error("Supabase presigned URL error: #{e.message}")
      nil
    end

    # Generate a pre-signed URL for uploading a file
    def presigned_upload_url(key, content_type, expires_in: 3600)
      return nil if key.blank?

      response = @client.post("/object/upload/sign/#{@bucket_name}/#{key}") do |req|
        req.headers['Content-Type'] = 'application/json'
        req.body = {
          expiresIn: expires_in
        }.to_json
      end

      if response.success?
        data = JSON.parse(response.body)
        "#{@supabase_url}#{data['url']}"
      else
        Rails.logger.error("Supabase presigned upload URL error: #{response.body}")
        nil
      end
    rescue StandardError => e
      Rails.logger.error("Supabase presigned upload URL error: #{e.message}")
      nil
    end

    # Directly upload a file to Supabase Storage
    def upload_file(key, file_data, content_type)
      return false if key.blank? || file_data.blank?

      response = @client.post("/object/#{@bucket_name}/#{key}") do |req|
        req.headers['Content-Type'] = content_type
        req.body = file_data
      end

      if response.success?
        true
      else
        Rails.logger.error("Supabase upload error: #{response.body}")
        false
      end
    rescue StandardError => e
      Rails.logger.error("Supabase upload error: #{e.message}")
      false
    end

    # Delete a file from Supabase Storage
    def delete_file(key)
      return false if key.blank?

      response = @client.delete("/object/#{@bucket_name}/#{key}")

      if response.success?
        true
      else
        Rails.logger.error("Supabase delete error: #{response.body}")
        false
      end
    rescue StandardError => e
      Rails.logger.error("Supabase delete error: #{e.message}")
      false
    end

    # List files in the bucket (optional method for debugging/admin purposes)
    def list_files(prefix: '', limit: 100)
      response = @client.post("/object/list/#{@bucket_name}") do |req|
        req.headers['Content-Type'] = 'application/json'
        req.body = {
          limit: limit,
          prefix: prefix
        }.to_json
      end

      if response.success?
        JSON.parse(response.body)
      else
        Rails.logger.error("Supabase list files error: #{response.body}")
        []
      end
    rescue StandardError => e
      Rails.logger.error("Supabase list files error: #{e.message}")
      []
    end
  end
end 