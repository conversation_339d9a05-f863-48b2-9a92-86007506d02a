namespace :debug do
  desc "Debug onboarding JWT token - Usage: rake debug:token TOKEN=your_jwt_token_here"
  task token: :environment do
    token = ENV['TOKEN']
    
    if token.nil? || token.empty?
      puts "❌ Error: Please provide a TOKEN argument"
      puts "Usage: rake debug:token TOKEN=your_jwt_token_here"
      puts ""
      puts "Example:"
      puts "rake debug:token TOKEN=eyJhbGciOiJIUzUxMiJ9.eyJzd2l0Y2hfaWQi..."
      exit 1
    end
    
    debug_onboarding_token(token)
  end
  
  private
  
  def debug_onboarding_token(token)
    puts "=" * 60
    puts "🔍 ONBOARDING TOKEN DEBUG ANALYSIS"
    puts "=" * 60
    puts "Token (first 50 chars): #{token[0..49]}..."
    puts "Token length: #{token.length} characters"
    puts ""
    
    # 1. Basic decode without verification
    begin
      payload = JWT.decode(token, nil, false)[0]
      puts "✅ 1. TOKEN PAYLOAD DECODED:"
      puts "-" * 30
      payload.each do |key, value|
        if key == 'exp' || key == 'iat'
          time_value = Time.at(value) rescue "Invalid timestamp"
          puts "  #{key.ljust(12)}: #{value} (#{time_value})"
        else
          puts "  #{key.ljust(12)}: #{value}"
        end
      end
      puts ""
    rescue => e
      puts "❌ 1. CANNOT DECODE TOKEN: #{e.message}"
      puts "   This indicates the token format is completely invalid."
      return
    end
    
    # 2. Time validation
    puts "⏰ 2. TIME VALIDATION:"
    puts "-" * 30
    begin
      exp_time = Time.at(payload['exp'])
      iat_time = Time.at(payload['iat'])
      current_time = Time.now
      
      puts "  Issued at    : #{iat_time} (#{time_ago_in_words(iat_time)} ago)"
      puts "  Expires at   : #{exp_time}"
      puts "  Current time : #{current_time}"
      puts "  Time to expiry: #{distance_of_time_in_words(current_time, exp_time)}"
      
      if exp_time < current_time
        puts "  Status       : ❌ EXPIRED (#{time_ago_in_words(exp_time)} ago)"
      else
        puts "  Status       : ✅ VALID (expires in #{distance_of_time_in_words(current_time, exp_time)})"
      end
      puts ""
    rescue => e
      puts "  ❌ Error parsing timestamps: #{e.message}"
      puts ""
    end
    
    # 3. Cache status check
    puts "💾 3. CACHE STATUS:"
    puts "-" * 30
    token_id = payload['jti']
    if token_id.present?
      cache_key = "onboarding_token:#{token_id}"
      cache_status = Rails.cache.read(cache_key)
      
      puts "  Token ID (jti): #{token_id}"
      puts "  Cache key     : #{cache_key}"
      puts "  Cache status  : #{cache_status.inspect}"
      
      case cache_status
      when nil
        puts "  Result        : ❌ TOKEN NOT FOUND IN CACHE (expired or never existed)"
      when true
        puts "  Result        : ✅ TOKEN IS VALID AND UNUSED"
      when 'used'
        puts "  Result        : ❌ TOKEN HAS ALREADY BEEN USED"
      else
        puts "  Result        : ⚠️  UNKNOWN CACHE STATUS: #{cache_status}"
      end
    else
      puts "  ❌ No token ID (jti) found in payload"
    end
    puts ""
    
    # 4. Environment configuration
    puts "⚙️  4. ENVIRONMENT CONFIGURATION:"
    puts "-" * 30
    jwt_secret_present = ENV['JWT_SECRET_KEY'].present?
    expiration_hours = ENV['ONBOARDING_ACCESS_TOKEN_EXPIRATION_HOURS']
    
    puts "  JWT_SECRET_KEY present    : #{jwt_secret_present ? '✅ YES' : '❌ NO'}"
    if jwt_secret_present
      puts "  JWT_SECRET_KEY length     : #{ENV['JWT_SECRET_KEY'].length} characters"
    end
    puts "  Expiration hours setting  : #{expiration_hours || 'NOT SET'}"
    puts ""
    
    # 5. Signature verification
    puts "🔐 5. SIGNATURE VERIFICATION:"
    puts "-" * 30
    if !jwt_secret_present
      puts "  ❌ Cannot verify signature - JWT_SECRET_KEY not configured"
    else
      begin
        JWT.decode(token, ENV['JWT_SECRET_KEY'], true, { algorithm: 'HS512' })
        puts "  ✅ SIGNATURE IS VALID"
      rescue JWT::ExpiredSignature
        puts "  ❌ TOKEN HAS EXPIRED (but signature would be valid)"
      rescue JWT::DecodeError => e
        puts "  ❌ SIGNATURE IS INVALID: #{e.message}"
        puts "     This usually means the token was signed with a different secret"
      rescue => e
        puts "  ❌ VERIFICATION ERROR: #{e.message}"
      end
    end
    puts ""
    
    # 6. Energy switch validation
    puts "🔄 6. ENERGY SWITCH VALIDATION:"
    puts "-" * 30
    switch_id = payload['switch_id']
    user_id = payload['user_id']
    
    puts "  Switch ID from token: #{switch_id}"
    puts "  User ID from token  : #{user_id}"
    
    if switch_id.present?
      energy_switch = EnergySwitch.find_by(id: switch_id)
      if energy_switch
        puts "  ✅ ENERGY SWITCH FOUND:"
        puts "    ID                : #{energy_switch.id}"
        puts "    Status            : #{energy_switch.status}"
        puts "    User ID           : #{energy_switch.switch_user_id}"
        puts "    User ID matches   : #{energy_switch.switch_user_id == user_id ? '✅ YES' : '❌ NO'}"
        puts "    Has Gas           : #{energy_switch.gas_energy_tariff.present? ? '✅ YES' : '❌ NO'}"
        puts "    Has Electricity   : #{energy_switch.electricity_energy_tariff.present? ? '✅ YES' : '❌ NO'}"
        puts "    Created at        : #{energy_switch.created_at}"
        puts "    Updated at        : #{energy_switch.updated_at}"
      else
        puts "  ❌ ENERGY SWITCH NOT FOUND with ID: #{switch_id}"
        puts "     The energy switch may have been deleted or the ID is incorrect"
      end
    else
      puts "  ❌ No switch_id found in token payload"
    end
    puts ""
    
    # 7. Final TokenService verification
    puts "🧪 7. TOKENSERVICE VERIFICATION TEST:"
    puts "-" * 30
    begin
      result = TokenService.verify_onboarding_jwt(token)
      if result
        puts "  ✅ SUCCESS: TokenService returned energy_switch_id = #{result}"
        puts "     This token should work for onboarding verification"
      else
        puts "  ❌ FAILED: TokenService returned nil"
        puts "     This token will be rejected by the onboarding system"
      end
    rescue => e
      puts "  ❌ ERROR during TokenService verification: #{e.message}"
    end
    puts ""
    
    # 8. Summary and recommendations
    puts "📋 8. SUMMARY & RECOMMENDATIONS:"
    puts "-" * 30
    
    issues = []
    
    # Check for common issues
    if payload['exp'] && Time.at(payload['exp']) < Time.now
      issues << "Token has expired"
    end
    
    if token_id.present? && Rails.cache.read("onboarding_token:#{token_id}").nil?
      issues << "Token not found in cache (may have expired or been used)"
    end
    
    if !ENV['JWT_SECRET_KEY'].present?
      issues << "JWT_SECRET_KEY environment variable not configured"
    end
    
    if switch_id.present? && !EnergySwitch.find_by(id: switch_id)
      issues << "Energy switch not found in database"
    end
    
    if issues.any?
      puts "  ❌ ISSUES FOUND:"
      issues.each_with_index do |issue, index|
        puts "     #{index + 1}. #{issue}"
      end
      puts ""
      puts "  💡 RECOMMENDATIONS:"
      if issues.include?("Token has expired")
        puts "     • Generate a new token for this energy switch"
      end
      if issues.include?("Token not found in cache (may have expired or been used)")
        puts "     • Check if token was already used or cache expired"
        puts "     • Generate a new token if needed"
      end
      if issues.include?("JWT_SECRET_KEY environment variable not configured")
        puts "     • Set JWT_SECRET_KEY in your environment variables"
      end
      if issues.include?("Energy switch not found in database")
        puts "     • Verify the energy switch still exists in the database"
        puts "     • Check if the switch_id in the token is correct"
      end
    else
      puts "  ✅ NO OBVIOUS ISSUES FOUND"
      puts "     If the token is still failing, check:"
      puts "     • Network connectivity between frontend and backend"
      puts "     • CORS configuration"
      puts "     • Rate limiting settings"
    end
    
    puts ""
    puts "=" * 60
    puts "🏁 DEBUG ANALYSIS COMPLETE"
    puts "=" * 60
  end

  # Helper method for time formatting
  def time_ago_in_words(time)
    distance_of_time_in_words(time, Time.now)
  end

  def distance_of_time_in_words(from_time, to_time)
    distance_in_seconds = (to_time - from_time).abs

    case distance_in_seconds
    when 0..59
      "#{distance_in_seconds.to_i} seconds"
    when 60..3599
      "#{(distance_in_seconds / 60).to_i} minutes"
    when 3600..86399
      "#{(distance_in_seconds / 3600).to_i} hours"
    else
      "#{(distance_in_seconds / 86400).to_i} days"
    end
  end
end
