# To run this task, use the following command in your console:
# bundle exec rake test_data:generate_users
#
# This will create 50 test switch users with random energy data
# for development and testing purposes.

namespace :test_data do
  desc "Generate test switch users with energy data"
  task generate_users: :environment do
    puts "Generating test switch users..."
    
    # Define UK postcodes for random assignment
    uk_postcodes = [
      "SW1A 1AA", "EC1A 1BB", "W1A 0AX", "M1 1AE", "B1 1BB",
      "G1 1AA", "L1 8JQ", "NE1 7RU", "S1 2BJ", "CF10 1BH",
      "BT1 1LT", "EH1 1YZ", "LS1 1UR", "BS1 5TR", "NG1 5FN"
    ]
    
    # Find existing energy tariffs to reference in non-draft switches
    available_energy_tariffs = EnergyTariff.all
    
    if available_energy_tariffs.empty?
      puts "Warning: No energy tariffs found in database. All switches will be set to draft status."
    end
    
    # Count how many users we create successfully
    successful_creations = 0
    max_attempts = 100
    
    # Create 50 test users with random data
    while successful_creations < 50 && max_attempts > 0
      max_attempts -= 1
      
      random_suffix = SecureRandom.hex(4)
      first_name = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
                    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"].sample
      last_name = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
                  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Robinson"].sample
      
      # Generate a unique email
      email = "test.user.#{random_suffix}@example.com"
      
      # Skip if this email already exists
      if SwitchUser.exists?(email: email)
        next
      end
      
      begin
        # Create the switch user
        switch_user = SwitchUser.create!(
          email: email,
          first_name: first_name,
          last_name: last_name,
          phone_number: "07#{rand(700000000..********9)}",
          date_of_birth: Date.today - rand(20..80).years
        )
        
        # Create an address for the user
        postcode = uk_postcodes.sample
        street_name = ['High Street', 'Main Road', 'Church Lane', 'Park Avenue', 'The Green'].sample
        number = rand(1..100).to_s
        flat = ["Flat #{rand(1..20)}", "Apartment #{rand(1..50)}", nil, nil].sample
        city = ["London", "Manchester", "Birmingham", "Glasgow", "Liverpool", "Edinburgh", "Cardiff"].sample
        
        full_address = [
          flat,
          "#{number} #{street_name}",
          city
        ].compact.join(", ")
        
        address = Address.create!(
          switch_user: switch_user,
          street: street_name,
          number: number,
          flat: flat,
          postcode: postcode,
          posttown: city,
          full_address: full_address
        )
        
        # Decide if billing address is the same as supply address
        billing_address_same = rand < 0.8 # 80% chance of true
        
        # Create payment method
        payment_method = PaymentMethod.create!(
          switch_user: switch_user,
          payment_type: [
            PaymentMethod::MONTHLY_FIXED_DIRECT_DEBIT,
            PaymentMethod::MONTHLY_VARIABLE_DIRECT_DEBIT, 
            PaymentMethod::MONTHLY_VARIABLE_CASH_CHEQUE
          ].sample,
          account_holder_name: "#{first_name} #{last_name}",
          sort_code: "#{rand(10..99)}#{rand(10..99)}#{rand(10..99)}",
          account_number: rand(********..********).to_s,
          estimated_monthly_payment: rand(50.0..200.0).round(2),
          billing_address_same_as_supply: billing_address_same,
          switch_within_5_days: [true, false].sample,
          understand_cooling_off_period: true,
          billing_address: billing_address_same ? nil : "#{rand(1..100)} #{['Park Road', 'Station Avenue', 'Victoria Street', 'Albert Road', 'Queen Lane'].sample}, #{["Bristol", "Leeds", "Sheffield", "Newcastle", "Brighton"].sample}, #{uk_postcodes.sample}"
        )
        
        # Create gas tariff for the user
        gas_tariff = UserTariff.create!(
          switch_user: switch_user,
          energy_type: :gas,
          meter_point_reference_number: rand(********00..********99).to_s,
          meter_serial_number: "G#{rand(********..********)}",
          unit_rate: rand(2.5..7.5),
          standing_charge: rand(20.0..35.0),
          supplier_name: ["British Gas", "E.ON", "EDF Energy", "Scottish Power", "Tomato Energy", "Rebel Energy"].sample,
          payment_method: ["Direct Debit", "Standing Order", "Cash/Cheque"].sample,
          tariff_type: ["Fixed", "Variable"].sample,
          gas_estimated_annual_usage: rand(8000..20000),
          gas_monthly_usage: rand(700..1800)
        )
        
        # Create electricity tariff for the user
        electricity_tariff = UserTariff.create!(
          switch_user: switch_user,
          energy_type: :electricity,
          meter_point_administration_number: rand(********00000..********99999).to_s,
          meter_serial_number: "E#{rand(********..********)}",
          unit_rate: rand(15.0..25.0),
          standing_charge: rand(20.0..40.0),
          supplier_name: ["British Gas", "E.ON", "EDF Energy", "Scottish Power", "Tomato Energy", "Rebel Energy"].sample,
          payment_method: ["Direct Debit", "Standing Order", "Cash/Cheque"].sample,
          tariff_type: ["Fixed", "Variable"].sample,
          electricity_est_annual_usage: rand(1500..4500),
          electricity_monthly_usage: rand(150..400),
          profile_class: [1, 2].sample.to_s,
          gsp_code: ["_A", "_B", "_C", "_D", "_E", "_F", "_G", "_H", "_J", "_K", "_L", "_M", "_N", "_P"].sample
        )
        
        # Always use draft status since we don't have access to valid energy tariffs
        # This avoids the validation error about switching_to_tariff
        status = :draft
        
        # Create energy switch with the tariffs
        energy_switch = EnergySwitch.create!(
          switch_user: switch_user,
          gas_energy_tariff: gas_tariff,
          electricity_energy_tariff: electricity_tariff,
          address: address,
          status: status,
          lived_three_years: [true, false].sample,
          payment_method: payment_method,
          reference_number: "SW#{rand(100000..999999)}",
          switch_date: Date.today + rand(10..60).days,
          viewed_at: [nil, Time.now - rand(1..30).days].sample
        )
        
        successful_creations += 1
        puts "Created switch user ##{successful_creations}: #{switch_user.full_name} (#{switch_user.email}) with status: #{status}"
      
      rescue ActiveRecord::RecordInvalid => e
        puts "Failed to create user: #{e.message}"
        next
      rescue ActiveRecord::RecordNotUnique => e
        puts "Duplicate record encountered: #{e.message}"
        next
      end
    end
    
    puts "Finished generating #{successful_creations} test switch users!"
  end

  desc "Generate test security events"
  task generate_security_events: :environment do
    puts "Generating test security events..."
    
    # Number of security events to generate
    num_events = 200
    successful_creations = 0
    
    # Get existing users to associate events with
    users = SwitchUser.all
    
    if users.empty?
      puts "No users found in the database. Please run rake test_data:generate_users first."
      return
    end

    # Use the event types defined in the SecurityEvent model
    event_types = [
      SecurityEvent::VERIFICATION_SUCCESS,
      SecurityEvent::VERIFICATION_FAILURE,
      SecurityEvent::VERIFICATION_CODE_SENT,
      SecurityEvent::RATE_LIMIT_TRIGGERED
    ]

    # IP addresses for events (mix of IPv4 and IPv6)
    ip_addresses = [
      "192.168.1.#{rand(1..255)}", 
      "10.0.#{rand(1..255)}.#{rand(1..255)}", 
      "172.16.#{rand(1..255)}.#{rand(1..255)}",
      "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
      "2001:0db8:85a3:0000:0000:8a2e:0370:#{rand(1000..9999)}",
      "127.0.0.1",
      "::1"
    ]

    # User agents for browser identification
    user_agents = [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
      "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
    ]

    # Generate events
    num_events.times do
      user = users.sample
      event_type = event_types.sample
      
      # Generate a timestamp within the last 30 days
      timestamp = Time.now - rand(0..30).days - rand(0..23).hours - rand(0..59).minutes - rand(0..59).seconds
      
      # Generate custom details based on event type
      details = {}
      ip_address = ip_addresses.sample
      
      case event_type
      when SecurityEvent::VERIFICATION_SUCCESS
        details = {
          method: ["email", "sms", "authenticator_app", "phone_call"].sample,
          user_agent: user_agents.sample,
          browser: ["Chrome", "Firefox", "Safari", "Edge"].sample,
          platform: ["Windows", "macOS", "iOS", "Android", "Linux"].sample
        }
      when SecurityEvent::VERIFICATION_FAILURE
        details = {
          reason: ["invalid_code", "expired_code", "too_many_attempts", "code_not_found"].sample,
          attempt_number: rand(1..10),
          user_agent: user_agents.sample,
          browser: ["Chrome", "Firefox", "Safari", "Edge"].sample,
          platform: ["Windows", "macOS", "iOS", "Android", "Linux"].sample
        }
      when SecurityEvent::VERIFICATION_CODE_SENT
        details = {
          channel: ["email", "sms", "push_notification"].sample,
          code_expires_in: "10 minutes",
          triggered_by: ["login", "password_reset", "profile_update", "payment_update"].sample
        }
      when SecurityEvent::RATE_LIMIT_TRIGGERED
        details = {
          threshold: rand(3..20),
          window_seconds: [60, 300, 600, 3600].sample,
          action_taken: ["block", "challenge", "delay", "notify"].sample,
          request_type: ["login", "api", "verification", "password_reset"].sample
        }
      end

      begin
        # Randomly decide if we'll include email (50% chance)
        include_email = rand > 0.5

        # Parameters for creating the security event
        event_params = {
          event_type: event_type,
          details: details,
          created_at: timestamp,
          updated_at: timestamp
        }

        # Always include at least one of email or IP address to satisfy validation
        if include_email
          event_params[:email] = user.email
          # 80% chance to also include IP address when email is present
          event_params[:ip_address] = ip_address if rand > 0.2
        else
          # If no email, must include IP address
          event_params[:ip_address] = ip_address
        end
        
        # Create the security event record
        security_event = SecurityEvent.create!(event_params)
        
        successful_creations += 1
        
        if successful_creations % 20 == 0
          puts "Created #{successful_creations} security events so far..."
        end
      rescue => e
        puts "Error creating security event: #{e.message}"
      end
    end
    
    puts "Finished generating #{successful_creations} security events!"
  end

  desc "Generate test energy switches spread over the last month"
  task generate_monthly_switches: :environment do
    puts "Generating test energy switches spread over the last 30 days..."
    
    # Check if we have users
    users = SwitchUser.all
    
    if users.empty?
      puts "No users found in the database. Please run rake test_data:generate_users first."
      return
    end
    
    # Number of switches to generate
    num_switches = 60
    successful_creations = 0
    
    # Distribution pattern - more switches on weekdays, less on weekends
    # More recent days tend to have more switches
    day_weights = {}
    
    # Create timestamps for the last 30 days with varied distribution
    30.downto(0) do |days_ago|
      date = Date.today - days_ago.days
      
      # Lower weight for weekend days (Saturday = 6, Sunday = 0)
      day_of_week = date.wday
      is_weekend = (day_of_week == 0 || day_of_week == 6)
      
      # Older days have slightly lower weights
      recency_factor = 1.0 + ((30 - days_ago) / 75.0)
      
      # Calculate weight - higher means more likely to get switches on this day
      weight = is_weekend ? 0.5 : 1.0
      weight *= recency_factor
      
      # Add some randomness (80-120%)
      weight *= (0.8 + rand * 0.4)
      
      # Store the weight
      day_weights[date] = weight
    end
    
    # Normalize weights so they sum to 1.0
    total_weight = day_weights.values.sum
    day_weights.each_key do |date|
      day_weights[date] /= total_weight
    end
    
    puts "Day distribution weights:"
    day_weights.sort.each do |date, weight|
      puts "  #{date.to_s} (#{date.strftime('%A')}): #{(weight * 100).round(2)}%"
    end
    
    # Find existing energy tariffs to use
    available_energy_tariffs = EnergyTariff.all
    
    if available_energy_tariffs.empty?
      puts "Warning: No energy tariffs found. All switches will be created with draft status."
    end
    
    # Keep track of timestamps we've created for better distribution
    used_timestamps = []
    
    # Generate switches
    while successful_creations < num_switches
      # Select a user
      user = users.sample
      
      # Skip if user doesn't have required associations
      unless user.addresses.any? && user.payment_methods.any?
        next
      end
      
      # Select a day based on weights
      selected_date = nil
      r = rand
      cumulative = 0
      
      day_weights.each do |date, weight|
        cumulative += weight
        if r <= cumulative
          selected_date = date
          break
        end
      end
      
      # If somehow we didn't select a date, pick a random one
      selected_date ||= day_weights.keys.sample
      
      # Generate a random time on that day
      hour = rand(7..21)  # Between 7am and 9pm
      minute = rand(0..59)
      second = rand(0..59)
      timestamp = selected_date.to_time.change(hour: hour, min: minute, sec: second)
      
      # Try to create the switch
      begin
        # Get the user's gas and electricity tariffs
        gas_tariff = user.user_tariffs.find_by(energy_type: 'gas')
        electricity_tariff = user.user_tariffs.find_by(energy_type: 'electricity')
        
        # Skip if user doesn't have both tariffs
        unless gas_tariff && electricity_tariff
          next
        end
        
        # Get user's primary address and payment method
        address = user.addresses.first
        payment_method = user.payment_methods.first
        
        # Choose a status - draft, confirmed, submitted_to_supplier, supplier_processing, switched, rejected_by_supplier
        # Weight toward confirmed and switched to have realistic completed switches
        status = [:draft, :confirmed, :confirmed, :submitted_to_supplier, :supplier_processing, :switched, :switched, :switched, :rejected_by_supplier].sample
        
        # Create the energy switch
        energy_switch = EnergySwitch.new(
          switch_user: user,
          gas_energy_tariff: gas_tariff,
          electricity_energy_tariff: electricity_tariff,
          address: address,
          status: status,
          lived_three_years: [true, false].sample,
          payment_method: payment_method,
          reference_number: "SW#{rand(100000..999999)}",
          switch_date: Date.today + rand(10..60).days,
          viewed_at: [nil, timestamp - rand(1..24).hours].sample,
          created_at: timestamp,
          updated_at: timestamp
        )
        
        # If the status is not draft, set a switching_to_tariff if available
        if status != :draft && available_energy_tariffs.any?
          energy_switch.switching_to_tariff = available_energy_tariffs.sample
        end
        
        energy_switch.save!
        
        successful_creations += 1
        puts "Created switch #{successful_creations}/#{num_switches} for #{user.full_name} on #{timestamp.strftime('%Y-%m-%d %H:%M')}, status: #{status}"
      
      rescue => e
        puts "Failed to create switch: #{e.message}"
      end
    end
    
    puts "Finished generating #{successful_creations} energy switches spread over the last month!"
    
    # Show the distribution of switches by date
    actual_distribution = EnergySwitch.where('created_at > ?', 30.days.ago)
                                       .group("DATE(created_at)")
                                       .count
                                       .sort
                                       .to_h
    
    puts "\nActual distribution of switches by date:"
    actual_distribution.each do |date, count|
      day_name = Date.parse(date.to_s).strftime('%A')
      puts "  #{date} (#{day_name}): #{count} switches"
    end
  end
end 