require 'logger'

namespace :cleanup do
  desc "Clean up stale energy switches and related entities"
  task energy_switches: :environment do
    logger = Logger.new(STDOUT)
    logger.info "Starting cleanup of stale energy switches..."

    EnergySwitch.all.destroy_all
    UserTariff.all.destroy_all
    Address.all.destroy_all
    PaymentMethod.all.destroy_all
    SwitchUser.all.destroy_all
    SecurityEvent.all.destroy_all

    logger.info "Cleanup completed"
  end
end
