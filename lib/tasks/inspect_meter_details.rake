require 'logger'

namespace :inspect do
  desc "Inspect meter details"
  task given_meter_serial_number: :environment do
    meter_serial_number = ENV['meter_serial_number']
    
    if meter_serial_number.nil? || meter_serial_number.empty?
      puts "Error: Please provide a meter_serial_number argument"
      puts "Usage: rake inspect:given_meter_serial_number meter_serial_number=12345678"
      exit 1
    end
    
    electricity_xoserve_api = XoserveElectricityApiService.new
    result = electricity_xoserve_api.search_utility_address(meter_serial_number)
    pp result
  end

  task given_mpan: :environment do
    mpan = ENV['mpan']
    
    if mpan.nil? || mpan.empty?
      puts "Error: Please provide an mpan argument"
      puts "Usage: rake inspect:given_mpan mpan=1234567890"
      exit 1
    end
    
    electricity_xoserve_api = XoserveElectricityApiService.new
    result = electricity_xoserve_api.get_technical_details_by_mpan(mpan)
    pp result
  end

  task given_mprn: :environment do
    mprn = ENV['mprn']
    
    if mprn.nil? || mprn.empty?
      puts "Error: Please provide an mprn argument"
      puts "Usage: rake inspect:given_mprn mprn=1234567890"
      exit 1
    end
    
    gas_xoserve_api = XoserveGasApiService.new
    result = gas_xoserve_api.get_switch_data(mprn)
    pp result
  end
end
