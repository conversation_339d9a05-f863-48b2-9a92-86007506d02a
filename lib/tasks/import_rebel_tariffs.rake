namespace :import do
  desc "Starting Rebel Energy tariff import..."
  task rebel_tariffs: :environment do
    puts "Starting Rebel Energy tariff import..."

    # --- Rebel Energy ---
rebel_energy = Supplier.find_or_create_by!(name: "Rebel Energy")
  rebel_energy.update!(
    logo: "https://meet-george-public-assets.s3.eu-west-2.amazonaws.com/tulo-energy-logo.jpg",
    about: "Rebel Energy is a relatively new energy supplier in the UK, having launched in 2022. It focuses on providing gas and electricity sourced entirely from renewable sources, aiming to challenge traditional energy market practice",
    trustpilot_rating: 4.5
  )

  # --- Rebel Energy Tariff ---
  # Assuming rebel-sample-tariff.json exists and has the expected structure
  rebel_tariff_file_path = Rails.root.join("rebel-sample-tariff.json")
  if File.exist?(rebel_tariff_file_path)
  rebel_tariff_info = JSON.parse(File.read(rebel_tariff_file_path))[0] # Assuming the first element is the tariff
  puts "\nProcessing Rebel Energy tariff: #{rebel_tariff_info["description"]}"

  rebel_energy_tariff = rebel_energy.energy_tariffs.find_or_initialize_by(
    tariff_name: rebel_tariff_info["description"] # Assuming description is unique per supplier
  )
  rebel_energy_tariff.assign_attributes(
    display_name: rebel_tariff_info["description"],
    effective_from: rebel_tariff_info["availableFrom"],
    duration: 12,
    energy_type: 'both',
    tariff_type: :fixed,
    exit_fees: 30,
    payment_methods: [
      PaymentMethod::MONTHLY_VARIABLE_DIRECT_DEBIT,
      PaymentMethod::MONTHLY_VARIABLE_CASH_CHEQUE,
      PaymentMethod::MONTHLY_FIXED_DIRECT_DEBIT
    ]
    # supplier_id is implicitly handled
  )
    rebel_energy_tariff.save!
    puts "  Found or created EnergyTariff: #{rebel_energy_tariff.tariff_name} (ID: #{rebel_energy_tariff.id})"

    # Clear existing rates before adding new ones
    puts "  Clearing existing rates for #{rebel_energy_tariff.tariff_name}..."
    rebel_energy_tariff.energy_tariff_rates.destroy_all

    puts "  Creating new rates for #{rebel_energy_tariff.tariff_name}..."
    # Note: The following rates are hardcoded based on the original script.
    # Consider parsing these from rebel_tariff_info if the JSON contains regional rates.

    # Create Electricity Standard Rate (PC1) - Hardcoded Example
    EnergyTariffRate.create!(
      energy_tariff: rebel_energy_tariff,
      region: 'NORWEB', # Hardcoded
      region_id: 16,
      gsp_code: '_G',
      fuel_type: 'electricity',
      profile_class: 1,
      exit_fees: BigDecimal('60'),
      standing_charge_inc_vat: BigDecimal((0.3723*100).to_s),
      unit_rate_inc_vat: BigDecimal((0.5713*100).to_s) # Issue: Multiplication likely incorrect
    )
    puts "    - Created PC1 rate (Hardcoded)"

    # Create Electricity Economy 7 Rate (PC2) - Hardcoded Example
    EnergyTariffRate.create!(
      energy_tariff: rebel_energy_tariff,
      region: 'NORWEB', # Hardcoded
      region_id: 16,
      gsp_code: '_G',
      fuel_type: 'electricity',
      profile_class: 2,
      exit_fees: BigDecimal('60'),
      standing_charge_inc_vat: BigDecimal((0.3641*100).to_s),
      day_unit_rate_inc_vat: BigDecimal((0.7245*100).to_s),
      night_unit_rate_inc_vat: BigDecimal((0.2407*100).to_s) # Issue: Multiplication likely incorrect
    )
    puts "    - Created PC2 rate (Hardcoded)"

    # Create Gas Rate - Hardcoded Example
    EnergyTariffRate.create!(
      energy_tariff: rebel_energy_tariff,
      fuel_type: 'gas', # Hardcoded
      exit_fees: BigDecimal('60'),
      profile_class: -99, # Not applicable to gas
      standing_charge_inc_vat: BigDecimal((0.197*100).to_s), # Issue: Multiplication likely incorrect
      unit_rate_inc_vat: BigDecimal((0.2065*100).to_s) # Issue: Multiplication likely incorrect
    )
    puts "    - Created Gas rate (Hardcoded)"
    puts "  Finished processing Rebel Energy tariff."
  else
    puts "\nSkipping Rebel Energy tariff: rebel-sample-tariff.json not found."
  end


  rescue StandardError => e
    puts "Error importing Rebel Energy tariffs: #{e.message}"
    puts e.backtrace
  end
end
