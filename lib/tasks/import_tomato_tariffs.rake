namespace :import do
  desc "Import Tomato Energy tariffs from JSON file"
  task tomato_tariffs: :environment do
    begin
      puts "Starting Tomato Energy tariff import..."
      
      # Find or create Tomato Energy supplier
      tomato_energy = Supplier.find_or_create_by!(name: "Tomato Energy")
      tomato_energy.update!(
        logo: "https://meet-george-public-assets.s3.eu-west-2.amazonaws.com/tomato-energy-logo.jpg",
        about: "Tomato Energy is a UK-based energy supplier that focuses on providing renewable energy solutions",
        trustpilot_rating: 4.3
      )

      # Load tariff information from JSON file
      tariff_file_path = Rails.root.join("full-tomato-prime-tariff-json.json")
      unless File.exist?(tariff_file_path)
        puts "Error: Tariff file not found at #{tariff_file_path}"
        next
      end
      
      tariff_info = JSON.parse(File.read(tariff_file_path))

      # --- Tomato Energy Tariff ---
      puts "Processing Tomato Energy tariff: #{tariff_info["tariffName"]}"
      energy_tariff = tomato_energy.energy_tariffs.find_or_initialize_by(
        tariff_name: tariff_info["tariffName"] # Assuming tariff_name is unique per supplier
      )
      energy_tariff.assign_attributes(
        display_name: tariff_info["displayName"],
        product_code: tariff_info["productCode"],
        effective_from: tariff_info["effectiveFrom"],
        duration: 12,
        energy_type: 'electricity',
        tariff_type: :fixed,
        exit_fees: tariff_info["exitFee"],
        payment_methods: [PaymentMethod::MONTHLY_VARIABLE_DIRECT_DEBIT]
        # supplier_id is implicitly handled by the association
        # meter_types: ['Standard', 'Economy 7/Dual Rate'], # Consider adding if needed
      )
      energy_tariff.save!
      puts "  Found or created EnergyTariff: #{energy_tariff.tariff_name} (ID: #{energy_tariff.id})"

      # Clear existing rates before adding new ones
      puts "  Clearing existing rates for #{energy_tariff.tariff_name}..."
      energy_tariff.energy_tariff_rates.destroy_all

      puts "  Creating new rates for #{energy_tariff.tariff_name}..."
      tariff_info['regionalPricing'].each do |region_data|
        # Create Standard Rate (PC1)
        EnergyTariffRate.create!(
          energy_tariff: energy_tariff,
          region: region_data['region'],
          region_id: region_data['regionId'],
          gsp_code: region_data['gspCode'],
          fuel_type: 'electricity',
          profile_class: 1,
          exit_fees: BigDecimal(tariff_info["exitFee"].to_s),
          standing_charge_ex_vat: BigDecimal(region_data['standardRate']['standingCharge']['excludingVAT'].to_s),
          standing_charge_inc_vat: BigDecimal(region_data['standardRate']['standingCharge']['includingVAT'].to_s),
          unit_rate_ex_vat: BigDecimal(region_data['standardRate']['unitRate']['excludingVAT'].to_s),
          unit_rate_inc_vat: BigDecimal(region_data['standardRate']['unitRate']['includingVAT'].to_s),
          annual_bill: BigDecimal(region_data['averageAnnualBill']['standard'].to_s)
        )
        # puts "    - Created PC1 rate for GSP: #{region_data['gspCode']}"

        # Create Economy 7 Rate (PC2)
        # Check if economy7Rate data exists before creating
        if region_data['economy7Rate'] && region_data['economy7Rate']['standingCharge'] && region_data['economy7Rate']['dayRate'] && region_data['economy7Rate']['nightRate']
          EnergyTariffRate.create!(
            energy_tariff: energy_tariff,
            region: region_data['region'],
            region_id: region_data['regionId'],
            gsp_code: region_data['gspCode'],
            fuel_type: 'electricity',
            profile_class: 2,
            exit_fees: BigDecimal(tariff_info["exitFee"].to_s),
            standing_charge_ex_vat: BigDecimal(region_data['economy7Rate']['standingCharge']['excludingVAT'].to_s),
            standing_charge_inc_vat: BigDecimal(region_data['economy7Rate']['standingCharge']['includingVAT'].to_s),
            day_unit_rate_ex_vat: BigDecimal(region_data['economy7Rate']['dayRate']['excludingVAT'].to_s),
            day_unit_rate_inc_vat: BigDecimal(region_data['economy7Rate']['dayRate']['includingVAT'].to_s),
            night_unit_rate_ex_vat: BigDecimal(region_data['economy7Rate']['nightRate']['excludingVAT'].to_s),
            night_unit_rate_inc_vat: BigDecimal(region_data['economy7Rate']['nightRate']['includingVAT'].to_s),
            annual_bill: BigDecimal(region_data['averageAnnualBill']['economy7'].to_s)
          )
          # puts "    - Created PC2 rate for GSP: #{region_data['gspCode']}"
        else
          puts "    - Skipped PC2 rate creation for GSP: #{region_data['gspCode']} (missing economy7Rate data in JSON)"
        end
      end
      puts "  Finished processing Tomato Energy tariff."
      
    rescue StandardError => e
      puts "Error importing Tomato Energy tariffs: #{e.message}"
      puts e.backtrace
    end
  end
end 