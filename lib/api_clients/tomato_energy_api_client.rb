require 'faraday'
require 'json'

class TomatoEnergyApiClient
  def initialize
    @api_key = ENV['TOMATO_ENERGY_API_KEY']
    @base_url = ENV['TOMATO_ENERGY_API_URL']

    if @base_url.nil? || @base_url.empty?
      raise ArgumentError, "TOMATO_ENERGY_API_URL environment variable is not set"
    end
    
    # Remove trailing slash if present
    @base_url = @base_url.chomp('/')
    
    # Extract base path if already included in URL
    if @base_url.include?('/api/v1/applications')
      @base_path = ''
      puts "Base URL already contains API path: #{@base_url}"
    else
      @base_path = '/api/v1/applications'
      puts "Using API path: #{@base_path}"
    end
  end

  def create_application(application_params)
    validate_application_params(application_params)
    post(@base_path, { application: application_params })
  end

  private

  def validate_application_params(params)
    required_fields = %w[
      externalReference
      submittedDateTime
      type
      sender
      channel
      customer
      supplyAddress
      isBillingAddressSameAsSupplyAddress
      consent
      electricitySupply
      newElectricityTariff
    ]

    missing_fields = required_fields - params.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required fields: #{missing_fields.join(', ')}"
    end

    # Validate nested objects
    validate_customer(params['customer']) if params['customer']
    validate_address(params['supplyAddress']) if params['supplyAddress']
    validate_consent(params['consent']) if params['consent']
    
    # Validate billing address if not same as supply
    if params['isBillingAddressSameAsSupplyAddress'] == false
      raise ArgumentError, "billingAddress is required when isBillingAddressSameAsSupplyAddress is false" unless params['billingAddress']
      validate_address(params['billingAddress'])
    end

    # Validate tariff-related fields
    validate_electricity_fields(params)

    if params['newGasTariff'] || params['gasSupply']
      validate_gas_fields(params)
    end
  end

  def validate_customer(customer)
    required_fields = %w[title firstName lastName dateOfBirth emailAddress phoneNumber]
    missing_fields = required_fields - customer.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required customer fields: #{missing_fields.join(', ')}"
    end
  end

  def validate_address(address)
    required_fields = %w[postcode]
    missing_fields = required_fields - address.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required address fields: #{missing_fields.join(', ')}"
    end
  end

  def validate_consent(consent)
    required_fields = %w[creditCheck coolingOffPeriodPayment]
    missing_fields = required_fields - consent.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required consent fields: #{missing_fields.join(', ')}"
    end
  end

  def validate_electricity_fields(params)
    if params['newElectricityTariff']
      required_fields = %w[supplierReference reference name paymentType billingMethod]
      missing_fields = required_fields - params['newElectricityTariff'].keys.map(&:to_s)
      if missing_fields.any?
        raise ArgumentError, "Missing required electricity tariff fields: #{missing_fields.join(', ')}"
      end
    end

    if params['electricitySupply']
      required_fields = %w[mpan isEconomy7 isSmartMeter]
      missing_fields = required_fields - params['electricitySupply'].keys.map(&:to_s)
      if missing_fields.any?
        raise ArgumentError, "Missing required electricity supply fields: #{missing_fields.join(', ')}"
      end
    end
  end

  def validate_gas_fields(params)
    if params['newGasTariff']
      required_fields = %w[supplierReference reference name paymentType billingMethod]
      missing_fields = required_fields - params['newGasTariff'].keys.map(&:to_s)
      if missing_fields.any?
        raise ArgumentError, "Missing required gas tariff fields: #{missing_fields.join(', ')}"
      end
    end

    if params['gasSupply']
      required_fields = %w[mprn]
      missing_fields = required_fields - params['gasSupply'].keys.map(&:to_s)
      if missing_fields.any?
        raise ArgumentError, "Missing required gas supply fields: #{missing_fields.join(', ')}"
      end
    end
  end

  def post(path, body = {})    
    response = connection.post do |req|
      req.url path
      req.headers['Content-Type'] = 'application/json'
      req.headers['x-api-key'] = @api_key
      req.body = body.to_json
    end
    
    handle_response(response)
  end

  def handle_response(response)
    case response.status
    when 200..299
      JSON.parse(response.body)
    when 400..499
      raise "Client error: #{response.status} - #{response.body}"
    when 500..599
      raise "Server error: #{response.status} - #{response.body}"
    else
      raise "Unknown error: #{response.status} - #{response.body}"
    end
  end

  def connection
    @connection ||= Faraday.new(url: @base_url, ssl: { verify: false }) do |faraday|
      faraday.request :url_encoded
      faraday.adapter Faraday::Adapter::NetHttp
    end
  end
end

# Example usage
# client = TomatoEnergyApiClient.new

# application_params = {
#   'externalReference' => '81dfd469-6296-4866-a2c5-7a33ae8403e6',
#   'submittedDateTime' => '2025-01-18T03:07:29Z',
#   'type' => 'Electricity',
#   'sender' => { 'reference' => 'meetgeorge' },
#   'channel' => { 'reference' => 'Internet' },
#   'customer' => {
#     'title' => 'Mr',
#     'firstName' => 'John',
#     'lastName' => 'Doe',
#     'dateOfBirth' => '1990-01-01',
#     'emailAddress' => '<EMAIL>',
#     'phoneNumber' => '07700900000'
#   },
#   'supplyAddress' => {
#     'postcode' => 'SN12 6GB',
#     'thoroughfare' => '28 PRIMROSE DRIVE, MELKSHAM'
#   },
#   'isBillingAddressSameAsSupplyAddress' => true,
#   'consent' => {
#     'creditCheck' => true,
#     'coolingOffPeriodPayment' => true
#   },
#   'electricitySupply' => {
#     'mpan' => '*************',
#     'isEconomy7' => true,
#     'isSmartMeter' => true
#   },
#   "currentElectricityTariff": {
#     "supplierName": "Outfox The Market",
#     "name": "Standard Dual",
#     "paymentType": "Monthly Direct Debit"
#   },
#   'newElectricityTariff' => {
#     'supplierReference' => 'Tomato.energy',
#     'reference' => '**********',
#     'name' => 'Prime',
#     'paymentType' => 'Monthly Direct Debit',
#     'billingMethod' => 'eBilling'
#   },
#   'directDebit' => {
#     'bankAccountSortCode': '042909',
#     'bankAccountNumber': '********',
#     'bankAccountName': 'Joshua Winterton',
#     'paymentDayOfMonth': 1,
#     'regularPaymentAmount': 80.65
#   }
# }

# response = client.create_application(application_params)
# 
# {"application"=>{"electricityReference"=>"**********", "gasReference"=>nil}}
# 
# {
#   "application": {
#     "electricityReference": "**********",
#     "gasReference": null
#   },
#   "error": {
#     "message": "Validation warning",
#     "details": {
#       "validationFailures": [
#         {
#           "reference": "/application/directDebit",
#           "message": null
#         }
#       ]
#     }
#   }
# }