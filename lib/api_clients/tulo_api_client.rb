require 'faraday'
require 'json'

class TuloApiClient
  attr_reader :auth_key, :base_url

  def initialize
    @auth_key = ENV['TULO_ENERGY_API_KEY']
    @base_url = ENV['TULO_ENERGY_API_URL']
    
    if @base_url.nil? || @base_url.empty?
      raise ArgumentError, "TULO_ENERGY_API_URL environment variable is not set"
    end
  end

  # Main endpoint from the API documentation
  def sale_made(sale_params)
    # Ensure AuthKey is included in the request
    params = sale_params.dup
    params['AuthKey'] = @auth_key
    
    # Validate required parameters
    validate_sale_made_params(params)
    
    # Make the API request
    post('/api/CustomerQuote/SaleMadeV2', params)
  end

  private

  def validate_sale_made_params(params)
    # Check required top-level parameters from API documentation
    required_fields = %w[
      AuthKey SaleMadeBy SaleMadeOn SaleType SaleCustomerType
      CustomerTitle CustomerFirstName CustomerLastName
      CustomerAddressLine1 CustomerPostcode CustomerPhoneNumber
      PaymentMethod
    ]

    missing_fields = required_fields - params.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required fields: #{missing_fields.join(', ')}"
    end

    # Validate SaleType is one of the allowed options
    unless ['CoS', 'CoT', 'NC'].include?(params['SaleType'])
      raise ArgumentError, "SaleType must be one of: CoS, CoT, NC"
    end

    # Validate MPANs if provided
    if params['MPANs'] && params['MPANs'].is_a?(Array) && params['MPANs'].any?
      params['MPANs'].each do |mpan|
        validate_mpan(mpan)
      end
    end

    # Validate MPRNs if provided
    if params['MPRNs'] && params['MPRNs'].is_a?(Array) && params['MPRNs'].any?
      params['MPRNs'].each do |mprn|
        validate_mprn(mprn)
      end
    end

    # Validate AddressHistory
    # if params['AddressHistory'] && params['AddressHistory'].is_a?(Array)
    #   params['AddressHistory'].each do |address|
    #     validate_address_history(address)
    #   end
    # else
    #   raise ArgumentError, "AddressHistory must be an array with at least one entry"
    # end
  end

  def validate_mpan(mpan)
    required_fields = ['MPAN']
    
    # If not using industry data lookup, additional fields are required
    if mpan['UseIndustryDataLookup'] == false
      required_fields += %w[MeterType GSP PC MTC LLF SSC EnergisationStatus]
    end

    missing_fields = required_fields - mpan.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required MPAN fields: #{missing_fields.join(', ')}"
    end
  end

  def validate_mprn(mprn)
    required_fields = ['MPRN']
    
    # If not using industry data lookup, additional fields are required
    if mprn['UseIndustryDataLookup'] == false
      required_fields += %w[MeterType LDZ]
    end

    missing_fields = required_fields - mprn.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required MPRN fields: #{missing_fields.join(', ')}"
    end
  end

  def validate_address_history(address)
    required_fields = %w[AddressLine1 AddressLine2 TownCity PostCode]
    
    missing_fields = required_fields - address.keys.map(&:to_s)
    if missing_fields.any?
      raise ArgumentError, "Missing required AddressHistory fields: #{missing_fields.join(', ')}"
    end
  end

  def post(path, body = {})
    # Ensure AuthKey is included in the request
    if body.is_a?(Hash) && !body.key?('AuthKey') && @auth_key
      body = body.merge('AuthKey' => @auth_key)
    end
    
    response = connection.post do |req|
      req.url path
      req.headers['Content-Type'] = 'application/json'
      req.body = body.to_json
    end
    
    # Handle error responses
    if response.status == 400
      begin
        error_data = JSON.parse(response.body)
        # Check for both camelCase and PascalCase versions of error message key
        error_message = error_data['errorMessage'] || error_data['ErrorMessage']
        if error_message && !error_message.empty?
          raise "Error from Tulo API: #{error_message}"
        else
          raise "Error from Tulo API (status 400)"
        end
      rescue JSON::ParserError
        raise "Invalid JSON response from Tulo API (status 400): #{response.body}"
      end
    elsif response.status != 200
      raise "Unexpected response: #{response.status}, body: #{response.body}"
    end
    
    # Parse JSON response
    begin
      JSON.parse(response.body)
    rescue JSON::ParserError
      raise "Invalid JSON response: #{response.body}"
    end
  end

  def connection
    @connection ||= Faraday.new(url: @base_url) do |faraday|
      # Don't use middleware, we'll handle JSON encoding manually
      faraday.adapter Faraday.default_adapter
      
      # Set default headers
      faraday.headers['Accept'] = 'application/json'
      faraday.headers['Content-Type'] = 'application/json'
      faraday.headers['User-Agent'] = 'TuloApiClient/1.0'
    end
  end
end