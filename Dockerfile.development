FROM ruby:3.2.2

# Install packages needed to build gems
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y build-essential git libpq-dev libvips pkg-config


# Install Bundler
RUN gem install bundler

RUN mkdir -p /rails
WORKDIR /rails

ENV RAILS_ENV="development"

# Copy Gemfiles to avoid bundle install everytime an application file is changed
COPY Gemfile Gemfile.lock ./
RUN bundle install --jobs 20 --retry 5

# Copy the application code to the application directory
COPY . .

EXPOSE 3003
