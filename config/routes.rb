require "sidekiq/web"
require "sidekiq/cron/web"

Rails.application.routes.draw do
  use_doorkeeper

  unless Rails.env.production?
    mount Rswag::Ui::Engine => '/api-docs'
    mount Rswag::Api::Engine => '/api-docs'
  end
  
  # Secure Sidekiq web UI in production
  if Rails.env.production?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      # Secure comparison to avoid timing attacks
      ActiveSupport::SecurityUtils.secure_compare(Digest::SHA256.hexdigest(username), Digest::SHA256.hexdigest(ENV['SIDEKIQ_USERNAME'])) &
      ActiveSupport::SecurityUtils.secure_compare(Digest::SHA256.hexdigest(password), Digest::SHA256.hexdigest(ENV['SIDEKIQ_PASSWORD']))
    end
  end
  
  mount Sidekiq::Web => "/sidekiq"

  namespace :api do
    namespace :v1 do
      get "/health_check", to: proc { [200, {}, ["success"]] }

      # Verify onboarding tokens
      get '/onboarding/verify', to: 'onboarding_verify#verify'

      resources :energy_switches do
        member do
          get :switch_status
          get :tariff_comparison
          post :confirm_switch
        end
      end

      resources :onboarding, only: [:create] do
        collection do
          post :send_verification_code
          post :verify_code
        end
      end
      
      namespace :admin do
        post '/auth/login', to: 'auth#login'
        delete '/auth/logout', to: 'auth#logout'
        get '/auth/me', to: 'auth#me'
        
        resources :security_events, only: [:index, :show]
        resources :energy_switches, only: [:index, :show]
        resources :switch_users, only: %i[index show]
        resources :suppliers, only: %i[index show]
        
        # Dashboard endpoint
        get '/dashboard', to: 'dashboard#index'

        # Add OPTIONS routes for CORS preflight
        match '/auth/login', to: 'auth#options', via: :options
        match '/auth/logout', to: 'auth#options', via: :options
        match '/auth/me', to: 'auth#options', via: :options
        match '/security_events', to: 'security_events#options', via: :options
        match '/security_events/:id', to: 'security_events#options', via: :options
        match '/energy_switches', to: 'energy_switches#options', via: :options
        match '/energy_switches/:id', to: 'energy_switches#options', via: :options
      end
    end
  end
end
