# config/schedule.yml

# Existing scheduled jobs should remain here...

# Security monitoring reports
# daily_security_report:
#   cron: "0 0 * * *"  # Run at midnight every day
#   class: "SendSecurityReportJob"
#   args: ['daily']
#   queue: security
#   description: "Send daily security event report"

# hourly_security_report:
#   cron: "0 * * * *"  # Run at the beginning of every hour
#   class: "SendSecurityReportJob"
#   args: ['hourly']
#   queue: security
#   description: "Send hourly security event report for high-volume periods"

# weekly_security_report:
#   cron: "0 0 * * 0"  # Run at midnight on Sunday
#   class: "SendSecurityReportJob"
#   args: ['weekly']
#   queue: security
#   description: "Send weekly security event summary report"

# Energy switch processing
resubmit_pending_switches:
  cron: "0 * * * *"  # Run at the beginning of every hour
  class: "ResubmitPendingSwitchesJob"
  queue: priority
  description: "Resubmit confirmed energy switches without supplier submissions" 