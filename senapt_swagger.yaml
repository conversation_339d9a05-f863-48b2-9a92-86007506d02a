
security:
- SenaptApiAuth: []
components: 
  schemas:
    Address: 
      properties:
        organisation: 
          type: string
        department: 
          type: string
        poBox: 
          type: string
        buildingName: 
          type: string
        buildingNumber: 
          type: string
        subBuildingName: 
          type: string
        thoroughfare: 
          type: string
        dependentThoroughfare: 
          type: string
        dependentLocality: 
          type: string
        doubleDependentLocality: 
          type: string
        postTown: 
          type: string
        county: 
          type: string
        postcode: 
          type: string
        countryCode: 
          type: string
      type: object
    Application: 
      properties:
        externalReference: 
          type: string
        submittedDateTime: 
          type: string
        type: 
          type: string
        sender: 
          $ref: '#/components/schemas/Sender'
        channel: 
          $ref: '#/components/schemas/Channel'
        customer: 
          $ref: '#/components/schemas/Customer'
        currentElectricityTariff: 
          $ref: '#/components/schemas/CurrentTariff'
        currentGasTariff: 
          $ref: '#/components/schemas/CurrentTariff'
        newElectricityTariff: 
          $ref: '#/components/schemas/NewTariff'
        newGasTariff: 
          $ref: '#/components/schemas/NewTariff'
        electricityEstimate: 
          $ref: '#/components/schemas/ElectricityEstimate'
        gasEstimate: 
          $ref: '#/components/schemas/Estimate'
        electricitySupply: 
          $ref: '#/components/schemas/ElectricitySupply'
        gasSupply: 
          $ref: '#/components/schemas/GasSupply'
        supplyAddress: 
          $ref: '#/components/schemas/Address'
        isBillingAddressSameAsSupplyAddress: 
          type: boolean
        billingAddress: 
          $ref: '#/components/schemas/BillingAddress'
        billingAddressHistory: 
          items: 
            $ref: '#/components/schemas/BillingAddress'
          type: array
        directDebit: 
          $ref: '#/components/schemas/DirectDebit'
        priorityService: 
          $ref: '#/components/schemas/PriorityService'
        communicationPreferences: 
          $ref: '#/components/schemas/CommunicationPreferences'
        consent: 
          $ref: '#/components/schemas/Consent'
      type: object
    ApplicationSuccessful: 
      properties:
        electricityReference: 
          type: string
        gasReference: 
          type: string
      type: object
    BillingAddress: 
      properties:
        organisation: 
          type: string
        department: 
          type: string
        poBox: 
          type: string
        buildingName: 
          type: string
        buildingNumber: 
          type: string
        subBuildingName: 
          type: string
        thoroughfare: 
          type: string
        dependentThoroughfare: 
          type: string
        dependentLocality: 
          type: string
        doubleDependentLocality: 
          type: string
        postTown: 
          type: string
        county: 
          type: string
        postcode: 
          type: string
        countryCode: 
          type: string
        startDate: 
          type: string
      type: object
    Channel: 
      properties:
        reference: 
          type: string
      type: object
    CommunicationPreferences: 
      properties:
        marketingEmailOptIn: 
          type: boolean
        marketingTextOptIn: 
          type: boolean
        marketingMailOptIn: 
          type: boolean
        marketingPhoneOptIn: 
          type: boolean
      type: object
    Consent: 
      properties:
        creditCheck: 
          type: boolean
        coolingOffPeriodPayment: 
          type: boolean
      type: object
    CurrentTariff: 
      properties:
        supplierName: 
          type: string
        name: 
          type: string
        paymentType: 
          type: string
      type: object
    Customer: 
      properties:
        title: 
          type: string
        firstName: 
          type: string
        lastName: 
          type: string
        dateOfBirth: 
          type: string
        emailAddress: 
          type: string
        phoneNumber: 
          type: string
      type: object
    DirectDebit: 
      properties:
        bankAccountSortCode: 
          type: string
        bankAccountNumber: 
          type: string
        bankAccountName: 
          type: string
        paymentDayOfMonth: 
          format: int32
          type: integer
        regularPaymentAmount: 
          format: double
          type: number
      type: object
    ElectricityEstimate: 
      properties:
        estimatedAnnualCost: 
          format: int32
          type: integer
        estimatedAnnualSaving: 
          format: int32
          type: integer
        consumption: 
          format: int32
          type: integer
        economy7DaytimeConsumptionProportion: 
          format: double
          type: number
      type: object
    ElectricitySupply: 
      properties:
        mpan: 
          type: string
        isEconomy7: 
          type: boolean
        isSmartMeter: 
          type: boolean
      type: object
    Error: 
      properties:
        message: 
          type: string
        details: 
          $ref: '#/components/schemas/ErrorDetails'
      type: object
    ErrorDetails: 
      properties:
        validationFailures: 
          items: 
            $ref: '#/components/schemas/ValidationFailure'
          type: array
      type: object
    Estimate: 
      properties:
        estimatedAnnualCost: 
          format: int32
          type: integer
        estimatedAnnualSaving: 
          format: int32
          type: integer
        consumption: 
          format: int32
          type: integer
      type: object
    GasSupply: 
      properties:
        mprn: 
          type: string
      type: object
    MSMRequest: 
      properties:
        application: 
          $ref: '#/components/schemas/Application'
      type: object
    MSMResponse: 
      properties:
        application: 
          $ref: '#/components/schemas/ApplicationSuccessful'
        error: 
          $ref: '#/components/schemas/Error'
      type: object
    Need: 
      properties:
        reference: 
          type: string
      type: object
    NewTariff: 
      properties:
        supplierReference: 
          type: string
        reference: 
          type: string
        name: 
          type: string
        paymentType: 
          type: string
        billingMethod: 
          type: string
      type: object
    PriorityService: 
      properties:
        hasNeeds: 
          type: boolean
        needs: 
          items: 
            $ref: '#/components/schemas/Need'
          type: array
      type: object
    Sender: 
      properties:
        reference: 
          type: string
      type: object
    ValidationFailure: 
      properties:
        reference: 
          type: string
        message: 
          type: string
      type: object
  securitySchemes:
    SenaptApiAuth: 
      description: APISenaptSecurityProvider AuthorisedExternalParties key
      in: header
      name: x-api-key
      type: apiKey
info: 
  contact: 
    email: <EMAIL>
    name: Development Team
    url: https://senapt.co.uk/
  description: The SenaptEaaS Integration API Allows third party systems to integrate
    with the Senapt Energy-as-a-Swrvice platform to help exten the set of services
    available to energy consumers
  title: SenaptEaaS Integration API
  version: v1
openapi: 3.0.3
paths:
  /api/v1/applications: 
    post: 
      description: Creates a new domestic customer with details provided
      requestBody: 
        content:
          application/json: 
            schema: 
              $ref: '#/components/schemas/MSMRequest'
        required: true
      responses:
        default: 
          content:
            application/json: 
              schema: 
                $ref: '#/components/schemas/MSMResponse'
          description: Simple JSON containing create customer result
      summary: create a customer
