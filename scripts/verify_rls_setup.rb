#!/usr/bin/env ruby

# Script to verify RLS setup
# Usage: rails runner scripts/verify_rls_setup.rb

puts "🔒 Verifying Row Level Security Setup..."
puts "=" * 50

# List of tables that should have RLS enabled
tables_to_check = [
  'switch_users',
  'addresses', 
  'oauth_applications',
  'oauth_access_grants',
  'oauth_access_tokens',
  'security_events',
  'suppliers',
  'energy_tariffs',
  'energy_tariff_rates',
  'payment_methods',
  'supplier_submissions',
  'energy_switches',
  'user_tariffs',
  'admins',
  'xoserve_electricity_records',
  'schema_migrations',
  'ar_internal_metadata'
]

# Check if we're on Supabase or local
def check_environment
  result = ActiveRecord::Base.connection.execute("SELECT 1 FROM pg_roles WHERE rolname = 'service_role';")
  result.count > 0
rescue
  false
end

is_supabase = check_environment
puts "Environment: #{is_supabase ? 'Supabase' : 'Local PostgreSQL'}"
puts ""

# Check RLS status for each table
rls_enabled_count = 0
policy_count = 0

tables_to_check.each do |table_name|
  begin
    # Check if RLS is enabled
    rls_result = ActiveRecord::Base.connection.execute(<<-SQL)
      SELECT relrowsecurity 
      FROM pg_class 
      WHERE relname = '#{table_name}' AND relkind = 'r';
    SQL
    
    if rls_result.count > 0 && rls_result.first['relrowsecurity']
      puts "✅ #{table_name}: RLS enabled"
      rls_enabled_count += 1
      
      # Check policies
      policy_result = ActiveRecord::Base.connection.execute(<<-SQL)
        SELECT COUNT(*) as policy_count
        FROM pg_policies 
        WHERE tablename = '#{table_name}';
      SQL
      
      policy_count_for_table = policy_result.first['policy_count'].to_i
      policy_count += policy_count_for_table
      puts "   📋 Policies: #{policy_count_for_table}"
      
      # List policy names
      policy_names = ActiveRecord::Base.connection.execute(<<-SQL)
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = '#{table_name}';
      SQL
      
      policy_names.each do |policy|
        puts "      - #{policy['policyname']}"
      end
      
    else
      puts "❌ #{table_name}: RLS NOT enabled"
    end
    
  rescue => e
    puts "⚠️  #{table_name}: Error checking - #{e.message}"
  end
  
  puts ""
end

puts "=" * 50
puts "📊 Summary:"
puts "Tables with RLS enabled: #{rls_enabled_count}/#{tables_to_check.length}"
puts "Total policies created: #{policy_count}"

if rls_enabled_count == tables_to_check.length
  puts "🎉 SUCCESS: All tables have RLS properly configured!"
  puts ""
  puts "🔐 Security Status:"
  if is_supabase
    puts "- ✅ Direct Supabase API access is blocked for unauthorized users"
    puts "- ✅ Rails application can access data normally via service role"
  else
    puts "- ✅ RLS is enabled for future Supabase deployment"
    puts "- ✅ Rails application can access data normally in development"
  end
else
  puts "⚠️  WARNING: Some tables are missing RLS configuration!"
  puts "Run the RLS migration to fix this: rails db:migrate"
end

puts ""
puts "🧪 Testing Rails Access:"
begin
  count = EnergySwitch.count
  puts "✅ Rails can access data: EnergySwitch.count = #{count}"
rescue => e
  puts "❌ Rails access failed: #{e.message}"
end 