#!/usr/bin/env ruby

# Test script for Supabase Storage Service
# Usage: ruby scripts/test_supabase_storage.rb

require 'dotenv/load'
require_relative '../lib/services/supabase_storage_service'

# Mock Rails logger for testing
module Rails
  def self.logger
    @logger ||= Logger.new(STDOUT)
  end
end

def test_supabase_storage
  puts "🧪 Testing Supabase Storage Service..."
  
  # Check environment variables
  required_vars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY', 'SUPABASE_USER_BILLS_BUCKET_NAME']
  missing_vars = required_vars.select { |var| ENV[var].nil? || ENV[var].empty? }
  
  if missing_vars.any?
    puts "❌ Missing required environment variables: #{missing_vars.join(', ')}"
    puts "Please set these in your .env file"
    exit 1
  end
  
  begin
    # Initialize the service
    storage = Services::SupabaseStorageService.new
    puts "✅ SupabaseStorageService initialized successfully"
    
    # Test file operations
    test_key = "test/#{Time.now.to_i}_test.txt"
    test_content = "Hello from Supabase Storage! #{Time.now}"
    
    puts "\n📤 Testing file upload..."
    upload_success = storage.upload_file(test_key, test_content, 'text/plain')
    
    if upload_success
      puts "✅ File uploaded successfully"
      
      puts "\n🔗 Testing presigned URL generation..."
      presigned_url = storage.presigned_url(test_key)
      
      if presigned_url
        puts "✅ Presigned URL generated: #{presigned_url[0..50]}..."
        
        puts "\n📋 Testing file listing..."
        files = storage.list_files(prefix: 'test/')
        puts "✅ Found #{files.length} files in test/ prefix"
        
        puts "\n🗑️  Testing file deletion..."
        delete_success = storage.delete_file(test_key)
        
        if delete_success
          puts "✅ File deleted successfully"
          puts "\n🎉 All tests passed! Supabase Storage is working correctly."
        else
          puts "❌ Failed to delete file"
        end
      else
        puts "❌ Failed to generate presigned URL"
      end
    else
      puts "❌ Failed to upload file"
    end
    
  rescue => e
    puts "❌ Error testing Supabase Storage: #{e.message}"
    puts e.backtrace.first(5).join("\n")
    exit 1
  end
end

# Run the test
test_supabase_storage 