require_relative '../lib/api_clients/tulo_api_client'
require 'time'
require 'json'

# Initialize the client with your API key

if ENV['RAILS_ENV'] != 'production'
  client = TuloApiClient.new
else
  puts "This script should not be run in production environment"
  exit 1
end

# Test API connectivity
# if client.test_connectivity
#   puts "Successfully connected to Tulo Energy API"
# else
#   puts "Failed to connect to Tulo Energy API"
#   exit 1
# end

# Prepare parameters for a sale
sale_params = {
  "SaleMadeBy" => "MeetGeorgeTest",
  "SaleMadeOn" => "2024-05-17T00:00:00",
  "SaleValidatedBy" => "Joe Bloggs",
  "SaleValidatedOn" => "2024-05-17T18:25:43.511Z",
  "SaleValidationStatus" => "V",
  "SaleChannel" => "MeetGeorgeSaleChannel",
  "SaleType" => "CoS",
  "CustomerReference" => "CustRef001",
  "SaleCustomerType" => 1, # How to deal with this?
  "CustomerTitle" => "Mr",
  "CustomerFirstName" => "Testtulo",
  "CustomerLastName" => "Test",
  "CustomerAddressLine1" => "74",
  "CustomerAddressLine2" => " LEYBOURNE ROAD  ",
  "CustomerTownCity" => "UXBRIDGE",
  "CustomerCounty" => nil,
  "CustomerPostcode" => "UB10 9HF",
  "CustomerEmail" => "<EMAIL>",
  "CustomerPhoneNumber" => "07501454545",
  "CustomerDateOfBirth" => "1974-07-13T00:00:00",
  "PaymentMethod" => "Direct Debit",
  "ElectricEAC" => 3200,
  "GasEAC" => 7407,
  "ElectricityTariffName" => "Tulo Vari-One",
  "GasTariffName" => "Tulo Vari-One",
  "MPANs" => [{
    "MPAN" => "*************",
    "UseIndustryDataLookup" => true,
  }],
  "MPRNs" => [{
    "MPRN" => "**********",
    "UseIndustryDataLookup" => true
  }],
  "DDGasMonthlyAmount" => 47,
  "DDGasPaymentOn" => 1,
  "DDElectricMonthlyAmount" => 74,
  "DDElectricPaymentOn" => 1,
  "BankAccountName" => "Test Account Name",
  "BankAccountNumber" => "********",
  "BankSortCode" => "000000"
}

begin
  puts "Sending request to Tulo API with the following parameters:"
  puts JSON.pretty_generate(sale_params.reject { |k, _| k == 'AuthKey' })
  puts "\nAPI Base URL: #{client.instance_variable_get(:@base_url)}"
  
  # Submit the sale
  response = client.sale_made(sale_params)
  puts "Sale submitted successfully!"
  puts "Response: #{JSON.pretty_generate(response)}"
  puts "CustomerQuoteId: #{response['CustomerQuoteId']}"
rescue ArgumentError => e
  puts "Parameter validation error: #{e.message}"
  puts e.backtrace.join("\n")
rescue => e
  puts "API Error: #{e.message}"
  puts "Error class: #{e.class}"
  puts "Backtrace:"
  puts e.backtrace.join("\n")
  
  # Try to extract more information if it's a Faraday error
  if defined?(e.response) && e.response
    puts "\nResponse status: #{e.response[:status]}"
    puts "Response headers: #{e.response[:headers]}"
    puts "Response body: #{e.response[:body]}"
  end
end