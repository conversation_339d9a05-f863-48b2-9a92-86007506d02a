version: "3.7"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.development
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails s -p 3003 -b '0.0.0.0'"
    stdin_open: true
    tty: true
    volumes:
      - .:/rails
    ports:
      - "3003:3003"
    links:
      - postgres
    depends_on:
      - sidekiq
    env_file:
      - .env

  postgres:
    image: postgres:16
    platform: linux/arm64/v8
    container_name: postgresql_meet_george
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_HOST_AUTH_METHOD=trust

  redis:
    image: redis:7.0
    command: redis-server
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - '6379:6379'

  sidekiq:
    build:
      context: .
      dockerfile: Dockerfile.development
    depends_on:
      - redis
    command: bundle exec sidekiq
    volumes:
      - .:/rails
    env_file:
      - .env