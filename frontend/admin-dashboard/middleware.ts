import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Check if the path is a dashboard path
  const isDashboardPath = pathname.startsWith('/dashboard');
  
  // Check if the path is an auth path
  const isAuthPath = pathname.startsWith('/auth');
  
  // Get the token
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });
  
  // If the user is not authenticated and trying to access a dashboard path
  if (!token && isDashboardPath) {
    const url = new URL('/auth/login', request.url);
    return NextResponse.redirect(url);
  }
  
  // If the user is authenticated and trying to access an auth path
  if (token && isAuthPath) {
    const url = new URL('/dashboard/main', request.url);
    return NextResponse.redirect(url);
  }
  
  // If the user is authenticated and tries to access /dashboard directly
  if (token && pathname === '/dashboard') {
    const url = new URL('/dashboard/main', request.url);
    return NextResponse.redirect(url);
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard', '/dashboard/:path*', '/auth/:path*'],
}; 