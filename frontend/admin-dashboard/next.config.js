const nextConfig = {
  reactStrictMode: true, // Enable for better development error checking
  images: {
    // In Next.js 15, 'domains' is deprecated, use only remotePatterns
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'meet-george-public-assets.s3.eu-west-2.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/a/**',
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
        port: '',
        pathname: '**',
      },
    ],
    // Enable AVIF and WebP format support
    formats: ['image/avif', 'image/webp'],
  },
  async headers() {
    return [
      {
        // This applies to all API routes
        source: "/api/:path*",
        headers: [
          { key: "Access-Control-Allow-Credentials", value: "true" },
          { key: "Access-Control-Allow-Origin", value: process.env.NODE_ENV === 'production' 
            ? process.env.NEXT_PUBLIC_CORS_ALLOWED_ORIGIN || 'https://admin.meet-george.com'
            : "http://localhost:4000" 
          },
          { key: "Access-Control-Allow-Methods", value: "GET,DELETE,PATCH,POST,PUT" },
          { key: "Access-Control-Allow-Headers", value: "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, username, Authorization" },
        ]
      }
    ]
  }
};

module.exports = nextConfig;
