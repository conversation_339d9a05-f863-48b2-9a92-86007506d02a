{"name": "meet-george-admin-dashboard", "version": "1.0.0", "private": true, "scripts": {"init": "npm install && npx shadcn-ui@latest add --all", "dev": "next dev -p 4000 --turbo", "build": "next build", "start": "next start -p 4000", "lint": "next lint", "preinstall": "npx npm-force-resolutions", "update-next": "npm install next@15.0.0 eslint-config-next@15.0.0"}, "dependencies": {"@babel/traverse": "^7.24.6", "@codemirror/legacy-modes": "^6.4.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@heroicons/react": "^2.1.3", "@hookform/resolvers": "^3.9.0", "@material-tailwind/react": "2.1.9", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-table": "^8.17.3", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@uiw/codemirror-theme-tokyo-night": "^4.22.1", "@uiw/react-codemirror": "^4.22.1", "adblock-detect-react": "^1.3.1", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto": "^1.0.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.8", "endent": "^2.1.0", "eventsource-parser": "^1.1.2", "framer-motion": "^11.18.2", "input-otp": "^1.2.4", "lucide-react": "^0.379.0", "next": "^15.0.0", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "paddle-sdk": "^4.0.1", "php-serialize": "^4.1.1", "react": "18.3.1", "react-custom-scrollbars-2": "^4.5.0", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-github-btn": "^1.4.0", "react-github-button": "^0.1.11", "react-hook-form": "^7.52.2", "react-icons": "^5.2.1", "react-is": "^18.3.1", "react-markdown": "^9.0.1", "react-merge-refs": "^2.1.1", "react-resizable-panels": "^2.0.23", "react-router-dom": "^6.23.1", "react-to-print": "^2.15.1", "recharts": "2.15.2", "remark-gfm": "^4.0.0", "shadcn-ui": "^0.2.3", "sonner": "^1.5.0", "swr": "^2.3.3", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-rtl": "^0.9.0", "vaul": "^0.9.1", "web-vitals": "^4.0.1", "zod": "^3.23.8"}, "resolutions": {"minimist": "1.2.6"}, "overrides": {"recharts": {"react-is": "$react-is"}}, "devDependencies": {"@babel/cli": "^7.24.6", "@babel/core": "^7.24.6", "@ianvs/prettier-plugin-sort-imports": "^4.2.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.7.10", "@types/node": "^20.12.12", "@types/react": "^18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "^10.4.19", "encoding": "^0.1.13", "eslint": "8.37.0", "eslint-config-next": "15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-tailwindcss": "^3.17.0", "eventsource": "^2.0.2", "npm-force-resolutions": "^0.0.10", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.3", "typescript": "5.4.5"}, "prettier": {"arrowParens": "always", "singleQuote": true, "tabWidth": 2, "trailingComma": "none"}}