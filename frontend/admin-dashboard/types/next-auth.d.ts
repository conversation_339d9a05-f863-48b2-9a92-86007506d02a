import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface User {
    id: string;
    email: string;
    name: string;
    accessToken?: string;
    tokenExpires?: number;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
    };
    accessToken?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    email: string;
    name: string;
    accessToken?: string;
    tokenExpires?: number;
  }
} 