# Use the official Node.js image as the base image
FROM node:18.18.0

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Install the dependencies
RUN npm install -D @swc/cli @swc/core

RUN npm run init

# Copy the rest of the application code to the working directory
COPY . .

# Build the application for production
RUN npm run build

# Expose the port the app will run on
EXPOSE 4000

# Start the application in production mode
CMD ["npm", "run", "start"]
