# Upgrading to Next.js 15

This document outlines the steps taken to upgrade the admin dashboard from Next.js 14 to Next.js 15.

## Changes Made

1. Updated Next.js to version 15.0.0:
   ```json
   "next": "^15.0.0"
   ```

2. Updated ESLint config to match Next.js version:
   ```json
   "eslint-config-next": "15.0.0"
   ```

3. Updated `next.config.js`:
   - Replaced `domains` with `remotePatterns` in the images configuration (domains is deprecated in Next.js 15)

4. Created an `update-next` script to perform the installation:
   ```json
   "update-next": "npm install next@15.0.0 eslint-config-next@15.0.0"
   ```

5. All existing features like navigation, Image components, and route handling are compatible with Next.js 15

## How to Complete the Upgrade

1. Run the update script to install the new versions:
   ```bash
   npm run update-next
   ```

2. Test that everything works as expected:
   ```bash
   npm run dev
   ```

3. If any issues occur, check the Next.js 15 migration guide for more specific solutions: https://nextjs.org/docs/app/building-your-application/upgrading/version-15

## Key Benefits of Next.js 15

- Improved performance with streamlined rendering
- Enhanced type safety
- Better build times
- New features for image optimization
- More flexible routing options