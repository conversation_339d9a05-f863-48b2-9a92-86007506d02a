'use client'

import { useEffect, useState } from 'react'
import { notFound, useParams } from 'next/navigation'
import Link from 'next/link'
import { getEnergySwitchById, EnergySwitchDetails } from '@/lib/energy-switches'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { ArrowLeft, Calendar, User, Home, Phone, Mail, AlertCircle, CheckCircle, Clock, CreditCard, Zap, Flame, ArrowRight, Eye, FileText, ExternalLink } from 'lucide-react'
import { format, formatDistanceToNow } from 'date-fns'

// Status badge variants
const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'secondary'
    case 'pending':
      return 'outline'
    case 'cancelled':
      return 'secondary'
    case 'failed':
      return 'destructive'
    default:
      return 'default'
  }
}

// This page is under /dashboard, so it automatically inherits the dashboard layout with sidebar
export default function EnergySwitchDetailsPage() {
  const params = useParams();
  const id = params.id as string;
  
  const [switchDetails, setSwitchDetails] = useState<EnergySwitchDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchEnergySwitchDetails() {
      try {
        setLoading(true)
        const details = await getEnergySwitchById(id)
        setSwitchDetails(details)
      } catch (err) {
        console.error('Error fetching energy switch details:', err)
        setError('Failed to load energy switch details')
      } finally {
        setLoading(false)
      }
    }

    fetchEnergySwitchDetails()
  }, [id])

  if (error) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <Link href="/dashboard/energy-switches" passHref>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Energy Switches
            </Button>
          </Link>
        </div>
        <Card>
          <CardHeader className="bg-red-50">
            <CardTitle className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              Error
            </CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <p>Please try again later or contact support if the problem persists.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading || !switchDetails) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <Link href="/dashboard/energy-switches" passHref>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Energy Switches
            </Button>
          </Link>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent className="space-y-6">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href="/dashboard/energy-switches" passHref>
          <Button variant="outline" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Energy Switches
          </Button>
        </Link>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">
          Energy Switch Details{' '}
          <span className="text-muted-foreground font-normal ml-2">
            {switchDetails.reference_number}
          </span>
        </h1>
        <p className="text-muted-foreground">
          View detailed information about this energy switch
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Status Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col">
              <Badge className="w-fit" variant={getStatusBadgeVariant(switchDetails.displayStatus)}>
                {switchDetails.displayStatus.charAt(0).toUpperCase() + switchDetails.displayStatus.slice(1)}
              </Badge>
              <span className="text-xs text-muted-foreground mt-2">
                {switchDetails.status}
              </span>
              {switchDetails.failureReason && (
                <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {switchDetails.failureReason}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Date Information */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Dates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Created: {format(new Date(switchDetails.createdAt), 'PPP')}</span>
                <span className="text-xs text-muted-foreground">
                  ({formatDistanceToNow(new Date(switchDetails.createdAt), { addSuffix: true })})
                </span>
              </div>
              {switchDetails.completedAt && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Completed: {format(new Date(switchDetails.completedAt), 'PPP')}</span>
                  <span className="text-xs text-muted-foreground">
                    ({formatDistanceToNow(new Date(switchDetails.completedAt), { addSuffix: true })})
                  </span>
                </div>
              )}
              {!switchDetails.completedAt && switchDetails.displayStatus === 'pending' && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm">In progress</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* User Information */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Customer</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {switchDetails.user.firstName} {switchDetails.user.lastName}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{switchDetails.user.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{switchDetails.user.phoneNumber || 'N/A'}</span>
              </div>
              {switchDetails.user.requiresPsrSupport && (
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <span className="text-sm">Requires Priority Services Register support</span>
                </div>
              )}
              <div className="mt-3 pt-2 border-t border-gray-100">
                <Link href={`/dashboard/users/${switchDetails.user.id}`}>
                  <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                    <Eye className="h-4 w-4" />
                    <span>View User Details</span>
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6 lg:w-fit">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tariffs">Tariffs</TabsTrigger>
          <TabsTrigger value="payment">Payment</TabsTrigger>
          <TabsTrigger value="address">Address</TabsTrigger>
          <TabsTrigger value="bills">Bills</TabsTrigger>
          <TabsTrigger value="submissions">Submissions</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Switch Overview</CardTitle>
              <CardDescription>
                Key information about this energy switch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">From Supplier</h3>
                  <div className="bg-muted p-4 rounded-lg">
                    <p className="text-xl font-semibold">{switchDetails.fromSupplier}</p>
                    <div className="mt-4 space-y-2">
                      {switchDetails.energyTariffs.gas && (
                        <div className="flex items-start gap-2">
                          <Flame className="h-5 w-5 text-orange-500 mt-0.5" />
                          <div>
                            <p className="font-medium">{switchDetails.energyTariffs.gas.tariffName}</p>
                            <p className="text-sm text-muted-foreground">
                              {switchDetails.energyTariffs.gas.tariffType}, 
                              paid by {switchDetails.energyTariffs.gas.paymentMethod}
                            </p>
                          </div>
                        </div>
                      )}
                      
                      {switchDetails.energyTariffs.electricity && (
                        <div className="flex items-start gap-2">
                          <Zap className="h-5 w-5 text-yellow-500 mt-0.5" />
                          <div>
                            <p className="font-medium">{switchDetails.energyTariffs.electricity.tariffName}</p>
                            <p className="text-sm text-muted-foreground">
                              {switchDetails.energyTariffs.electricity.tariffType},
                              paid by {switchDetails.energyTariffs.electricity.paymentMethod}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-4">To Supplier</h3>
                  <div className="bg-muted p-4 rounded-lg">
                    <p className="text-xl font-semibold">{switchDetails.toSupplier}</p>
                    {switchDetails.switchingToTariff && (
                      <div className="mt-4 space-y-2">
                        <div className="flex items-start gap-2">
                          {switchDetails.switchingToTariff.fuelType === 'gas' ? (
                            <Flame className="h-5 w-5 text-orange-500 mt-0.5" />
                          ) : switchDetails.switchingToTariff.fuelType === 'electricity' ? (
                            <Zap className="h-5 w-5 text-yellow-500 mt-0.5" />
                          ) : (
                            <div className="flex">
                              <Flame className="h-5 w-5 text-orange-500 mt-0.5" />
                              <Zap className="h-5 w-5 text-yellow-500 mt-0.5 -ml-1" />
                            </div>
                          )}
                          <div>
                            <p className="font-medium">{switchDetails.switchingToTariff.tariffName}</p>
                            <p className="text-sm text-muted-foreground">
                              {switchDetails.switchingToTariff.tariffType}, 
                              {switchDetails.switchingToTariff.fuelType === 'dual' ? ' dual fuel' : ` ${switchDetails.switchingToTariff.fuelType} only`}
                            </p>
                            {switchDetails.switchingToTariff.estimatedSavings > 0 && (
                              <p className="text-sm text-green-600 font-medium mt-1">
                                Est. savings: £{switchDetails.switchingToTariff.estimatedSavings}/year
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Tariffs Tab */}
        <TabsContent value="tariffs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Energy Tariffs</CardTitle>
              <CardDescription>
                Detailed information about the customer's energy tariffs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Gas Tariff */}
              {switchDetails.energyTariffs.gas ? (
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Flame className="h-5 w-5 text-orange-500" />
                    <h3 className="text-lg font-medium">Gas Tariff</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 bg-muted p-4 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Supplier</p>
                      <p>{switchDetails.energyTariffs.gas.supplierName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Tariff Type</p>
                      <p className="capitalize">{switchDetails.energyTariffs.gas.tariffType}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Standing Charge</p>
                      <p>{switchDetails.energyTariffs.gas.standingCharge} p/day</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Unit Rate</p>
                      <p>{switchDetails.energyTariffs.gas.unitRate} p/kWh</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Meter Serial Number</p>
                      <p>{switchDetails.energyTariffs.gas.meterSerialNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">MPRN</p>
                      <p>{switchDetails.energyTariffs.gas.meterPointReferenceNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Annual Consumption</p>
                      <p>{switchDetails.energyTariffs.gas.annualConsumption} kWh</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Estimated Cost</p>
                      <p>£{switchDetails.energyTariffs.gas.estimatedMonthlyCost}/month (£{switchDetails.energyTariffs.gas.estimatedYearlyCost}/year)</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No gas tariff information available</p>
                </div>
              )}

              {/* Electricity Tariff */}
              {switchDetails.energyTariffs.electricity ? (
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Zap className="h-5 w-5 text-yellow-500" />
                    <h3 className="text-lg font-medium">Electricity Tariff</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 bg-muted p-4 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Supplier</p>
                      <p>{switchDetails.energyTariffs.electricity.supplierName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Tariff Type</p>
                      <p className="capitalize">{switchDetails.energyTariffs.electricity.tariffType}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Standing Charge</p>
                      <p>{switchDetails.energyTariffs.electricity.standingCharge} p/day</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Unit Rate</p>
                      <p>{switchDetails.energyTariffs.electricity.unitRate} p/kWh</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Meter Serial Number</p>
                      <p>{switchDetails.energyTariffs.electricity.meterSerialNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">MPAN</p>
                      <p>{switchDetails.energyTariffs.electricity.meterPointAdministrationNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Annual Consumption</p>
                      <p>{switchDetails.energyTariffs.electricity.annualConsumption} kWh</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Estimated Cost</p>
                      <p>£{switchDetails.energyTariffs.electricity.estimatedMonthlyCost}/month (£{switchDetails.energyTariffs.electricity.estimatedYearlyCost}/year)</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No electricity tariff information available</p>
                </div>
              )}

              {/* New Tariff */}
              {switchDetails.switchingToTariff && (
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <ArrowRight className="h-5 w-5 text-blue-500" />
                    <h3 className="text-lg font-medium">New Tariff</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 bg-muted p-4 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Supplier</p>
                      <p>{switchDetails.switchingToTariff.supplierName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Tariff Name</p>
                      <p>{switchDetails.switchingToTariff.tariffName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Tariff Type</p>
                      <p className="capitalize">{switchDetails.switchingToTariff.tariffType}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Fuel Type</p>
                      <p className="capitalize">{switchDetails.switchingToTariff.fuelType}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Payment Methods</p>
                      <p>{switchDetails.switchingToTariff.paymentMethods.map(m => m.replace('_', ' ')).join(', ')}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Estimated Savings</p>
                      <p className="text-green-600">£{switchDetails.switchingToTariff.estimatedSavings}/year</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Payment Tab */}
        <TabsContent value="payment" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Information</CardTitle>
              <CardDescription>
                Payment details associated with this energy switch
              </CardDescription>
            </CardHeader>
            <CardContent>
              {switchDetails.paymentMethod ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 bg-muted p-4 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Account Holder</p>
                      <p>{switchDetails.paymentMethod.accountHolder}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Payment Type</p>
                      <p className="capitalize">{switchDetails.paymentMethod.paymentType.replace('_', ' ')}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Account Number</p>
                      <p>****{switchDetails.paymentMethod.accountNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Sort Code</p>
                      <p>**-**-{switchDetails.paymentMethod.sortCode}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No payment information available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Address Tab */}
        <TabsContent value="address" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Address Information</CardTitle>
              <CardDescription>
                Address details associated with this energy switch
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-2">
                <Home className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">Supply Address</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    {switchDetails.address.fullAddress}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {switchDetails.address.postcode}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Bills Tab */}
        <TabsContent value="bills" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Bills Information</CardTitle>
              <CardDescription>
                Bill details and raw data associated with this energy switch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Bill URL */}
              {switchDetails.bill_url ? (
                <div>
                  <h3 className="text-lg font-medium mb-4">Bill Document</h3>
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-500" />
                      <a 
                        href={switchDetails.bill_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-2 py-2"
                      >
                        <span>View Original Bill</span>
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No bill document available</p>
                </div>
              )}

              {/* Bill JSON */}
              {switchDetails.bill_json ? (
                <div>
                  <h3 className="text-lg font-medium mb-4">Bill Data</h3>
                  <div className="bg-muted p-4 rounded-lg overflow-hidden">
                    <div className="max-h-96 overflow-auto">
                      <pre className="text-xs whitespace-pre-wrap break-words">
                        {(() => {
                          try {
                            const jsonData = typeof switchDetails.bill_json === 'string' 
                              ? JSON.parse(switchDetails.bill_json) 
                              : switchDetails.bill_json;
                            return JSON.stringify(jsonData, null, 2);
                          } catch (error) {
                            return `Error parsing JSON: ${error.message}`;
                          }
                        })()}
                      </pre>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No bill data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Submissions Tab */}
        <TabsContent value="submissions" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Submissions</CardTitle>
              <CardDescription>
                Submission history for this energy switch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Supplier Reference */}
              <div className="bg-muted p-4 rounded-lg mb-4">
                <h3 className="text-lg font-medium mb-2">Supplier Reference</h3>
                {switchDetails.supplier_reference ? (
                  <p className="text-lg font-mono">{switchDetails.supplier_reference}</p>
                ) : (
                  <p className="text-muted-foreground">No supplier reference available</p>
                )}
              </div>

              {/* Submissions List */}
              {switchDetails.supplierSubmissions && switchDetails.supplierSubmissions.length > 0 ? (
                <div>
                  <h3 className="text-lg font-medium mb-4">Submission History</h3>
                  <div className="space-y-4">
                    {switchDetails.supplierSubmissions.map((submission) => (
                      <div key={submission.id} className="border rounded-lg overflow-hidden">
                        <div className={`p-3 ${getSubmissionHeaderColor(submission.status)}`}>
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              {getSubmissionStatusIcon(submission.status)}
                              <span className="font-medium">
                                {submission.supplierName} - {submission.submissionType}
                              </span>
                            </div>
                            <Badge variant={getSubmissionBadgeVariant(submission.status)}>
                              {submission.status}
                            </Badge>
                          </div>
                        </div>
                        <div className="p-4 bg-card">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">Status</p>
                              <p>{submission.statusSummary}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">Attempt</p>
                              <p>#{submission.attemptNumber}</p>
                            </div>
                            {submission.submittedAt && (
                              <div>
                                <p className="text-sm font-medium text-muted-foreground">Submitted</p>
                                <p>{format(new Date(submission.submittedAt), 'PPP p')}</p>
                              </div>
                            )}
                            {submission.processedAt && (
                              <div>
                                <p className="text-sm font-medium text-muted-foreground">Processed</p>
                                <p>{format(new Date(submission.processedAt), 'PPP p')}</p>
                              </div>
                            )}
                            {submission.supplierReference && (
                              <div>
                                <p className="text-sm font-medium text-muted-foreground">Supplier Reference</p>
                                <p className="font-mono">{submission.supplierReference}</p>
                              </div>
                            )}
                            {submission.errorMessage && (
                              <div className="col-span-2">
                                <p className="text-sm font-medium text-muted-foreground">Error</p>
                                <p className="text-red-600">{submission.errorMessage}</p>
                              </div>
                            )}
                            {submission.rejectionReason && (
                              <div className="col-span-2">
                                <p className="text-sm font-medium text-muted-foreground">Rejection Reason</p>
                                <p className="text-red-600">{submission.rejectionReason}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No submission history available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Helper functions for the Submissions tab
const getSubmissionHeaderColor = (status: string) => {
  switch (status) {
    case 'successful':
      return 'bg-green-50'
    case 'failed':
      return 'bg-red-50'
    case 'rejected':
      return 'bg-red-50'
    case 'pending':
      return 'bg-blue-50'
    case 'submitted':
      return 'bg-yellow-50'
    case 'retry_scheduled':
      return 'bg-purple-50'
    default:
      return 'bg-slate-50'
  }
}

const getSubmissionStatusIcon = (status: string) => {
  switch (status) {
    case 'successful':
      return <CheckCircle className="h-5 w-5 text-green-600" />
    case 'failed':
      return <AlertCircle className="h-5 w-5 text-red-600" />
    case 'rejected':
      return <AlertCircle className="h-5 w-5 text-red-600" />
    case 'pending':
      return <Clock className="h-5 w-5 text-blue-600" />
    case 'submitted':
      return <Clock className="h-5 w-5 text-yellow-600" />
    case 'retry_scheduled':
      return <Clock className="h-5 w-5 text-purple-600" />
    default:
      return <Clock className="h-5 w-5 text-slate-600" />
  }
}

const getSubmissionBadgeVariant = (status: string) => {
  switch (status) {
    case 'successful':
      return 'secondary'
    case 'failed':
      return 'destructive'
    case 'rejected':
      return 'destructive'
    case 'pending':
      return 'outline'
    case 'submitted':
      return 'default'
    case 'retry_scheduled':
      return 'outline'
    default:
      return 'outline'
  }
} 