
'use client';

import UserList from '@/components/dashboard/users';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Loading skeleton for users page
function UserListSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-28" />
      </div>
      <div className="rounded-md border">
        <Skeleton className="h-[40px] w-full" />
        <div className="space-y-1 p-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-14 w-full" />
          ))}
        </div>
      </div>
    </div>
  );
}

export default function Account() {
  return (
    <Suspense fallback={<UserListSkeleton />}>
      <UserList />
    </Suspense>
  );
}
