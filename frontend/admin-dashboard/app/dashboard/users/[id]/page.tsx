'use client'

import { useEffect, useState } from 'react'
import { notFound, useParams } from 'next/navigation'
import Link from 'next/link'
import { getUserDetailsById, UserDetails } from '@/lib/users'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { ArrowLeft, Calendar, User, Home, Phone, Mail, AlertCircle, CheckCircle, Clock, CreditCard, Zap, Flame, ArrowRight } from 'lucide-react'
import { format, formatDistanceToNow } from 'date-fns'

// Status badge variants for energy switches
const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'secondary'
    case 'pending':
      return 'outline'
    case 'cancelled':
      return 'secondary'
    case 'failed':
      return 'destructive'
    default:
      return 'default'
  }
}

// This page is under /dashboard, so it automatically inherits the dashboard layout with sidebar
export default function UserDetailsPage() {
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const params = useParams();
  const userId = params.id as string;

  useEffect(() => {
    async function fetchUserDetails() {
      try {
        setLoading(true)
        const details = await getUserDetailsById(userId)
        setUserDetails(details)
      } catch (err) {
        console.error('Error fetching user details:', err)
        setError('Failed to load user details')
      } finally {
        setLoading(false)
      }
    }

    fetchUserDetails()
  }, [userId])

  if (error) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <Link href="/dashboard/users" passHref>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Users
            </Button>
          </Link>
        </div>
        <Card>
          <CardHeader className="bg-red-50">
            <CardTitle className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              Error
            </CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <p>Please try again later or contact support if the problem persists.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading || !userDetails) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <Link href="/dashboard/users" passHref>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Users
            </Button>
          </Link>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent className="space-y-6">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href="/dashboard/users" passHref>
          <Button variant="outline" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Users
          </Button>
        </Link>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">
          User Details
          <span className="text-muted-foreground font-normal ml-2">
            {userDetails.fullName}
          </span>
        </h1>
        <p className="text-muted-foreground">
          View detailed information about this user
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Basic Info Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {userDetails.fullName}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{userDetails.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{userDetails.phoneNumber || 'Not provided'}</span>
              </div>
              {userDetails.requiresPsrSupport && (
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <span className="text-sm">Requires Priority Services Register support</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Account Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Created: {format(new Date(userDetails.createdAt), 'PPP')}</span>
                <span className="text-xs text-muted-foreground">
                  ({formatDistanceToNow(new Date(userDetails.createdAt), { addSuffix: true })})
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Updated: {format(new Date(userDetails.updatedAt), 'PPP')}</span>
                <span className="text-xs text-muted-foreground">
                  ({formatDistanceToNow(new Date(userDetails.updatedAt), { addSuffix: true })})
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Quick Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-muted-foreground">Addresses</p>
                  <p className="text-lg font-semibold">{userDetails.addresses.length}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Payment Methods</p>
                  <p className="text-lg font-semibold">{userDetails.payment_methods.length}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Energy Switches</p>
                  <p className="text-lg font-semibold">{userDetails.energy_switches.length}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="addresses" className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:w-fit">
          <TabsTrigger value="addresses">Addresses</TabsTrigger>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="energy-switches">Energy Switches</TabsTrigger>
        </TabsList>
        
        {/* Addresses Tab */}
        <TabsContent value="addresses" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Addresses</CardTitle>
              <CardDescription>
                Addresses associated with this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userDetails.addresses.length > 0 ? (
                <div className="space-y-6">
                  {userDetails.addresses.map((address) => (
                    <div key={address.id} className="bg-muted p-4 rounded-lg">
                      <div className="flex items-start gap-2">
                        <Home className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">Address</p>
                          <p className="text-sm text-muted-foreground mt-1">
                            {address.fullAddress}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {address.postcode}
                          </p>
                          <p className="text-xs text-muted-foreground mt-2">
                            Added {formatDistanceToNow(new Date(address.createdAt), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No addresses found for this user</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Payment Methods Tab */}
        <TabsContent value="payment-methods" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>
                Payment methods associated with this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userDetails.payment_methods.length > 0 ? (
                <div className="space-y-6">
                  {userDetails.payment_methods.map((payment) => (
                    <div key={payment.id} className="bg-muted p-4 rounded-lg">
                      <div className="flex items-start gap-2">
                        <CreditCard className="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div>
                          <p className="font-medium">{payment.paymentType.replace('_', ' ')}</p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2 mt-2">
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">Account Holder</p>
                              <p className="text-sm">{payment.accountHolder}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">Account Number</p>
                              <p className="text-sm">****{payment.accountNumber}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">Sort Code</p>
                              <p className="text-sm">**-**-{payment.sortCode}</p>
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground mt-2">
                            Added {formatDistanceToNow(new Date(payment.createdAt), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No payment methods found for this user</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Energy Switches Tab */}
        <TabsContent value="energy-switches" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Energy Switches</CardTitle>
              <CardDescription>
                Energy switches associated with this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userDetails.energy_switches.length > 0 ? (
                <div className="space-y-6">
                  {userDetails.energy_switches.map((energySwitch) => (
                    <div key={energySwitch.id} className="bg-muted p-4 rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">
                            <Link href={`/dashboard/energy-switches/${energySwitch.id}`} className="text-blue-600 hover:underline">
                              {energySwitch.reference_number}
                            </Link>
                          </p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2 mt-2">
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">From Supplier</p>
                              <p className="text-sm">{energySwitch.fromSupplier}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">To Supplier</p>
                              <p className="text-sm">{energySwitch.toSupplier}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">Status</p>
                              <Badge className="mt-1" variant={getStatusBadgeVariant(energySwitch.displayStatus)}>
                                {energySwitch.displayStatus.charAt(0).toUpperCase() + energySwitch.displayStatus.slice(1)}
                              </Badge>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">Created</p>
                              <p className="text-sm">{format(new Date(energySwitch.createdAt), 'PPP')}</p>
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground mt-2">
                            Created {formatDistanceToNow(new Date(energySwitch.createdAt), { addSuffix: true })}
                            {energySwitch.completedAt && `, Completed ${formatDistanceToNow(new Date(energySwitch.completedAt), { addSuffix: true })}`}
                          </p>
                        </div>
                        <Link href={`/dashboard/energy-switches/${energySwitch.id}`}>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-4 border border-dashed rounded-lg">
                  <p className="text-muted-foreground text-center">No energy switches found for this user</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 