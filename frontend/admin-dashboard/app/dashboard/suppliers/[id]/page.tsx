'use client'

import { useEffect, useState } from 'react'
import { notFound, useParams } from 'next/navigation'
import Link from 'next/link'
import { getSupplierById, Supplier, EnergyTariff } from '@/lib/suppliers'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { ArrowLeft, Calendar, Star, Globe, Zap, Flame, ScrollText, ListFilter, AlertCircle } from 'lucide-react'
import { format } from 'date-fns'

// Determines the badge variant based on energy type
const getEnergyTypeBadgeVariant = (energyType: string) => {
  switch (energyType.toLowerCase()) {
    case 'gas':
      return 'destructive'
    case 'electricity':
      return 'secondary'
    case 'both':
      return 'outline'
    default:
      return 'default'
  }
}

// Determines the badge variant based on tariff type
const getTariffTypeBadgeVariant = (tariffType: string) => {
  switch (tariffType.toLowerCase()) {
    case 'fixed':
      return 'secondary'
    case 'variable':
      return 'default'
    default:
      return 'outline'
  }
}

export default function SupplierDetailsPage() {
  const [supplier, setSupplier] = useState<Supplier | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const params = useParams();
  const supplierId = params.id as string;

  useEffect(() => {
    async function fetchSupplierDetails() {
      try {
        setIsLoading(true)
        const data = await getSupplierById(supplierId)
        setSupplier(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching supplier details:', err)
        setError('Could not fetch supplier details')
        setSupplier(null)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSupplierDetails()
  }, [supplierId])

  // Handle loading state
  if (isLoading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <Link href="/dashboard/suppliers" passHref>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Suppliers
            </Button>
          </Link>
        </div>
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Skeleton className="h-[180px] rounded-xl" />
            <Skeleton className="h-[180px] rounded-xl" />
            <Skeleton className="h-[180px] rounded-xl" />
          </div>
          <Skeleton className="h-[400px] rounded-xl" />
        </div>
      </div>
    )
  }

  // Handle error state
  if (error || !supplier) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <Link href="/dashboard/suppliers" passHref>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Suppliers
            </Button>
          </Link>
        </div>
        <div className="bg-muted p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            <h2 className="text-lg font-medium">Error Loading Supplier</h2>
          </div>
          <p className="mt-2 text-sm text-muted-foreground">
            {error || 'Could not load supplier details. Please try again later.'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href="/dashboard/suppliers" passHref>
          <Button variant="outline" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Suppliers
          </Button>
        </Link>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">
          Supplier Details
          <span className="text-muted-foreground font-normal ml-2">
            {supplier.name}
          </span>
        </h1>
        <p className="text-muted-foreground">
          View detailed information about this supplier
        </p>
      </div>

      {/* Supplier Overview */}
      <div className="grid gap-6 md:grid-cols-2 mb-6">
        {/* Supplier Information */}
        <Card>
          <CardHeader>
            <CardTitle>Supplier Information</CardTitle>
            <CardDescription>Basic information about the supplier</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center">
              {supplier.logoUrl ? (
                <img 
                  src={supplier.logoUrl} 
                  alt={`${supplier.name} logo`} 
                  className="h-16 w-16 object-contain mr-4 rounded"
                />
              ) : (
                <div className="h-16 w-16 bg-muted flex items-center justify-center rounded mr-4">
                  <span className="text-xl font-bold">{supplier.name.charAt(0)}</span>
                </div>
              )}
              <div>
                <h3 className="font-bold text-xl">{supplier.name}</h3>
                {supplier.trustpilotRating !== null && (
                  <div className="flex items-center mt-1">
                    <Star className="h-4 w-4 text-yellow-500 mr-1" />
                    <span className="text-sm">{supplier.trustpilotRating} / 5 Trustpilot Rating</span>
                  </div>
                )}
              </div>
            </div>
            
            <Separator />
            
            {supplier.description && (
              <div>
                <p className="text-sm text-muted-foreground">{supplier.description}</p>
              </div>
            )}
            
            {supplier.websiteUrl && (
              <div className="flex items-center">
                <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                <a href={supplier.websiteUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">
                  {supplier.websiteUrl}
                </a>
              </div>
            )}
            
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-sm">
                Added on {format(new Date(supplier.createdAt), 'PPP')}
              </span>
            </div>
          </CardContent>
          <CardFooter>
            <div className="text-sm text-muted-foreground">
              ID: {supplier.id}
            </div>
          </CardFooter>
        </Card>

        {/* Energy Tariffs Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Energy Tariffs</CardTitle>
            <CardDescription>Summary of available energy tariffs</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-lg font-bold">
              {supplier.energyTariffs?.length || 0} Tariffs Available
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center">
                <Zap className="h-4 w-4 mr-2 text-yellow-500" />
                <span>
                  {supplier.energyTariffs?.filter(t => t.energyType === 'electricity' || t.energyType === 'both').length || 0} Electricity Tariffs
                </span>
              </div>
              <div className="flex items-center">
                <Flame className="h-4 w-4 mr-2 text-red-500" />
                <span>
                  {supplier.energyTariffs?.filter(t => t.energyType === 'gas' || t.energyType === 'both').length || 0} Gas Tariffs
                </span>
              </div>
              <div className="flex items-center">
                <ScrollText className="h-4 w-4 mr-2 text-green-500" />
                <span>
                  {supplier.energyTariffs?.filter(t => t.tariffType === 'fixed').length || 0} Fixed Tariffs
                </span>
              </div>
              <div className="flex items-center">
                <ListFilter className="h-4 w-4 mr-2 text-blue-500" />
                <span>
                  {supplier.energyTariffs?.filter(t => t.tariffType === 'variable').length || 0} Variable Tariffs
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Energy Tariffs Detail */}
      <Card>
        <CardHeader>
          <CardTitle>Energy Tariffs</CardTitle>
          <CardDescription>Detailed information about all energy tariffs</CardDescription>
        </CardHeader>
        <CardContent>
          {supplier.energyTariffs && supplier.energyTariffs.length > 0 ? (
            <div className="space-y-6">
              {supplier.energyTariffs.map((tariff) => (
                <div key={tariff.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="font-bold text-lg">{tariff.tariffName}</h3>
                      <div className="flex space-x-2 mt-1">
                        <Badge variant={getEnergyTypeBadgeVariant(tariff.energyType)}>
                          {tariff.energyType === 'gas' ? (
                            <Flame className="h-3 w-3 mr-1" />
                          ) : tariff.energyType === 'electricity' ? (
                            <Zap className="h-3 w-3 mr-1" />
                          ) : (
                            <>
                              <Flame className="h-3 w-3 mr-1" />
                              <Zap className="h-3 w-3 mr-1" />
                            </>
                          )}
                          {tariff.energyType.charAt(0).toUpperCase() + tariff.energyType.slice(1)}
                        </Badge>
                        <Badge variant={getTariffTypeBadgeVariant(tariff.tariffType)}>
                          {tariff.readableTariffType}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {tariff.productCode && <div>Product Code: {tariff.productCode}</div>}
                      {tariff.exitFees !== null && <div>Exit Fee: £{tariff.exitFees}</div>}
                    </div>
                  </div>
                  
                  {/* Date information */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Available From</div>
                      <div>{tariff.availableFrom ? format(new Date(tariff.availableFrom), 'PPP') : 'N/A'}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Available To</div>
                      <div>{tariff.availableTo ? format(new Date(tariff.availableTo), 'PPP') : 'Ongoing'}</div>
                    </div>
                  </div>
                  
                  <Separator className="my-4" />
                  
                  {/* Tariff rates */}
                  <div>
                    <h4 className="font-semibold mb-2">Tariff Rates</h4>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-muted">
                            <th className="text-left p-2 text-sm">Fuel Type</th>
                            <th className="text-left p-2 text-sm">GSP/Profile Class</th>
                            <th className="text-left p-2 text-sm">Standing Charge (inc VAT)</th>
                            <th className="text-left p-2 text-sm">Unit Rate (inc VAT)</th>
                            <th className="text-left p-2 text-sm">Day Rate (inc VAT)</th>
                            <th className="text-left p-2 text-sm">Night Rate (inc VAT)</th>
                          </tr>
                        </thead>
                        <tbody>
                          {tariff.tariffRates.map((rate) => (
                            <tr key={rate.id} className="border-b">
                              <td className="p-2 text-sm">{rate.fuelType}</td>
                              <td className="p-2 text-sm">{rate.gspCode || rate.profileClass || 'N/A'}</td>
                              <td className="p-2 text-sm">
                                {rate.standingChargeIncVat !== null && rate.standingChargeIncVat !== undefined
                                  ? `${Number(rate.standingChargeIncVat).toFixed(2)}p`
                                  : 'N/A'}
                              </td>
                              <td className="p-2 text-sm">
                                {rate.unitRateIncVat !== null && rate.unitRateIncVat !== undefined
                                  ? `${Number(rate.unitRateIncVat).toFixed(2)}p`
                                  : 'N/A'}
                              </td>
                              <td className="p-2 text-sm">
                                {rate.dayUnitRateIncVat !== null && rate.dayUnitRateIncVat !== undefined
                                  ? `${Number(rate.dayUnitRateIncVat).toFixed(2)}p`
                                  : 'N/A'}
                              </td>
                              <td className="p-2 text-sm">
                                {rate.nightUnitRateIncVat !== null && rate.nightUnitRateIncVat !== undefined
                                  ? `${Number(rate.nightUnitRateIncVat).toFixed(2)}p`
                                  : 'N/A'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  <div className="mt-4 text-xs text-muted-foreground">
                    Tariff ID: {tariff.id}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No energy tariffs available for this supplier.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 