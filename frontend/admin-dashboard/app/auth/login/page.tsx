'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LockIcon, MailIcon, LogInIcon } from "lucide-react";

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError('Please enter both email and password');
      toast.error('Please enter both email and password');
      return;
    }
    
    setIsLoading(true);
    setError('');
    
    try {
      console.log('Attempting to sign in with:', email);
      
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });
      
      console.log('Sign in result:', result);
      
      if (result?.error) {
        // Parse the error message for better user feedback
        let errorMessage = result.error;
        
        if (errorMessage.includes('Backend unavailable')) {
          errorMessage = 'Unable to connect to the authentication server. Using fallback authentication.';
          
          if (errorMessage.includes('no fallback credentials matched')) {
            errorMessage = 'Server is unavailable and your credentials don\'t match the fallback development account.';
          }
        } else if (errorMessage.includes('Connection error')) {
          errorMessage = 'Connection to authentication server failed. Please try again later.';
        }
        
        setError(errorMessage);
        toast.error(errorMessage);
      } else if (result?.ok) {
        toast.success('Login successful');
        router.push('/dashboard/main');
      } else {
        setError('An unexpected error occurred');
        toast.error('An unexpected error occurred');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Login failed. Please try again.');
      toast.error('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background/95 px-4 py-12 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md shadow-lg border-muted">
        <CardHeader className="space-y-2 text-center">
          <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <LogInIcon className="h-6 w-6 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold tracking-tight">Meet George Dashboard</CardTitle>
          <CardDescription className="text-muted-foreground">
            Enter your credentials to access the admin dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <form onSubmit={handleLogin} className="space-y-5">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">Email</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <MailIcon className="h-5 w-5 text-muted-foreground" />
                </div>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="relative">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <LockIcon className="h-5 w-5 text-muted-foreground" />
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10"
                  required
                />
              </div>
            </div>
            <Button type="submit" className="w-full font-medium" disabled={isLoading}>
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⚪</span>
                  Logging in...
                </>
              ) : (
                <>Sign in</>
              )}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col items-center border-t pt-5">
          <p className="text-sm text-muted-foreground">
            Admin access only
          </p>
        </CardFooter>
      </Card>
    </div>
  );
} 