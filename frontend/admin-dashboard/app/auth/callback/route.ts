import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

import type { NextRequest } from 'next/server'
import type { Database } from '@/types/types_db'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
  }

  // URL to redirect to after sign in process completes 
  return NextResponse.redirect(`${requestUrl.origin}/dashboard/main`)
}