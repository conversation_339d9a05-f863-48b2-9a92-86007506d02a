'use client';

import { useEffect } from 'react';

export const ClientLayoutScripts = () => {
  // Run script only on client
  useEffect(() => {
    // Apply fixes without affecting theme class
    // No need to add layout-stable class as it's already added in layout.tsx
    
    // Force scrollbar to be present
    document.documentElement.style.overflowY = 'scroll';
    
    // Calculate scrollbar width
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    document.documentElement.style.setProperty('--scrollbar-width', scrollbarWidth + 'px');
    
    // Function to ensure navbar is fixed and doesn't shift
    function fixNavbar() {
      // Wait briefly for DOM to be ready
      setTimeout(() => {
        // Select navbar and its elements
        const navbar = document.querySelector('nav');
        if (navbar) {
          // Ensure it has the fixed-nav class
          navbar.classList.add('fixed-nav');
          
          // Get all dropdowns in the document
          const dropdowns = document.querySelectorAll('[data-state]');
          
          // Add a class to track if any dropdown is open
          dropdowns.forEach(dropdown => {
            if (dropdown.getAttribute('data-state') === 'open') {
              document.documentElement.classList.add('dropdown-open');
            }
            
            // Monitor dropdown state changes
            const observer = new MutationObserver(mutations => {
              mutations.forEach(mutation => {
                if (mutation.attributeName === 'data-state') {
                  const isOpen = dropdown.getAttribute('data-state') === 'open';
                  if (isOpen) {
                    document.documentElement.classList.add('dropdown-open');
                  } else {
                    document.documentElement.classList.remove('dropdown-open');
                  }
                }
              });
            });
            
            observer.observe(dropdown, { attributes: true });
          });
          
          // Find the navbar links container
          const linksContainer = document.querySelector('.nav-links-container');
          if (linksContainer && navbar.contains(linksContainer)) {
            // Make sure it's in absolute position
            (linksContainer as HTMLElement).style.position = 'absolute';
            (linksContainer as HTMLElement).style.right = '16px';
            (linksContainer as HTMLElement).style.top = '50%';
            (linksContainer as HTMLElement).style.transform = 'translateY(-50%)';
          }
        }
      }, 0);
    }
    
    // Run fix immediately
    fixNavbar();
    
    // Run fix when DOM is loaded
    document.addEventListener('DOMContentLoaded', fixNavbar);
    
    // Run fix after any content changes
    const observer = new MutationObserver(fixNavbar);
    observer.observe(document.documentElement, { 
      childList: true, 
      subtree: true 
    });
    
    // Run fix on resize
    window.addEventListener('resize', () => {
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
      document.documentElement.style.setProperty('--scrollbar-width', scrollbarWidth + 'px');
      fixNavbar();
    });

    // Cleanup function
    return () => {
      document.removeEventListener('DOMContentLoaded', fixNavbar);
      if (observer) {
        observer.disconnect();
      }
    };
  }, []);
  
  return null;
}; 