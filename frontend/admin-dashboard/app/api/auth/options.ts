import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { NextAuthOptions } from 'next-auth';
import axios from 'axios';

// Define API URL
const API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: 'Email and Password',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            return null;
          }

          try {
            const response = await axios.post(`${API_URL}/api/v1/admin/auth/login`, {
              email: credentials.email,
              password: credentials.password,
            }, {
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              }
            });

            if (response.data.success) {
              // Note: This won't work in the NextAuth authorize callback
              // because it runs on the server. Token will be stored in the jwt callback
              // when it's available on the client.
              const user = {
                id: response.data.admin.id,
                email: response.data.admin.email,
                name: response.data.admin.name,
                accessToken: response.data.access_token,
                tokenExpires: response.data.expires_in,
              };
              return user;
            } else {
              throw new Error('Invalid email or password');
            }
          } catch (apiError: any) {
            // For 401/403 responses, it's an authentication failure
            if (apiError.response && (apiError.response.status === 401 || apiError.response.status === 403)) {
              throw new Error('Invalid email or password');
            }
            
            // For other errors, provide a more specific message
            throw new Error(`Connection error: ${apiError.message}`);
          }
          
          // If we get here, authentication failed
          return null;
        } catch (error: any) {
          throw error; // Re-throw the error to preserve the specific message
        }
      },
    }),
  ],
  debug: process.env.NODE_ENV === 'development',
  pages: {
    signIn: '/auth/login',
    signOut: '/auth/login',
    error: '/auth/login',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        // Store the access token in the JWT
        if (user.accessToken) {
          token.accessToken = user.accessToken;
          // Store actual token expiration time
          token.tokenExpiry = Date.now() + (user.tokenExpires * 1000);
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          id: token.id as string,
          email: token.email as string,
          name: token.name as string,
        };
        // Add the access token to the session
        if (token.accessToken) {
          session.accessToken = token.accessToken as string;
          
          // Add token expiry to session for client-side validation
          if (token.tokenExpiry) {
            session.expires = new Date(token.tokenExpiry as number).toISOString();
          }
        }
      }
      return session;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
}; 