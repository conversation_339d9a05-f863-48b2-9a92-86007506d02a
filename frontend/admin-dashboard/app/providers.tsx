'use client';

import { SessionProvider } from 'next-auth/react';
import { SWRConfig } from 'swr';
import React from 'react';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <SWRConfig 
        value={{
          revalidateOnFocus: false,
          revalidateIfStale: true,
          revalidateOnReconnect: true,
          dedupingInterval: 15000,
          errorRetryCount: 3,
          shouldRetryOnError: false,
          provider: () => {
            // Simple memory cache for storing SWR data
            const cache = new Map();
            return cache;
          }
        }}
      >
        {children}
      </SWRConfig>
    </SessionProvider>
  );
}