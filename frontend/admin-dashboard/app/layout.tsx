import { PropsWithChildren } from 'react';
import '@/styles/globals.css';
import { ThemeProvider } from './theme-provider';
import { Providers } from './providers';
import { ClientLayoutScripts } from './components/client-layout-scripts';
import * as React from 'react';

export const dynamic = 'force-dynamic';

export default function RootLayout({
  // Layouts must accept a children prop.
  // This will be populated with nested layouts or pages
  children
}: PropsWithChildren) {
  return (
    <html 
      lang="en" 
      className="light layout-stable"
      style={{ colorScheme: "light" }}
    >
      <head>
        <title>
          MG Dashboard
        </title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        
        <link rel="icon" href="/img/favicon.ico" />
      </head>
      <body id={'root'} className="loading bg-white">
        <Providers>
        <ClientLayoutScripts />
          <main id="skip">{children}</main>
        </Providers>
      </body>
    </html>
  );
}
