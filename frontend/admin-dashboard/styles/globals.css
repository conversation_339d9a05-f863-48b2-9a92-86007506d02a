@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force scrollbar to always be visible to prevent layout shifts */
html {
  overflow-y: scroll; /* Always show vertical scrollbar to prevent layout shifts */
  scroll-behavior: smooth;
  font-family: 'Inter', sans-serif;
  color-scheme: unset !important;
  /* Ensure content doesn't shift when modals/dropdowns appear */
  padding-right: 0 !important; /* Prevent automatic padding adjustments by browsers */
}

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 30 100% 50%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 30 100% 50%;
  --radius: 0.5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;

  /* Colors for data visualization */
  --color-desktop: hsl(var(--chart-1));
  --color-mobile: hsl(var(--chart-2));
}

/* Dark theme removed */

body {
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
  /* New approach to prevent layout shifts when dropdowns appear */
  width: 100%; 
  position: relative;
  /* Remove the margin-right adjustment that could cause issues */
  margin-right: 0;
}

/* Prevent dropdowns from causing layout shifts */
.dropdown-open {
  /* Prevent any layout shifts when dropdowns open */
  overflow: visible !important;
}

/* Adjust dropdown containers to prevent shifting */
[data-state="open"] {
  overflow: visible;
  z-index: 50;
}

option {
  color: black;
}

p {
  letter-spacing: 0px;
}

img {
  pointer-events: none;
}
::-moz-selection {
  /* Code for Firefox */
  color: white;
  background: #09090B;
}

::selection {
  color: white;
  background: #09090B;
}

input.defaultCheckbox {
  color: white;
}

input.defaultCheckbox::before {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.66662 10.115L12.7946 3.98633L13.7379 4.92899L6.66662 12.0003L2.42395 7.75766L3.36662 6.81499L6.66662 10.115Z' fill='white'/%3E%3C/svg%3E%0A");
  opacity: 0;
  height: 16px;
  width: 16px;
  top: 0;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0px);
}

input:checked.defaultCheckbox::before {
  opacity: 1;
}

/* Prevent header/navbar shifting when dropdowns open */
nav {
  width: auto !important;
  right: 0.75rem !important; /* 3 in tailwind = 0.75rem */
  left: 0.75rem !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  position: fixed !important;
}

@media (min-width: 1280px) {
  nav {
    margin-left: 0 !important;
    margin-right: 0 !important;
    left: 358px !important;
    right: 0.75rem !important;
  }
}

/* Prevent nav content from shifting */
nav > div {
  position: relative !important;
}

nav > div:first-child {
  flex-grow: 1;
}

nav > div:last-child {
  flex-shrink: 0;
  width: auto !important;
}

/* Fix for dropdown buttons */
.dropdown-open [role="menu"],
[data-state="open"][role="menu"] {
  margin-right: 0 !important;
}

/* Additional dropdown-specific styling */
.dropdown-open nav,
[data-state="open"] ~ nav {
  padding-right: 0 !important;
}

/* Ensure buttons in navbar don't shift */
.layout-stable nav > div:last-child button,
.layout-stable nav > div:last-child a {
  position: relative !important;
  transform: none !important;
}

/* Fix for the avatar and other circular buttons */
.layout-stable nav > div:last-child .rounded-full {
  transform: none !important;
  position: relative !important;
  right: 0 !important;
}

/* Prevent any space calculation issues */
.layout-stable body,
.dropdown-open body {
  overflow-x: hidden !important;
}

/* Force the navbar to maintain fixed width */
@media (min-width: 768px) {
  nav > div:last-child {
    width: auto !important;
    min-width: auto !important;
    max-width: none !important;
  }
}

/* Fixed navbar positioning */
.fixed-nav {
  left: 12px !important;
  right: 12px !important;
  width: auto !important;
  position: fixed !important;
}

/* Ensure navbar links don't shift */
.nav-links-container {
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 100;
}

/* Screen size adjustments */
@media (min-width: 1280px) {
  .fixed-nav {
    left: 358px !important;
  }
}

/* Remove all previous navbar styling that might conflict */
nav:not(.fixed-nav) {
  display: none !important;
}

/* Prevent any scrollbar-related shifts */
.dropdown-open,
.layout-stable,
html[data-state="open"] {
  overflow-y: scroll !important;
  padding-right: 0 !important;
}
 