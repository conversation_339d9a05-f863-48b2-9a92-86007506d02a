import { z } from "zod";
import { api } from "./api-client";

// Schema for security event data
export const securityEventSchema = z.object({
  id: z.union([z.string(), z.number()]),
  event_type: z.string(),
  email: z.string().nullable().optional(),
  ip_address: z.string().nullable().optional(),
  details: z.record(z.any()).nullable().optional(),
  created_at: z.string(),
  updated_at: z.string()
});

export type SecurityEvent = z.infer<typeof securityEventSchema>;

// Response type for the security events API
interface SecurityEventsResponse {
  events: SecurityEvent[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Interface for getSecurityEvents parameters
export interface GetSecurityEventsParams {
  page?: number;
  limit?: number;
  event_type?: string;
  email?: string;
  ip_address?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
}

// Get security events with pagination and filtering
export async function getSecurityEvents(params: GetSecurityEventsParams = {}): Promise<SecurityEventsResponse> {
  try {
    // Extract parameters
    const { 
      page = 1, 
      limit = 25, 
      event_type, 
      email, 
      ip_address, 
      start_date, 
      end_date,
      search
    } = params;
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    
    if (event_type) {
      queryParams.append('event_type', event_type);
    }
    
    if (email) {
      queryParams.append('email', email);
    }
    
    if (ip_address) {
      queryParams.append('ip_address', ip_address);
    }
    
    if (start_date) {
      queryParams.append('start_date', start_date);
    }
    
    if (end_date) {
      queryParams.append('end_date', end_date);
    }
    
    if (search) {
      queryParams.append('search', search);
    }
    
    // Generate the URL with query parameters
    const url = `/admin/security_events?${queryParams.toString()}`;
    
    // Make the API request
    const data = await api.get<SecurityEventsResponse>(url);
    
    return {
      events: data.events || [],
      total: data.total || 0,
      page: data.page || page,
      limit: data.limit || limit,
      total_pages: data.total_pages || 1
    };
  } catch (error) {
    console.error("Error fetching security events:", error);
    throw error;
  }
}

/**
 * Get a single security event by ID
 */
export async function getSecurityEventById(eventId: string): Promise<SecurityEvent> {
  try {
    // Make the API request
    const eventData = await api.get<SecurityEvent>(`/admin/security_events/${eventId}`);
    
    return eventData;
  } catch (error) {
    console.error(`Error fetching security event ${eventId}:`, error);
    throw error;
  }
}

/**
 * Get the list of unique event types for filtering
 */
export async function getEventTypes(): Promise<string[]> {
  try {
    // This is a simplified approach. In a real app, you might want a dedicated endpoint
    const { events } = await getSecurityEvents({ limit: 100 });
    const eventTypes = new Set<string>();
    
    events.forEach(event => {
      if (event.event_type) {
        eventTypes.add(event.event_type);
      }
    });
    
    return Array.from(eventTypes);
  } catch (error) {
    console.error("Error fetching event types:", error);
    return [];
  }
} 