import { getSession, signOut } from 'next-auth/react';

/**
 * Service to handle authentication for the admin dashboard
 * Uses session cookies from NextAuth instead of localStorage for better security
 */
export const AuthTokenService = {
  /**
   * Check if a session is valid - relies on NextAuth session management
   */
  hasValidToken: async () => {
    const session = await getSession();
    return !!session?.accessToken;
  },
  
  /**
   * Handle authentication errors
   */
  handleAuthError: async () => {
    // Sign out from NextAuth
    await signOut({ redirect: true, callbackUrl: '/auth/login' });
  }
};

/**
 * Get authorization headers for API requests
 */
export const getAuthHeaders = async () => {
  // Get session from NextAuth
  const session = await getSession();
  
  if (session?.accessToken) {
    return {
      'Authorization': `Bearer ${session.accessToken}`
    };
  }
  
  // No authentication available
  return {};
}; 