import { userSchema } from "@/components/dashboard/users/data/schema";
import { z } from "zod";
import { api } from "./api-client";

export type User = z.infer<typeof userSchema>;

// Interface for API User data
interface ApiUser {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  title?: string | null;
  phone_number?: string | null;
  date_of_birth?: string | null;
  created_at: string;
  updated_at: string;
  requires_psr_support?: boolean;
  [key: string]: any; // For any other fields
}

// Interface for getUsers parameters
export interface GetUsersParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filter?: Record<string, any>;
  search?: string;
  psr_support?: string;
}

// Detailed user type for the user details page
export type UserDetails = {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  fullName: string;
  phoneNumber: string | null;
  requiresPsrSupport: boolean;
  createdAt: string;
  updatedAt: string;
  addresses: {
    id: string;
    fullAddress: string;
    postcode: string;
    createdAt: string;
  }[];
  payment_methods: {
    id: string;
    accountHolder: string;
    accountNumber: string;
    sortCode: string;
    paymentType: string;
    createdAt: string;
  }[];
  energy_switches: {
    id: string;
    reference_number: string;
    status: string;
    displayStatus: 'pending' | 'completed' | 'cancelled' | 'failed';
    createdAt: string;
    completedAt: string | null;
    fromSupplier: string;
    toSupplier: string;
  }[];
};

// Function to get the base API URL from environment variables
export function getApiBaseUrl(): string {
  return process.env.NEXT_PUBLIC_API_BASE_URL;
}

// Test function to check API connectivity
export async function testApiConnection() {
  try {
    // Test the switch_users endpoint using our API client
    const data = await api.get('/switch_users');
    return { success: true, data };
  } catch (error) {
    return { success: false, error };
  }
}

// Get users with pagination
export async function getUsers(params: GetUsersParams = {}): Promise<{ users: User[], total: number }> {
  try {
    // Extract parameters
    const { page = 1, limit = 10, sortBy, sortOrder, search, psr_support } = params;
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    
    if (sortBy) {
      queryParams.append('sort_by', sortBy);
    }
    
    if (sortOrder) {
      queryParams.append('sort_order', sortOrder);
    }
    
    if (search) {
      queryParams.append('search', search);
    }
    
    if (psr_support) {
      queryParams.append('psr_support', psr_support);
    }
    
    // Add any filter parameters
    if (params.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    // Generate the URL with query parameters
    const url = `/admin/switch_users?${queryParams.toString()}`;
    
    // Make the API request
    const data = await api.get<{ 
      users: ApiUser[], 
      total: number, 
      page: number,
      limit: number,
      total_pages: number 
    }>(url);
    
    return {
      users: data.users.map((userData: ApiUser) => ({
        id: userData.id,
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        title: userData.title,
        phone_number: userData.phone_number,
        date_of_birth: userData.date_of_birth,
        created_at: userData.created_at,
        updated_at: userData.updated_at,
        requires_psr_support: userData.requires_psr_support || false
      })),
      total: data.total
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Process the API response and convert it to our User type
 */
function processUserResponse(responseData: any): { users: User[], total: number } {
  // Check if the response has the expected format
  if (!responseData || !responseData.users || !Array.isArray(responseData.users)) {
    return { users: [], total: 0 };
  }
  
  try {
    // Map the API data to our User type
    const users: User[] = responseData.users.map((userData: ApiUser) => ({
      id: userData.id,
      email: userData.email,
      first_name: userData.first_name,
      last_name: userData.last_name,
      title: userData.title,
      phone_number: userData.phone_number,
      date_of_birth: userData.date_of_birth,
      created_at: userData.created_at,
      updated_at: userData.updated_at,
      requires_psr_support: userData.requires_psr_support || false
    }));
    
    return {
      users,
      total: users.length, // This should ideally come from a total count in the API response
    };
  } catch (error) {
    return { users: [], total: 0 };
  }
}

/**
 * Get a single user by ID
 */
export async function getUserById(userId: string): Promise<User> {
  try {
    // Make the API request
    const userData = await api.get<ApiUser>(`/admin/switch_users/${userId}`);
    
    // Map to our User type
    return {
      id: userData.id,
      email: userData.email,
      first_name: userData.first_name,
      last_name: userData.last_name,
      title: userData.title,
      phone_number: userData.phone_number,
      date_of_birth: userData.date_of_birth,
      created_at: userData.created_at,
      updated_at: userData.updated_at,
      requires_psr_support: userData.requires_psr_support || false
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Get detailed user information for the user details page
 */
export async function getUserDetailsById(userId: string): Promise<UserDetails> {
  try {
    // Make the API request to get detailed user data
    const userData = await api.get<UserDetails>(`/admin/switch_users/${userId}`);
    
    // The API now returns data in the format we need
    return userData;
  } catch (error) {
    console.error("Error fetching user details:", error);
    throw error;
  }
}

/**
 * Generate mock user details for development and testing
 */
function getMockUserDetails(userId: string): UserDetails {
  return {
    id: userId,
    email: `user${Math.floor(Math.random() * 100)}@example.com`,
    firstName: "John",
    lastName: "Doe",
    fullName: "John Doe",
    phoneNumber: "+447700900123",
    requiresPsrSupport: Math.random() > 0.5,
    createdAt: new Date(Date.now() - Math.random() * ***********).toISOString(),
    updatedAt: new Date().toISOString(),
    addresses: [
      {
        id: `addr-${Math.floor(Math.random() * 1000)}`,
        fullAddress: "123 Example Street, London",
        postcode: "SW1A 1AA",
        createdAt: new Date(Date.now() - Math.random() * ***********).toISOString()
      }
    ],
    payment_methods: [
      {
        id: `payment-${Math.floor(Math.random() * 1000)}`,
        accountHolder: "John Doe",
        accountNumber: "1234",
        sortCode: "12",
        paymentType: "direct_debit",
        createdAt: new Date(Date.now() - Math.random() * ***********).toISOString()
      }
    ],
    energy_switches: Array.from({ length: 3 }, (_, i) => {
      const statuses = ['draft', 'confirmed', 'submitted_to_supplier', 'supplier_processing', 'switched', 'rejected_by_supplier'];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const displayStatus = 
        status === 'switched' ? 'completed' :
        status === 'rejected_by_supplier' ? 'failed' :
        'pending';
      
      const createdAt = new Date(Date.now() - Math.random() * ***********);
      let completedAt = null;
      if (status === 'switched' || status === 'rejected_by_supplier') {
        completedAt = new Date(createdAt.getTime() + Math.random() * **********).toISOString();
      }
      
      return {
        id: `switch-${i}-${Math.floor(Math.random() * 1000)}`,
        reference_number: `ES${new Date().getFullYear()}${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`,
        status,
        displayStatus,
        createdAt: createdAt.toISOString(),
        completedAt,
        fromSupplier: ['BG Energy', 'EDF', 'Octopus', 'E.ON', 'Scottish Power'][Math.floor(Math.random() * 5)],
        toSupplier: ['Bulb', 'Octopus', 'OVO', 'Shell Energy', 'Rebel Energy'][Math.floor(Math.random() * 5)]
      };
    })
  };
}
