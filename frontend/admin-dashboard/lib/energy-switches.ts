import { AuthTokenService, getAuthHeaders } from '@/lib/auth';
import { api } from '@/lib/api-client';

// Define the EnergySwitch type
export type EnergySwitch = {
  id: string
  userId: string
  userEmail?: string
  fromSupplier: string
  toSupplier: string
  status: 'pending' | 'completed' | 'cancelled' | 'failed' | 'confirmed' | 'submitted_to_supplier'
  createdAt: string
  completedAt?: string
  failureReason?: string
  reference_number?: string
}

// Define the detailed EnergySwitch type for individual switch details
export type EnergySwitchDetails = {
  id: string
  reference_number: string
  userId: string
  bill_url?: string
  bill_json?: string
  supplier_reference?: string
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    phoneNumber: string
    requiresPsrSupport: boolean
  }
  address: {
    id: string
    fullAddress: string
    postcode: string
  }
  fromSupplier: string
  toSupplier: string
  status: string
  displayStatus: 'pending' | 'completed' | 'cancelled' | 'failed'
  createdAt: string
  updatedAt: string
  completedAt?: string
  failureReason?: string
  paymentMethod?: {
    id: string
    accountHolder: string
    accountNumber: string
    sortCode: string
    paymentType: string
  }
  energyTariffs: {
    gas?: {
      id: string
      supplierName: string
      tariffName: string
      tariffType: string
      paymentMethod: string
      meterSerialNumber: string
      meterPointReferenceNumber: string
      standingCharge: number
      unitRate: number
      annualConsumption: number
      estimatedYearlyCost: number
      estimatedMonthlyCost: number
    }
    electricity?: {
      id: string
      supplierName: string
      tariffName: string
      tariffType: string
      paymentMethod: string
      meterSerialNumber: string
      meterPointAdministrationNumber: string
      standingCharge: number
      unitRate: number
      annualConsumption: number
      estimatedYearlyCost: number
      estimatedMonthlyCost: number
    }
  }
  switchingToTariff?: {
    id: string
    supplierName: string
    tariffName: string
    tariffType: string
    fuelType: string
    estimatedSavings: number
    paymentMethods: string[]
  }
  supplierSubmissions: {
    id: string
    supplierName: string
    submissionType: string
    status: string
    statusSummary: string
    submittedAt?: string
    processedAt?: string
    attemptNumber: number
    supplierReference?: string
    errorMessage?: string
    rejectionReason?: string
  }[]
}

// Define parameters for fetching energy switches
export interface GetEnergySwitchesParams {
  page?: number;
  limit?: number;
  status?: string;
  userId?: string;
  fromDate?: string;
  toDate?: string;
  search?: string;
}

// Define response type
export interface GetEnergySwitchesResponse {
  switches: EnergySwitch[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

/**
 * Fetches energy switches based on provided parameters
 */
export async function getEnergySwitches(params: GetEnergySwitchesParams = {}): Promise<GetEnergySwitchesResponse> {
  try {
    // Build query parameters
    const queryParams: Record<string, string> = {};
    if (params.page) queryParams.page = params.page.toString();
    if (params.limit) queryParams.limit = params.limit.toString();
    if (params.status) queryParams.status = params.status;
    if (params.userId) queryParams.user_id = params.userId;
    if (params.fromDate) queryParams.from_date = params.fromDate;
    if (params.toDate) queryParams.to_date = params.toDate;
    if (params.search) queryParams.search = params.search;
    
    // Use the api client which handles auth and 401 errors
    const data = await api.get<any>('/admin/energy_switches', { params: queryParams });

    return {
      switches: data.switches || [],
      total: data.total || 0,
      page: data.page || params.page || 1,
      limit: data.limit || params.limit || 10,
      total_pages: data.total_pages || Math.ceil((data.total || 0) / (data.limit || params.limit || 10))
    };
  } catch (error) {
    console.error('Error fetching energy switches:', error);
    
    // Return mock data in development if requested
    if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
      console.log('Falling back to mock data due to error');
      return getMockEnergySwitches(params);
    }
    
    throw error;
  }
}

/**
 * Generates mock energy switch data for development and testing
 */
function getMockEnergySwitches(params: GetEnergySwitchesParams = {}): GetEnergySwitchesResponse {
  // Create mock energy switches
  const mockSwitches: EnergySwitch[] = Array.from({ length: 25 }, (_, i) => {
    const id = `switch-${Math.random().toString(36).substring(2, 10)}`;
    const statuses = ['pending', 'completed', 'cancelled', 'failed'] as const;
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    const createdAt = new Date();
    createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 30));
    
    let completedAt = undefined;
    if (status === 'completed' || status === 'failed') {
      completedAt = new Date(createdAt);
      completedAt.setDate(completedAt.getDate() + Math.floor(Math.random() * 7) + 1);
    }
    
    return {
      id,
      userId: `user-${Math.floor(Math.random() * 1000)}`,
      userEmail: `user${i + 1}@example.com`,
      fromSupplier: ['BG Energy', 'EDF', 'Octopus', 'E.ON', 'Scottish Power'][Math.floor(Math.random() * 5)],
      toSupplier: ['Bulb', 'Octopus', 'OVO', 'Shell Energy', 'Rebel Energy'][Math.floor(Math.random() * 5)],
      status,
      createdAt: createdAt.toISOString(),
      completedAt: completedAt?.toISOString(),
      failureReason: status === 'failed' ? 'Supplier rejected the switch request' : undefined,
    };
  });
  
  // Get page and limit from params or use defaults
  const page = params.page || 1;
  const limit = params.limit || 10;
  
  // Apply pagination
  const start = (page - 1) * limit;
  const paginatedSwitches = mockSwitches.slice(start, start + limit);
  
  return {
    switches: paginatedSwitches,
    total: mockSwitches.length,
    page,
    limit,
    total_pages: Math.ceil(mockSwitches.length / limit)
  };
}

/**
 * Fetches a single energy switch by ID
 */
export async function getEnergySwitchById(id: string): Promise<EnergySwitchDetails> {
  try {
    // Use the api client which handles auth and 401 errors
    const data = await api.get<EnergySwitchDetails>(`/admin/energy_switches/${id}`);
    
    return data;
  } catch (error) {
    console.error(`Error fetching energy switch ${id}:`, error);
    
    throw error;
  }
}
