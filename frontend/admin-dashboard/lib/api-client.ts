import axios from 'axios';
import { getSession } from 'next-auth/react';
import { AuthTokenService } from './auth';

// Create an axios instance
const apiClient = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL || ''}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      const session = await getSession();
      if (session?.accessToken) {
        // Set the Authorization header for every request
        config.headers.Authorization = `Bearer ${session.accessToken}`;
      }
    } catch (error) {
      // Silently fail and let the request proceed without auth
      console.error("Failed to get session token", error);
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      // Handle the authentication error
      await AuthTokenService.handleAuthError();
    }
    
    return Promise.reject(error);
  }
);

// Export the client instance
export default apiClient;

// Helper functions for API calls
export const api = {
  /**
   * Make a GET request
   */
  get: async <T>(url: string, config = {}) => {
    try {
      const response = await apiClient.get<T>(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  
  /**
   * Make a POST request
   */
  post: async <T>(url: string, data = {}, config = {}) => {
    try {
      const response = await apiClient.post<T>(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  
  /**
   * Make a PUT request
   */
  put: async <T>(url: string, data = {}, config = {}) => {
    try {
      const response = await apiClient.put<T>(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
  
  /**
   * Make a DELETE request
   */
  delete: async <T>(url: string, config = {}) => {
    try {
      const response = await apiClient.delete<T>(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}; 