import { z } from "zod";
import { api } from "./api-client";

// Schema for energy tariff rates
export const energyTariffRateSchema = z.object({
  id: z.string(),
  fuelType: z.string(),
  gspCode: z.string().nullable(),
  profileClass: z.string().nullable(),
  standingChargeIncVat: z.number(),
  unitRateIncVat: z.number(),
  dayUnitRateIncVat: z.number().nullable(),
  nightUnitRateIncVat: z.number().nullable(),
  createdAt: z.string(),
  updatedAt: z.string()
});

// Schema for energy tariffs
export const energyTariffSchema = z.object({
  id: z.string(),
  tariffName: z.string(),
  energyType: z.string(),
  tariffType: z.string(),
  readableTariffType: z.string(),
  productCode: z.string().nullable(),
  availableFrom: z.string().nullable(),
  availableTo: z.string().nullable(),
  exitFees: z.number().nullable(),
  paymentMethods: z.array(z.string()),
  createdAt: z.string(),
  updatedAt: z.string(),
  tariffRates: z.array(energyTariffRateSchema)
});

// Schema for supplier details
export const supplierSchema = z.object({
  id: z.string(),
  name: z.string(),
  logoUrl: z.string().nullable(),
  description: z.string().nullable(),
  trustpilotRating: z.number().nullable(),
  websiteUrl: z.string().nullable(),
  tariffsCount: z.number().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  energyTariffs: z.array(energyTariffSchema).optional()
});

// Interface for getSuppliers parameters
export interface GetSuppliersParams {
  page?: number;
  limit?: number;
  name?: string;
  search?: string;
}

// Types based on the schemas
export type Supplier = z.infer<typeof supplierSchema>;
export type EnergyTariff = z.infer<typeof energyTariffSchema>;
export type EnergyTariffRate = z.infer<typeof energyTariffRateSchema>;

/**
 * Get a list of suppliers with pagination
 */
export async function getSuppliers(params: GetSuppliersParams = {}): Promise<{ suppliers: Supplier[], total: number, page: number, limit: number, totalPages: number }> {
  try {
    // Extract parameters
    const { page = 1, limit = 10, name, search } = params;
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    
    if (name) {
      queryParams.append('name', name);
    }
    
    if (search) {
      queryParams.append('search', search);
    }
    
    // Make the API request
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    const response = await api.get<any>(`/admin/suppliers${queryString}`);
    
    // Process and return the data
    return {
      suppliers: response.suppliers.map((supplier: any) => ({
        id: supplier.id,
        name: supplier.name,
        logoUrl: supplier.logo,
        description: supplier.description,
        trustpilotRating: supplier.trustpilot_rating,
        websiteUrl: supplier.website_url,
        tariffsCount: supplier.tariffsCount,
        createdAt: supplier.createdAt,
        updatedAt: supplier.updatedAt
      })),
      total: response.total,
      page: response.page,
      limit: response.limit,
      totalPages: response.total_pages
    };
  } catch (error) {
    console.error("Error fetching suppliers:", error);
    throw error;
  }
}

/**
 * Get detailed information about a specific supplier including energy tariffs
 */
export async function getSupplierById(supplierId: string): Promise<Supplier> {
  try {
    // Make the API request
    const response = await api.get<any>(`/admin/suppliers/${supplierId}`);
    
    // Process and return the data
    return {
      id: response.id,
      name: response.name,
      logoUrl: response.logo,
      description: response.description,
      trustpilotRating: response.trustpilot_rating,
      websiteUrl: response.website_url,
      createdAt: response.createdAt,
      updatedAt: response.updatedAt,
      energyTariffs: (response.energyTariffs || []).map((tariff: any) => ({
        id: tariff.id,
        tariffName: tariff.tariffName,
        energyType: tariff.energyType,
        tariffType: tariff.tariffType,
        readableTariffType: tariff.readableTariffType,
        productCode: tariff.productCode,
        availableFrom: tariff.availableFrom,
        availableTo: tariff.availableTo,
        exitFees: tariff.exitFees,
        paymentMethods: tariff.paymentMethods,
        createdAt: tariff.createdAt,
        updatedAt: tariff.updatedAt,
        tariffRates: (tariff.tariffRates || []).map((rate: any) => ({
          id: rate.id,
          fuelType: rate.fuelType,
          gspCode: rate.gspCode,
          profileClass: rate.profileClass,
          standingChargeIncVat: rate.standingChargeIncVat,
          unitRateIncVat: rate.unitRateIncVat,
          dayUnitRateIncVat: rate.dayUnitRateIncVat,
          nightUnitRateIncVat: rate.nightUnitRateIncVat,
          createdAt: rate.createdAt,
          updatedAt: rate.updatedAt
        }))
      }))
    };
  } catch (error) {
    console.error("Error fetching supplier details:", error);
    throw error;
  }
} 