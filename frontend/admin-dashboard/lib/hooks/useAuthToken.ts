import { useSession } from 'next-auth/react';

/**
 * Hook to check authentication status
 * Uses NextAuth session for secure cookie-based authentication
 */
export function useAuthToken() {
  const { data: session, status } = useSession();

  return {
    isAuthenticated: status === 'authenticated',
    hasToken: status === 'authenticated' && !!session?.accessToken,
    isLoading: status === 'loading',
  };
} 