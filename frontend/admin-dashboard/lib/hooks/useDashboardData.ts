import useSWR from 'swr';
import { api } from '../api-client';

export type DashboardData = {
  metrics: {
    total_users: number;
    active_users: number;
    total_suppliers: number;
    total_switches: number;
  };
  recent_users: any[];
  recent_switches: any[];
  security_events: Record<string, number>;
  stats: {
    switches_by_supplier: Record<string, number>;
    switches_by_status: Record<string, number>;
    security_events_trend: Record<string, number>;
    user_growth: number;
    daily_switches: Record<string, number>;
  };
};

export function useDashboardData() {
  const { data, error, isLoading, mutate } = useSWR<DashboardData>(
    '/admin/dashboard',
    async () => {
      try {
        return await api.get('/admin/dashboard');
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        throw error;
      }
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 seconds
    }
  );

  return {
    dashboardData: data,
    isLoading,
    isError: error,
    mutate,
  };
}