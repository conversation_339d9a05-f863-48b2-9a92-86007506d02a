// Auth Imports
import { <PERSON><PERSON>out<PERSON> } from '@/types/types';
import {
  HiOutlineHome,
  HiOutlineUsers,
  HiOutlineCog8Tooth,
  HiOutlineShieldExclamation,
  HiOutlineBolt,
  HiOutlineBuildingStorefront,
} from 'react-icons/hi2';

import { FaVoicemail } from 'react-icons/fa';

export const routes: IRoute[] = [
  {
    name: 'Dashboard',
    path: '/dashboard/main',
    icon: <HiOutlineHome className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />,
    collapse: false
  },
  {
    name: 'Energy Switches',
    path: '/dashboard/energy-switches',
    icon: (
      <HiOutlineBolt className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
    ),
    collapse: false,
    disabled: false
  },
  {
    name: 'Switch Users',
    path: '/dashboard/users',
    icon: (
      <HiOutlineUsers className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
    ),
    collapse: false,
    disabled: false
  },
  {
    name: 'Suppliers',
    path: '/dashboard/suppliers',
    icon: (
      <HiOutlineBuildingStorefront className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
    ),
    collapse: false,
    disabled: false
  },
  {
    name: 'Security Events',
    path: '/dashboard/security-events',
    icon: (
      <HiOutlineShieldExclamation className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
    ),
    collapse: false,
    disabled: false
  },
  {
    name: 'Profile Settings',
    path: '/dashboard/settings',
    icon: (
      <HiOutlineCog8Tooth className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
    ),
    collapse: false,
    disabled: false
  }
];
