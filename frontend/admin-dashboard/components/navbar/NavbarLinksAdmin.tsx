'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useRouter } from 'next/navigation';
import { signOut } from 'next-auth/react';
import { toast } from 'sonner';
import React from 'react';
import { useState, useEffect } from 'react';
import { FiAlignJustify } from 'react-icons/fi';
import {
  HiOutlineInformationCircle,
  HiOutlineArrowRightOnRectangle,
} from 'react-icons/hi2';

export default function HeaderLinks(props: {
  [x: string]: any;
}) {
  const { onOpen } = props;
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [signOutDialogOpen, setSignOutDialogOpen] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSignOutClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setSignOutDialogOpen(true);
  };

  const handleConfirmedSignOut = async () => {
    toast.info('Signing out...');
    await signOut({ callbackUrl: '/auth/login' });
  };

  if (!mounted) {
    return null;
  }

  return (
    <>
      <Dialog open={signOutDialogOpen} onOpenChange={setSignOutDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Sign out confirmation</DialogTitle>
            <DialogDescription>
              Are you sure you want to sign out of your account?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-row justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setSignOutDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmedSignOut}>
              Sign Out
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div 
        className="nav-links-container" 
        style={{ 
          position: 'absolute',
          right: '16px',
          top: '50%',
          transform: 'translateY(-50%)',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}
      >
        <Button
          variant="outline"
          className="flex h-9 min-w-9 cursor-pointer rounded-full border-zinc-200 p-0 text-xl text-zinc-950 md:min-h-10 md:min-w-10 xl:hidden"
          onClick={onOpen}
          style={{ position: 'relative' }}
        >
          <FiAlignJustify className="h-4 w-4" />
        </Button>

        {/* Dropdown Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="flex h-9 min-w-9 cursor-pointer rounded-full border-zinc-200 p-0 text-xl text-zinc-950 md:min-h-10 md:min-w-10"
              style={{ position: 'relative' }}
            >
              <HiOutlineInformationCircle className="h-[20px] w-[20px] text-zinc-950" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 p-2">
            <a target="blank" href="mailto:#">
              <Button variant="outline" className="mb-2 w-full">
                Help & Support
              </Button>
            </a>
            <a target="blank" href="/#faqs">
              <Button variant="outline" className="w-full">
                FAQs & More
              </Button>
            </a>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="outline"
          className="flex h-9 min-w-9 cursor-pointer rounded-full border-zinc-200 p-0 text-xl text-zinc-950 md:min-h-10 md:min-w-10"
          style={{ position: 'relative' }}
          onClick={handleSignOutClick}
          title="Sign out"
        >
          <HiOutlineArrowRightOnRectangle className="h-4 w-4 stroke-2 text-zinc-950" />
        </Button>
        
        <a href="/dashboard/settings" style={{ position: 'relative' }}>
          <Avatar className="h-9 min-w-9 md:min-h-10 md:min-w-10">
            <AvatarImage src={props.userDetails?.avatar_url ?? ''} />
            <AvatarFallback className="font-bold">SA</AvatarFallback>
          </Avatar>
        </a>
      </div>
    </>
  );
}
