'use client';

/* eslint-disable */
import AdminNavbarLinks from './NavbarLinksAdmin';
import NavLink from '@/components/link/NavLink';
import { isWindowAvailable } from '@/utils/navigation';
import { useState, useEffect } from 'react';

export default function AdminNavbar(props: {
  brandText: string;
  onOpen: (...args: any[]) => any;
  [x: string]: any;
}) {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    isWindowAvailable() && window.addEventListener('scroll', changeNavbar);

    return () => {
      isWindowAvailable() && window.removeEventListener('scroll', changeNavbar);
    };
  });

  const { brandText, onOpen } = props;
  const changeNavbar = () => {
    if (isWindowAvailable() && window.scrollY > 1) {
      setScrolled(true);
    } else {
      setScrolled(false);
    }
  };

  return (
    <nav
      className="fixed-nav"
      style={{ 
        position: 'fixed',
        top: '12px',
        left: '12px',
        right: '12px',
        zIndex: 50,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        padding: '8px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
        backdropFilter: 'blur(12px)',
        marginTop: '16px',
        overflow: 'visible'
      }}
    >
      <div style={{ position: 'relative', marginLeft: '6px' }}>
        <p className="text-md shrink capitalize text-zinc-950 md:text-3xl">
          <NavLink
            href="#"
            className="font-bold capitalize hover:text-zinc-950"
          >
            {brandText}
          </NavLink>
        </p>
      </div>
      {/* Links container */}
      <AdminNavbarLinks
        onOpen={onOpen}
        products={props.products}
      />
    </nav>
  );
}