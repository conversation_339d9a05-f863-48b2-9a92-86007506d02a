import { Card } from './ui/card';
import dynamic from 'next/dynamic';

// Use dynamic import for react-markdown
const ReactMarkdown = dynamic(() => import('react-markdown'), {
  ssr: true, // Set to false if you encounter hydration issues
});

export default function MessageBox(props: { output: string }) {
  const { output } = props;
  return (
    <Card className="mb-7 flex min-h-[564px] w-full min-w-fit rounded-lg border px-5 py-4 font-medium dark:border-zinc-800">
      <ReactMarkdown className="font-medium dark:text-white">
        {output ? output : 'Your generated response will appear here...'}
      </ReactMarkdown>
    </Card>
  );
}
