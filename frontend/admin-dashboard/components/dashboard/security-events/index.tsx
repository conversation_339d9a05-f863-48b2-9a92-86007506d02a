'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import useSWR from 'swr';
import { columns } from "../components/security-events/columns"
import { DataTable } from "../components/security-events/data-table"
import DashboardLayout from "@/components/layout";
import { 
  SecurityEvent, 
  getSecurityEvents, 
  GetSecurityEventsParams 
} from '@/lib/security-events';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2, RefreshCw } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";

export default function SecurityEventsList() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isClient, setIsClient] = useState(false);
  
  // Get URL params or defaults
  const currentPageParam = searchParams.get('page');
  const pageSizeParam = searchParams.get('limit');
  const searchParam = searchParams.get('search');
  const eventTypeParam = searchParams.get('event_type');
  
  // Set state from URL params
  const [currentPage, setCurrentPage] = useState(currentPageParam ? parseInt(currentPageParam) : 1);
  const [pageSize, setPageSize] = useState(pageSizeParam ? parseInt(pageSizeParam) : 10);
  const [searchValue, setSearchValue] = useState(searchParam || '');
  const [eventType, setEventType] = useState(eventTypeParam || '');
  
  // Set isClient to true on component mount to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Update URL when filters change
  const updateUrl = useCallback(() => {
    const params = new URLSearchParams();
    
    if (currentPage > 1) params.set('page', currentPage.toString());
    if (pageSize !== 10) params.set('limit', pageSize.toString());
    if (searchValue) params.set('search', searchValue);
    if (eventType) params.set('event_type', eventType);
    
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.push(newUrl, { scroll: false });
  }, [currentPage, pageSize, searchValue, eventType, router]);
  
  // Update URL when filters change
  useEffect(() => {
    if (isClient) {
      updateUrl();
    }
  }, [currentPage, pageSize, searchValue, eventType, isClient, updateUrl]);
  
  // Fetch events with pagination and filters
  const { data, error, isLoading, mutate } = useSWR(
    isClient ? [`/admin/security_events`, currentPage, pageSize, searchValue, eventType] : null, 
    async () => {
      console.log(`Fetching events - page: ${currentPage}, limit: ${pageSize}, search: ${searchValue}, event_type: ${eventType}`);
      const params: GetSecurityEventsParams = { 
        page: currentPage, 
        limit: pageSize
      };
      
      // Add filters if provided
      if (searchValue) params.search = searchValue;
      if (eventType) params.event_type = eventType;
      
      return await getSecurityEvents(params);
    },
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000, // 10 seconds
      onError: (err) => {
        console.error('Error fetching security events:', err);
      }
    }
  );

  // Handle manual refresh
  const handleRefresh = async () => {
    console.log('Manual refresh triggered');
    await mutate(undefined, { revalidate: true });
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && (!data?.total_pages || newPage <= data.total_pages)) {
      setCurrentPage(newPage);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  // Handle filter changes
  const handleFilterChange = (filters: { eventType?: string, search?: string }) => {
    // Reset to first page when changing filters
    setCurrentPage(1);
    
    // Update filter state
    if (filters.search !== undefined) {
      setSearchValue(filters.search);
    }
    
    if (filters.eventType !== undefined) {
      setEventType(filters.eventType);
    }
  };

  // Events to display
  const events = data?.events || [];
  
  // Calculate pagination range
  const totalPages = data?.total_pages || 0;
  const startItem = events.length > 0 ? (currentPage - 1) * pageSize + 1 : 0;
  const endItem = events.length > 0 ? startItem + events.length - 1 : 0;
  const totalItems = data?.total || 0;

  return (
    <DashboardLayout
      title="Security Events"
      description="Monitor security-related events in the system"
    >
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="flex justify-end mb-6">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            className="gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            {isLoading ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
        
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load security events: {error.message}
            </AlertDescription>
          </Alert>
        )}
        
        <div className="w-full rounded-lg">
          <div>
            {isLoading && events.length === 0 && (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading security events...</span>
              </div>
            )}
            
            {(!isLoading || events.length > 0) && (
              <>
                <DataTable 
                  columns={columns} 
                  data={events}
                  isLoading={isLoading}
                  pageSize={pageSize}
                  onPageSizeChange={handlePageSizeChange}
                  onPageChange={handlePageChange}
                  currentPage={currentPage}
                  totalItems={data?.total || 0}
                  onFilterChange={handleFilterChange}
                  initialSearchValue={searchValue}
                  initialEventType={eventType}
                />
                
                {events.length > 0 && totalPages > 1 && (
                  <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
                    <div className="text-sm text-muted-foreground">
                      Showing <span className="font-medium">{startItem}</span> to{" "}
                      <span className="font-medium">{endItem}</span> of{" "}
                      <span className="font-medium">{totalItems}</span> events
                    </div>
                    
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious 
                            onClick={() => handlePageChange(currentPage - 1)}
                            className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                        
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          // Show 2 pages before and after current page, or first 5 if near start
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }
                          
                          return (
                            <PaginationItem key={pageNum}>
                              <PaginationLink
                                isActive={pageNum === currentPage}
                                onClick={() => handlePageChange(pageNum)}
                                className="cursor-pointer"
                              >
                                {pageNum}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        })}
                        
                        <PaginationItem>
                          <PaginationNext 
                            onClick={() => handlePageChange(currentPage + 1)}
                            className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 