'use client';

import { useState, useEffect } from 'react';
import useS<PERSON> from 'swr';
import { columns, mapEnergySwitch } from "@/components/dashboard/components/energy-switches/columns"
import { DataTable } from "@/components/dashboard/components/energy-switches/data-table"
import DashboardLayout from "@/components/layout";
import { 
  EnergySwitch, 
  getEnergySwitches, 
  GetEnergySwitchesParams 
} from '@/lib/energy-switches';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2, RefreshCw, List } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";

// Constants for pagination
const DEFAULT_PAGE_SIZE = 10;

export default function EnergySwitchesList() {
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isClient, setIsClient] = useState(false);
  const [activeFilters, setActiveFilters] = useState<{
    status?: string;
    fromSupplier?: string;
    toSupplier?: string;
    search?: string;
  }>({});
  
  // Set isClient to true on component mount to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Create fetcher function with the pagination parameters
  const fetcher = async () => {
    try {    
      const params: GetEnergySwitchesParams = {
        page: currentPage,
        limit: pageSize,
        ...activeFilters
      };
      
      const result = await getEnergySwitches(params);
      return result;
    } catch (error) {
      console.error('Error in fetcher:', error);
      throw error;
    }
  };
  
  // Fetch data using SWR with the current parameters
  const { data, error, isLoading, mutate } = useSWR(
    isClient ? [`/api/energy_switches`, currentPage, pageSize, activeFilters] : null, 
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000, // 10 seconds
      onError: (err) => {
        console.error('SWR error:', err);
      }
    }
  );
  
  // Handle applying filters
  const handleFilterChange = (filters: typeof activeFilters) => {
    setActiveFilters(filters);
    setCurrentPage(1); // Reset to first page when filters change
  };
  
  // Handle search
  const handleSearch = (searchValue: string) => {
    const trimmedValue = searchValue.trim();
    setActiveFilters(prev => ({ ...prev, search: trimmedValue || undefined }));
    setCurrentPage(1); // Reset to first page when search changes
  };
  
  // Handle status change
  const handleStatusChange = (status: string | null) => {
    setActiveFilters(prev => ({ ...prev, status: status || undefined }));
    setCurrentPage(1); // Reset to first page when status changes
  };
  
  // Handle manual refresh
  const handleRefresh = async () => {
    console.log('Manual refresh triggered');
    // Use the revalidate API from SWR, with true to force revalidation
    await mutate(undefined, { revalidate: true });
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && (!data?.total_pages || newPage <= data.total_pages)) {
      setCurrentPage(newPage);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  // Calculate pagination range
  const totalPages = data?.total_pages || 0;
  const startItem = data?.switches?.length > 0 ? (currentPage - 1) * pageSize + 1 : 0;
  const endItem = data?.switches?.length > 0 ? startItem + data.switches.length - 1 : 0;
  const totalItems = data?.total || 0;

  return (
    <DashboardLayout
      title="Energy Switches"
      description="Manage and view all energy switch transactions"
    >
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="flex justify-end mb-6">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            className="gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            {isLoading ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
        
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load energy switches: {error.message}
            </AlertDescription>
          </Alert>
        )}
        
        <div className="w-full rounded-lg">
          <div>
            {isLoading && !data?.switches && (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading energy switches...</span>
              </div>
            )}
            
            {data?.switches && (
              <>
                <DataTable 
                  data={data.switches.map(mapEnergySwitch)} 
                  // @ts-ignore
                  columns={columns}
                  isLoading={isLoading}
                  onPageSizeChange={handlePageSizeChange}
                  pageSize={pageSize}
                  onPageChange={handlePageChange}
                  currentPage={currentPage}
                  totalItems={totalItems}
                  onSearch={handleSearch}
                  initialSearchValue={activeFilters.search}
                  onStatusChange={handleStatusChange}
                  initialStatus={activeFilters.status || null}
                />
                
                {/* Pagination information and controls */}
                <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div className="text-sm text-muted-foreground">
                    Showing <span className="font-medium">{startItem}</span> to{" "}
                    <span className="font-medium">{endItem}</span> of{" "}
                    <span className="font-medium">{totalItems}</span> switches
                  </div>
                  
                  {totalPages > 1 && (
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious 
                            onClick={() => handlePageChange(currentPage - 1)}
                            className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                        
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          // Show 2 pages before and after current page, or first 5 if near start
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }
                          
                          return (
                            <PaginationItem key={pageNum}>
                              <PaginationLink
                                isActive={pageNum === currentPage}
                                onClick={() => handlePageChange(pageNum)}
                                className="cursor-pointer"
                              >
                                {pageNum}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        })}
                        
                        <PaginationItem>
                          <PaginationNext 
                            onClick={() => handlePageChange(currentPage + 1)}
                            className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 