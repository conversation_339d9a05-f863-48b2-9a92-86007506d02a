"use client"

import { useState, use<PERSON>em<PERSON>, lazy, Suspense } from "react"
import { Metadata } from "next"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from "@/components/ui/tabs"
import { motion } from "framer-motion"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { Overview } from "@/components/dashboard/components/overview"
import { RecentSales } from "@/components/dashboard/components/recent-sales"
import DashboardLayout from "@/components/layout"
import { Bar<PERSON>hart, Line<PERSON>hart, Pie<PERSON>hart } from "@/components/ui/charts"
import { 
  Activity, 
  Users, 
  Bolt, 
  ShieldAlert, 
  Building, 
  TrendingUp, 
  ArrowUpRight, 
  <PERSON>DownRight,
  Info,
  User
} from "lucide-react"
import Link from "next/link"
import { useDashboardData } from "@/lib/hooks/useDashboardData"

// Utility to sanitize data from API
const sanitizeString = (str: string | undefined | null): string => {
  if (!str) return '';
  return String(str)
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

// Utility to obfuscate sensitive info like emails
const obfuscateEmail = (email: string | undefined | null): string => {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (!username || !domain) return email;
  return `${username.slice(0, 2)}${"*".repeat(username.length - 2)}@${domain}`;
};

// Utility to create secure user links
const createSecureUserLink = (userId: string | number | undefined | null): string => {
  if (!userId) return '#';
  // In a real app, you might want to use a more secure method
  // This is just a simple example - consider using a proper encryption method
  const encodedId = btoa(`user_${userId}`);
  return `/dashboard/users/${encodedId}`;
};

// Format date with proper localization
const formatDate = (dateString: string | undefined | null): string => {
  if (!dateString) return '';
  try {
    // Use Intl.DateTimeFormat for consistent date formatting across timezones
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(new Date(dateString));
  } catch (e) {
    console.error('Error formatting date:', e);
    return dateString;
  }
};

// Create reusable component for supplier data chart
const SupplierSwitchesChart = ({ supplierData = {}, height = 350 }: { supplierData: Record<string, any>, height?: number }) => {
  const hasData = Object.keys(supplierData).length > 0;
  
  if (!hasData) {
    return (
      <PieChart
        data={[]}
        height={height}
        description="No energy supplier switches recorded yet"
      />
    );
  }
  
  return (
    <PieChart
      data={Object.entries(supplierData).map(([name, value]) => ({
        name,
        value: Number(value)
      }))}
      height={height}
      valueFormatter={(value) => String(value)}
    />
  );
};

export const metadata: Metadata = {
  title: "Energy Admin Dashboard",
  description: "Energy Management Dashboard",
}

export default function DashboardPage() {
  const { dashboardData, isLoading, isError, mutate } = useDashboardData();
  const [activeTab, setActiveTab] = useState("overview");

  // Memoize calculated values to prevent unnecessary recalculations
  const activeUserPercentage = useMemo(() => {
    if (!dashboardData?.metrics?.total_users || dashboardData.metrics.total_users === 0) {
      return 0;
    }
    return Math.round((dashboardData.metrics.active_users / dashboardData.metrics.total_users) * 100);
  }, [dashboardData?.metrics?.active_users, dashboardData?.metrics?.total_users]);

  // Memoize user growth data for LineChart to prevent recalculation on re-renders
  const userGrowthData = useMemo(() => {
    if (!dashboardData?.stats) {
      return [];
    }
    
    // For demonstration, create mock data based on available stats
    const mockMonths = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    const currentMonth = new Date().getMonth();
    const recentMonths = [...mockMonths.slice(currentMonth - 5), ...mockMonths.slice(0, currentMonth + 1)];
    
    // Base growth on total_users and user_growth percentage
    const baseUsers = dashboardData.metrics.total_users / (1 + (dashboardData.stats.user_growth / 100));
    
    return recentMonths.map((month, index) => {
      // Create a growth curve using the user_growth percentage
      const growthFactor = 1 + ((index / 5) * (dashboardData.stats.user_growth / 100));
      const userCount = Math.round(baseUsers * growthFactor);
      
      return {
        month,
        users: userCount
      };
    });
  }, [dashboardData?.stats, dashboardData?.metrics?.total_users]);

  // Dashboard data state management

  // Animation variants for Framer Motion
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.05
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  }

  const chartVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5 }
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout title="Dashboard" description="Loading...">
        <div className="h-full w-full p-4 md:p-8 pt-6">
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader className="pb-2">
                  <Skeleton className="h-4 w-24" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-20 mb-2" />
                  <Skeleton className="h-4 w-40" />
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-7 mt-4">
            <Card className="lg:col-span-4">
              <CardHeader>
                <Skeleton className="h-5 w-40" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[350px] w-full rounded-md" />
              </CardContent>
            </Card>
            <Card className="lg:col-span-3">
              <CardHeader>
                <Skeleton className="h-5 w-40 mb-2" />
                <Skeleton className="h-4 w-60" />
              </CardHeader>
              <CardContent>
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-4 mb-4">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[150px]" />
                      <Skeleton className="h-4 w-[100px]" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (isError) {
    return (
      <DashboardLayout
        title="Dashboard Error"
        description="There was an error loading the dashboard data"
      >
        <div className="h-full w-full flex flex-col items-center justify-center p-4 md:p-8 pt-6">
          <ShieldAlert className="h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Dashboard</h2>
          <p className="text-muted-foreground mb-4 text-center max-w-md">
            There was an error while fetching the dashboard data. Please try refreshing the page or contact support if the issue persists.
          </p>
          <button 
            onClick={() => mutate()} 
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout
      title="Energy Management Dashboard"
      description="Monitor energy suppliers, users, and energy switches"
    >
      <div className="h-full w-full">
        <div className="flex flex-col">
          <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
            <Tabs 
              defaultValue="overview" 
              className="space-y-4"
              onValueChange={setActiveTab}
            >
              <TabsList className="inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="users">Users</TabsTrigger>
                <TabsTrigger value="suppliers">Suppliers</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <motion.div 
                  className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <motion.div variants={itemVariants}>
                    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Total Users
                        </CardTitle>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="rounded-full p-1 hover:bg-muted">
                                <Users className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total registered users on the platform</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{dashboardData?.metrics?.total_users || 0}</div>
                        <p className="text-xs text-muted-foreground">
                          {dashboardData?.stats?.user_growth > 0 ? (
                            <motion.span 
                              className="text-green-500 flex items-center"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.2 }}
                            >
                              <ArrowUpRight className="h-3 w-3 mr-1" />
                              +{dashboardData?.stats?.user_growth}% from last month
                            </motion.span>
                          ) : (
                            <motion.span 
                              className="text-red-500 flex items-center"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.2 }}
                            >
                              <ArrowDownRight className="h-3 w-3 mr-1" />
                              {dashboardData?.stats?.user_growth}% from last month
                            </motion.span>
                          )}
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Active Users
                        </CardTitle>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="rounded-full p-1 hover:bg-muted">
                                <Activity className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Users who have been active in the last 30 days</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{dashboardData?.metrics?.active_users || 0}</div>
                        <p className="text-xs text-muted-foreground">
                          {activeUserPercentage || 0}% of total users
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="rounded-full p-1 hover:bg-muted">
                                <Building className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Energy suppliers on the platform</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{dashboardData?.metrics?.total_suppliers || 0}</div>
                        <p className="text-xs text-muted-foreground">
                          Supporting energy switches
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div variants={itemVariants}>
                    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Energy Switches
                        </CardTitle>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="rounded-full p-1 hover:bg-muted">
                                <Bolt className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total energy switches processed through the platform</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{dashboardData?.metrics?.total_switches || 0}</div>
                        <p className="text-xs text-muted-foreground">
                          Total switches processed
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>
                </motion.div>

                {/* Energy Switches Trend Chart */}
                <motion.div 
                  className="mt-4"
                  variants={chartVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <div>
                        <CardTitle>Energy Switches Trend</CardTitle>
                        <CardDescription>Daily energy switches over the last 30 days</CardDescription>
                      </div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="rounded-full p-1 hover:bg-muted">
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Number of energy switches processed each day</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </CardHeader>
                    <CardContent>
                      {(() => {
                        // Use daily_switches data from the API
                        const switchesData = dashboardData?.stats?.daily_switches || {};
                        
                        const hasData = Object.keys(switchesData).length > 0;
                        
                        if (!hasData) {
                          return (
                            <BarChart
                              data={[]}
                              x="date"
                              y="switches"
                              height={300}
                              description="No energy switches recorded in the past 30 days"
                            />
                          );
                        }
                        
                        return (
                          <BarChart
                            data={Object.entries(switchesData).map(([date, count]) => ({
                              date,
                              switches: count
                            }))}
                            x="date"
                            y="switches"
                            height={300}
                            colors={["hsl(var(--primary))"]}
                            valueFormatter={(value) => String(value)}
                          />
                        );
                      })()}
                    </CardContent>
                  </Card>
                </motion.div>

                <div className="grid gap-4 grid-cols-1 lg:grid-cols-7">
                  <motion.div 
                    className="lg:col-span-4"
                    variants={chartVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    <Card className="h-full">
                      <CardHeader>
                        <CardTitle>Energy Switches by Supplier</CardTitle>
                      </CardHeader>
                      <CardContent className="pl-2">
                        <SupplierSwitchesChart supplierData={dashboardData?.stats?.switches_by_supplier} />
                      </CardContent>
                    </Card>
                  </motion.div>

                  <Card className="lg:col-span-3">
                    <CardHeader>
                      <CardTitle>Recent Energy Switches</CardTitle>
                      <CardDescription>
                        Latest energy supplier switches
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <motion.div 
                        className="space-y-4"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                      >
                        {dashboardData?.recent_switches?.length ? (
                          dashboardData.recent_switches.map((item, i) => (
                            <motion.div 
                              className="flex items-center" 
                              key={i}
                              variants={itemVariants}
                            >
                              <Avatar className="h-9 w-9 mr-4">
                                <User className="h-5 w-5" />
                              </Avatar>
                              <div className="space-y-1">
                                <Link 
                                  href={createSecureUserLink(item.switch_user?.id)} 
                                  className="text-sm font-medium leading-none hover:underline"
                                >
                                  {sanitizeString(item.switch_user?.name) || 'Unknown User'}
                                </Link>
                                <p className="text-sm text-muted-foreground">
                                  {item.switching_to_tariff ? 
                                    `Switched to ${sanitizeString(item.switching_to_tariff.supplier.name)}` : 
                                    'Pending to switch'}
                                </p>
                              </div>
                              <div className="ml-auto font-medium">
                                <Badge variant={
                                  item.status === 'completed' ? 'default' :
                                  item.status === 'pending' ? 'secondary' :
                                  'secondary'
                                }>
                                  {item.status}
                                </Badge>
                              </div>
                            </motion.div>
                          ))
                        ) : (
                          <div className="flex items-center justify-center h-[200px]">
                            <p className="text-muted-foreground">No recent switches data available</p>
                          </div>
                        )}
                      </motion.div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="users" className="space-y-4">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle>User Growth</CardTitle>
                      <CardDescription>Monthly user acquisition</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.2, duration: 0.5 }}
                      >
                        <LineChart 
                          data={userGrowthData}
                          x="month"
                          y="users"
                          height={350}
                          colors={["hsl(var(--primary))"]}
                          title="Monthly User Growth"
                          description="User growth trend based on available data"
                          valueFormatter={(value) => String(value)}
                        />
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <motion.div 
                        className="space-y-4"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                      >
                        {dashboardData?.recent_users?.length ? (
                          dashboardData.recent_users.map((user, i) => (
                            <motion.div 
                              className="flex items-center" 
                              key={i}
                              variants={itemVariants}
                            >
                              <Avatar className="h-10 w-10 mr-4">
                                <User className="h-6 w-6" />
                              </Avatar>
                              <div className="space-y-1">
                                <p className="text-sm font-medium leading-none">{sanitizeString(user.name)}</p>
                                <p className="text-sm text-muted-foreground">{obfuscateEmail(user.email)}</p>
                              </div>
                              <div className="ml-auto font-medium text-sm text-muted-foreground">
                                {formatDate(user.created_at)}
                              </div>
                            </motion.div>
                          ))
                        ) : (
                          <div className="flex items-center justify-center h-[200px]">
                            <p className="text-muted-foreground">No recent users data available</p>
                          </div>
                        )}
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>
              
              <TabsContent value="suppliers" className="space-y-4">
                <motion.div 
                  className="grid gap-4 grid-cols-1 lg:grid-cols-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.4 }}
                >
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1, duration: 0.5 }}
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>Switches by Supplier</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <SupplierSwitchesChart supplierData={dashboardData?.stats?.switches_by_supplier} />
                      </CardContent>
                    </Card>
                  </motion.div>
                  
                  <motion.div
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>Switches by Status</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(() => {
                          const statusData = dashboardData?.stats?.switches_by_status || {};
                          
                          // Make sure we have actual data
                          const hasData = Object.keys(statusData).length > 0;
                          
                          if (!hasData) {
                            return (
                              <div className="flex flex-col items-center justify-center h-[350px]">
                                <PieChart
                                  data={[{name: "No Data", value: 1}]}
                                  height={300}
                                />
                                <p className="text-muted-foreground text-center mt-4">No switch status data available</p>
                              </div>
                            );
                          }
                          
                          return (
                            <PieChart
                              data={Object.entries(statusData).map(([name, value]) => ({
                                name,
                                value
                              }))}
                              height={350}
                            />
                          );
                        })()}
                      </CardContent>
                    </Card>
                  </motion.div>
                </motion.div>
              </TabsContent>
              
              <TabsContent value="security" className="space-y-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle>Security Events Trend</CardTitle>
                      <CardDescription>Last 30 days</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {(() => {
                        const securityEventsData = dashboardData?.stats?.security_events_trend || {};
                        
                        // Make sure we have actual data
                        const hasData = Object.keys(securityEventsData).length > 0;
                        
                        if (!hasData) {
                          return (
                            <LineChart
                              data={[]}
                              x="date"
                              y="events"
                              height={350}
                              description="No security events recorded in the past 30 days"
                              title="Security Events"
                            />
                          );
                        }

                        return (
                          <LineChart
                            data={Object.entries(securityEventsData).map(([date, count]) => ({
                              date: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
                              events: count
                            }))}
                            x="date"
                            y="events"
                            height={350}
                            colors={["hsl(var(--primary))"]}
                            title="Security Events"
                            showLegend={false}
                            valueFormatter={(value) => String(value)}
                          />
                        );
                      })()}
                    </CardContent>
                  </Card>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle>Security Events by Type</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <motion.div 
                        className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                      >
                        {Object.keys(dashboardData?.security_events || {}).length ? (
                          Object.entries(dashboardData?.security_events || {}).map(([eventType, count], i) => (
                            <motion.div key={i} variants={itemVariants}>
                              <Card className="overflow-hidden transition-all duration-200 hover:shadow-md hover:bg-muted/50">
                                <CardContent className="pt-6">
                                  <div className="flex items-center justify-between">
                                    <div className="space-y-1">
                                      <p className="text-sm font-medium">{eventType}</p>
                                      <p className="text-2xl font-bold">{count}</p>
                                    </div>
                                    <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                                      <ShieldAlert className="h-6 w-6 text-red-600" />
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            </motion.div>
                          ))
                        ) : (
                          <div className="col-span-full flex items-center justify-center h-[150px]">
                            <p className="text-muted-foreground">No security events data available</p>
                          </div>
                        )}
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}