"use client"

import { Separator } from "@/components/ui/separator"
import DashboardLayout from "@/components/layout"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MailIcon, UserIcon } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export default function SettingsProfilePage() {
  const { data: session, status } = useSession()
  
  // Extract first letters of name for avatar fallback if available
  const getInitials = (name?: string) => {
    if (!name) return "U";
    return name.split(" ")
      .map(part => part.charAt(0))
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };
  
  return (
    <DashboardLayout
      title="Profile Settings"
      description="View your profile information"
    >
      <div className="h-full w-full">
        <div className="space-y-6 p-8">
          <div className="flex flex-col space-y-1">
            <h3 className="text-2xl font-semibold tracking-tight">Profile</h3>
            <p className="text-sm text-muted-foreground">
              Your profile information and settings
            </p>
          </div>
          <Separator />
          
          {status === "loading" ? (
            <div className="flex items-center justify-center h-40">
              <p className="text-muted-foreground">Loading profile information...</p>
            </div>
          ) : (
            <div className="grid gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16">
                      {session?.user?.name && (
                        <AvatarImage src={`https://avatar.vercel.sh/${session.user.name}.png`} alt={session.user.name} />
                      )}
                      <AvatarFallback className="text-xl">{getInitials(session?.user?.name)}</AvatarFallback>
                    </Avatar>
                    <div className="space-y-1">
                      <CardTitle>{session?.user?.name || "User"}</CardTitle>
                      <CardDescription>Account Information</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">User Details</h4>
                      <div className="grid gap-4">
                        <div className="flex items-center space-x-4 rounded-md border p-4">
                          <MailIcon className="h-5 w-5 text-muted-foreground" />
                          <div className="flex-1 space-y-1">
                            <p className="text-sm font-medium leading-none">Email</p>
                            <p className="text-sm text-muted-foreground">{session?.user?.email || "Not available"}</p>
                          </div>
                          <Badge>Primary</Badge>
                        </div>
                        {session?.user?.id && (
                          <div className="flex items-center space-x-4 rounded-md border p-4">
                            <UserIcon className="h-5 w-5 text-muted-foreground" />
                            <div className="flex-1 space-y-1">
                              <p className="text-sm font-medium leading-none">User ID</p>
                              <p className="text-sm text-muted-foreground">{session.user.id}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="border-t bg-muted/50 px-6 py-3">
                  <p className="text-xs text-muted-foreground">
                    This information is managed by the authentication system and cannot be modified here.
                  </p>
                </CardFooter>
              </Card>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
