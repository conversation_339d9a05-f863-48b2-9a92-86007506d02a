"use client"

import { useEffect, useState } from "react"
import dynamic from 'next/dynamic'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"

const profileFormSchema = z.object({
  number: z
    .string()
    .regex(/^(?:\+?61|0)[2-478](?:[ -]?[0-9]){8}$/, {
      message: "Please enter a valid Australian phone number.",
    }),
  email: z
    .string({
      required_error: "Please enter your email address.",
    })
    .email("Please enter a valid email address.")
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

// This can come from your database or API.
const defaultValues: Partial<ProfileFormValues> = {
  number: ""
}

const ProfileFormContent = () => {
  const [isMounted, setIsMounted] = useState(false)
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
    mode: "onTouched",
  })

  useEffect(() => {
    setIsMounted(true)
  }, [])

  function onSubmit(data: ProfileFormValues) {
    if (isMounted) {
      toast({
        title: "You submitted the following values:",
        description: (
          <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
            <code className="text-white">{JSON.stringify(data, null, 2)}</code>
          </pre>
        ),
      })
    }
  }

  if (!isMounted) {
    return null // or a loading spinner
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input 
                  placeholder="+***********" 
                  {...field} 
                  onBlur={(e) => {
                    field.onBlur();
                    form.trigger("number");
                  }}
                />
              </FormControl>
              <FormDescription>
                Please enter your Australian phone number. Format: +*********** or 0412345678.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter your email" 
                  {...field} 
                  onBlur={(e) => {
                    field.onBlur();
                    form.trigger("email");
                  }}
                />
              </FormControl>
              <FormDescription>
                Please enter your email address.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Update profile</Button>
      </form>
    </Form>
  )
}

export const ProfileForm = dynamic(() => Promise.resolve(ProfileFormContent), {
  ssr: false
})