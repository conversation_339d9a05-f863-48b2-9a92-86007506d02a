'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';

const routes = [
  '/dashboard/main',
  '/dashboard/energy-switches',
  '/dashboard/users',
  '/dashboard/suppliers',
  '/dashboard/security-events',
  '/dashboard/settings'
];

// Component to prefetch all main dashboard routes
export default function RoutePrefetcher() {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Prefetch all main routes that aren't the current route
    routes.forEach(route => {
      if (pathname !== route) {
        router.prefetch(route);
      }
    });
  }, [pathname, router]);

  // This is a hidden component that only handles prefetching
  return null;
}