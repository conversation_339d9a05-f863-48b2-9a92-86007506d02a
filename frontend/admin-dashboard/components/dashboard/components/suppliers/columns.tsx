"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Supplier } from "@/lib/suppliers"
import { DataTableColumnHeader } from "../data-table-column-header"
import { DataTableRowActions } from "./data-table-row-actions"
import { Star } from "lucide-react"

export const columns: ColumnDef<Supplier>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      const supplier = row.original
      return (
        <div className="flex items-center space-x-3">
          {supplier.logoUrl ? (
            <img 
              src={supplier.logoUrl} 
              alt={`${supplier.name} logo`}
              className="h-8 w-8 rounded object-contain"
            />
          ) : (
            <div className="h-8 w-8 bg-muted rounded flex items-center justify-center">
              <span className="font-bold text-sm">{supplier.name.charAt(0)}</span>
            </div>
          )}
          <span className="max-w-[500px] truncate font-medium">{supplier.name}</span>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: "tariffsCount",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tariffs" />
    ),
    cell: ({ row }) => {
      const count = row.getValue("tariffsCount") as number | undefined
      return (
        <div className="font-medium">
          {count !== undefined ? count : <span className="text-muted-foreground">N/A</span>}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: "trustpilotRating",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Rating" />
    ),
    cell: ({ row }) => {
      const ratingValue = row.getValue("trustpilotRating")
      
      if (ratingValue === null || ratingValue === undefined) {
        return <span className="text-muted-foreground">Not rated</span>
      }
      
      // Ensure rating is converted to a number
      const rating = typeof ratingValue === 'number' ? ratingValue : Number(ratingValue)
      
      // Check if conversion resulted in a valid number
      if (isNaN(rating)) {
        return <span className="text-muted-foreground">Invalid rating</span>
      }
      
      return (
        <div className="flex items-center">
          <Star className="h-4 w-4 text-yellow-500 mr-1" />
          <span>{rating.toFixed(1)}/5</span>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: "websiteUrl",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Website" />
    ),
    cell: ({ row }) => {
      const url = row.getValue("websiteUrl") as string | null
      
      if (!url) {
        return <span className="text-muted-foreground">No website</span>
      }
      
      return (
        <a 
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-500 hover:underline"
          onClick={(e) => e.stopPropagation()}
        >
          Visit website
        </a>
      )
    },
    enableSorting: false,
    enableHiding: true,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Added" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt") as string)
      return <div>{date.toLocaleDateString()}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
] 