"use client"

import { RxCross2 } from "react-icons/rx"
import { Table } from "@tanstack/react-table"
import { Search, X } from "lucide-react"
import { useState, useEffect, useRef } from "react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { DataTableViewOptions } from "../data-table-view-options"

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onSearch?: (value: string) => void
  initialSearchValue?: string
}

export function DataTableToolbar<TData>({
  table,
  onSearch,
  initialSearchValue,
}: DataTableToolbarProps<TData>) {
  const [searchValue, setSearchValue] = useState(initialSearchValue?.trim() || "")
  const inputRef = useRef<HTMLInputElement>(null)
  
  const isFiltered = 
    table.getState().columnFilters.length > 0 || searchValue.trim() !== ""
  
  // Update searchValue when initialSearchValue changes
  useEffect(() => {
    if (initialSearchValue !== undefined && initialSearchValue.trim() !== searchValue) {
      setSearchValue(initialSearchValue);
      // Maintain focus if input was previously focused
      if (document.activeElement === inputRef.current) {
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 10);
      }
    }
  }, [initialSearchValue]);
  
  // Force focus restoration when component updates
  useEffect(() => {
    const activeElement = document.activeElement;
    if (activeElement?.id === "suppliers-search-input") {
      setTimeout(() => inputRef.current?.focus(), 10);
    }
  });

  // Function to handle search
  const handleSearch = (value: string) => {
    setSearchValue(value)
    
    if (onSearch) {
      // Use server-side search if onSearch is provided
      onSearch(value)
    } else {
      // Fall back to client-side search if column is available
      if (table.getColumn("name")) {
        table.getColumn("name")?.setFilterValue(value)
      }
    }
  }

  // Function to reset all filters
  const resetFilters = () => {
    if (onSearch) {
      onSearch("")
    } else {
      table.resetColumnFilters()
    }
    setSearchValue("")
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <div className="relative w-[250px]">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            id="suppliers-search-input"
            ref={inputRef}
            placeholder="Search suppliers..."
            value={searchValue}
            onChange={(event) => handleSearch(event.target.value)}
            className="h-9 pl-8"
            // Add onBlur handler to help maintain focus
            onBlur={(e) => {
              // If we're still interacting with the page and not switching tabs/windows
              if (document.hasFocus()) {
                // Short delay to let other click events process first
                setTimeout(() => {
                  // Only refocus if we lost focus to body or similar neutral element
                  const activeElement = document.activeElement;
                  if (activeElement === document.body || activeElement === null) {
                    inputRef.current?.focus();
                  }
                }, 100);
              }
            }}
          />
        </div>
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={resetFilters}
            className="h-9 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
} 