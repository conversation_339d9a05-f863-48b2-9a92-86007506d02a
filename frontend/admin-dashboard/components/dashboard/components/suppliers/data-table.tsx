"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  PaginationState,
  Table,
} from "@tanstack/react-table"

import {
  Table as UITable,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { DataTablePagination } from "../data-table-pagination"
import { DataTableViewOptions } from "../data-table-view-options"
import { DataTableToolbar } from "./data-table-toolbar"
import { Loader2, Search, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isLoading?: boolean
  pageSize?: number
  onPageSizeChange?: (size: number) => void
  onPageChange?: (page: number) => void
  currentPage?: number
  totalItems?: number
  onSearch?: (searchValue: string) => void
  initialSearchValue?: string
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isLoading = false,
  pageSize = 10,
  onPageSizeChange,
  onPageChange,
  currentPage,
  totalItems,
  onSearch,
  initialSearchValue,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const searchTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  
  // Add debounced search function
  const debouncedSearch = React.useCallback((value: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Save current active element to restore focus later
    const activeElement = document.activeElement;
    const activeElementId = activeElement instanceof HTMLElement ? activeElement.id : null;
    
    searchTimeoutRef.current = setTimeout(() => {
      if (onSearch) {
        onSearch(value);
        
        // More robust focus restoration with multiple fallbacks
        setTimeout(() => {
          // Try to find the element by ID first
          if (activeElementId) {
            const elementById = document.getElementById(activeElementId);
            if (elementById) {
              elementById.focus();
              return;
            }
          }
          
          // Then try the element reference
          if (activeElement instanceof HTMLElement) {
            activeElement.focus();
            return;
          }
          
          // Last resort - find any search input
          const searchInputs = document.querySelectorAll('input[placeholder*="Search"]');
          if (searchInputs.length > 0 && searchInputs[0] instanceof HTMLElement) {
            searchInputs[0].focus();
          }
        }, 10); // Small delay to ensure DOM is ready
      }
    }, 500); // Wait 500ms after user stops typing
  }, [onSearch]);
  
  // Set up controlled pagination state
  const [{ pageIndex, pageSize: tablePgSize }, setPagination] = React.useState<PaginationState>({
    pageIndex: (currentPage || 1) - 1, // Adjust for 0-based index in the table
    pageSize: pageSize || 10,
  })
  
  // Update pagination state when props change
  React.useEffect(() => {
    if (currentPage !== undefined) {
      setPagination(prev => ({ ...prev, pageIndex: currentPage - 1 }))
    }
  }, [currentPage])
  
  React.useEffect(() => {
    if (pageSize !== undefined) {
      setPagination(prev => ({ ...prev, pageSize: pageSize }))
    }
  }, [pageSize])

  // Define pagination event handlers
  const handlePageChange = (newPageIndex: number) => {
    if (onPageChange) {
      // Convert 0-based index to 1-based page number for API
      onPageChange(newPageIndex + 1)
    }
  }
  
  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize)
    }
  }

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    pageCount: totalItems ? Math.ceil(totalItems / (tablePgSize || 10)) : undefined,
    manualPagination: !!onPageChange, // Enable manual pagination when onPageChange is provided
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex,
        pageSize: tablePgSize,
      },
    },
  })

  // Listen for pagination changes
  React.useEffect(() => {
    if (onPageChange && pageIndex !== (currentPage || 1) - 1) {
      handlePageChange(pageIndex)
    }
  }, [pageIndex])
  
  React.useEffect(() => {
    if (onPageSizeChange && tablePgSize !== pageSize) {
      handlePageSizeChange(tablePgSize)
    }
  }, [tablePgSize])

  return (
    <div className="space-y-4">
      <DataTableToolbar 
        table={table} 
        onSearch={debouncedSearch}
        initialSearchValue={initialSearchValue}
      />
      <div className="rounded-md border">
        <UITable>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="whitespace-nowrap">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell 
                  colSpan={columns.length} 
                  className="h-24 text-center"
                >
                  <div className="flex justify-center items-center gap-2">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    <span>Loading suppliers...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No suppliers found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </UITable>
      </div>
      <DataTablePagination 
        table={table} 
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  )
} 