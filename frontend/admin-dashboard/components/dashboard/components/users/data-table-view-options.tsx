"use client"

import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu"
import { RxMixerHorizontal } from "react-icons/rx"
import { Column, ColumnDef, Table } from "@tanstack/react-table"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>
}

// Helper function to format column IDs into readable labels
function formatColumnName(columnId: string): string {
  // Replace underscores and hyphens with spaces
  const spacedText = columnId.replace(/[_-]/g, ' ');
  
  // Capitalize each word
  return spacedText
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

// Helper function to extract column title from column definition
function getColumnDisplayName<TData, TValue>(column: Column<TData, TValue>): string {
  // First, try to extract the title from column.columnDef.meta
  const meta = column.columnDef.meta as { title?: string } | undefined;
  if (meta?.title) {
    return meta.title;
  }

  // Check for a common pattern where columns are defined with a title in the header
  // We store the title in column meta for reuse
  if (!meta?.title && typeof column.columnDef.header === 'function') {
    // We can't call the header function directly, so we'll fall back to formatting the ID
    // Look for known columns from our application
    if (column.id === 'email') return 'Email';
    if (column.id === 'name') return 'Name';
    if (column.id === 'phone_number') return 'Phone';
    if (column.id === 'date_of_birth') return 'DOB';
    if (column.id === 'requires_psr_support') return 'PSR Support';
    if (column.id === 'created_at') return 'Created';
    if (column.id === 'updated_at') return 'Updated';
    if (column.id === 'actions') return 'Actions';
  }
  
  // Fall back to formatting the column ID
  return formatColumnName(column.id);
}

export function DataTableViewOptions<TData>({
  table,
}: DataTableViewOptionsProps<TData>) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="ml-auto hidden h-8 lg:flex"
        >
          <RxMixerHorizontal className="mr-2 h-4 w-4" />
          View
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[180px]">
        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {table
          .getAllColumns()
          .filter(
            (column) =>
              typeof column.accessorFn !== "undefined" && column.getCanHide()
          )
          .map((column) => {
            // Get a display-friendly column name
            const displayName = getColumnDisplayName(column);
            
            return (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize"
                checked={column.getIsVisible()}
                onCheckedChange={(value) => column.toggleVisibility(!!value)}
              >
                {displayName}
              </DropdownMenuCheckboxItem>
            )
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
