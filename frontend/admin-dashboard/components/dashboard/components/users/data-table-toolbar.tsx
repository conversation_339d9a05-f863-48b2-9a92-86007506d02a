"use client"

import { RxCross2 } from "react-icons/rx"
import { Table } from "@tanstack/react-table"
import { Search, X } from "lucide-react"
import { useState, useEffect, useRef } from "react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DataTableViewOptions } from "./data-table-view-options"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onSearch?: (value: string) => void
  initialSearchValue?: string
  onFilterChange?: (filters: { psrSupport?: string, search?: string }) => void
  initialPsrSupport?: string
}

export function DataTableToolbar<TData>({
  table,
  onSearch,
  initialSearchValue,
  onFilterChange,
  initialPsrSupport,
}: DataTableToolbarProps<TData>) {
  const [searchValue, setSearchValue] = useState(initialSearchValue?.trim() || "")
  const [psrSupport, setPsrSupport] = useState(initialPsrSupport || "all")
  const inputRef = useRef<HTMLInputElement>(null)
  
  const isFiltered = 
    table.getState().columnFilters.length > 0 || 
    searchValue.trim() !== "" || 
    (psrSupport !== "all" && psrSupport !== "")
  
  // Update states when initial props change
  useEffect(() => {
    if (initialSearchValue !== undefined && initialSearchValue.trim() !== searchValue) {
      setSearchValue(initialSearchValue);
      // Maintain focus if input was previously focused
      if (document.activeElement === inputRef.current) {
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 10);
      }
    }
  }, [initialSearchValue]);
  
  useEffect(() => {
    if (initialPsrSupport !== undefined && initialPsrSupport !== psrSupport) {
      setPsrSupport(initialPsrSupport);
    }
  }, [initialPsrSupport]);
  
  // Force focus restoration when component updates
  useEffect(() => {
    const activeElement = document.activeElement;
    if (activeElement?.id === "users-search-input") {
      setTimeout(() => inputRef.current?.focus(), 10);
    }
  });

  // Function to handle global search
  const handleSearch = (value: string) => {
    setSearchValue(value)
    
    if (onFilterChange) {
      onFilterChange({ 
        search: value, 
        psrSupport: psrSupport === "all" ? "all" : psrSupport
      })
    } else if (onSearch) {
      // Legacy support for onSearch
      onSearch(value)
    } else {
      // Fall back to client-side search
      table.setGlobalFilter(value)
    }
  }
  
  // Function to handle PSR support filter change
  const handlePsrSupportChange = (value: string) => {
    setPsrSupport(value)
    
    if (onFilterChange) {
      // Always call onFilterChange to trigger backend refetch
      // Pass undefined for "all" to match backend controller's .present? check
      onFilterChange({ 
        psrSupport: value === "all" ? "all" : value,
        search: searchValue 
      })
      
      // Update URL when "all" is selected
      if (value === "all" && typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.delete("psr_support");
        window.history.replaceState({}, "", url.toString());
      }
    } else {
      // Fall back to client-side filtering
      if (table.getColumn("requires_psr_support")) {
        if (value === "all") {
          table.getColumn("requires_psr_support")?.setFilterValue("all")
        } else if (value === "required") {
          table.getColumn("requires_psr_support")?.setFilterValue("required")
        } else {
          table.getColumn("requires_psr_support")?.setFilterValue("not-required")
        }
      }
    }
  }

  // Function to reset all filters
  const resetFilters = () => {
    setSearchValue("")
    setPsrSupport("all")
    
    if (onFilterChange) {
      // Force a refetch with "all" value to ensure parent component updates correctly
      onFilterChange({ search: "", psrSupport: "all" })
      
      // Clear URL parameters when filters are reset
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.delete("psr_support");
        url.searchParams.delete("search");
        window.history.replaceState({}, "", url.toString());
      }
    } else if (onSearch) {
      onSearch("")
      table.resetColumnFilters()
    } else {
      table.setGlobalFilter("")
      table.resetColumnFilters()
    }
  }

  return (
    <div className="flex items-center justify-between gap-4 mb-4">
      <div className="flex flex-1 items-center space-x-2">
        <div className="relative w-[280px]">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            id="users-search-input"
            ref={inputRef}
            placeholder="Search by email, name or phone..."
            value={searchValue}
            onChange={(event) => handleSearch(event.target.value)}
            className="h-10 pl-8 bg-background text-foreground"
            // Add onBlur handler to help maintain focus
            onBlur={(e) => {
              // If we're still interacting with the page and not switching tabs/windows
              if (document.hasFocus()) {
                // Short delay to let other click events process first
                setTimeout(() => {
                  // Only refocus if we lost focus to body or similar neutral element
                  const activeElement = document.activeElement;
                  if (activeElement === document.body || activeElement === null) {
                    inputRef.current?.focus();
                  }
                }, 100);
              }
            }}
          />
        </div>
        
        {/* PSR Support Filter */}
        <div className="relative">
          <Select
            value={psrSupport}
            onValueChange={handlePsrSupportChange}
          >
            <SelectTrigger className="h-10 w-[180px] bg-background text-foreground">
              <SelectValue placeholder="All Support Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Support Statuses</SelectItem>
              <SelectItem value="required">PSR Support Required</SelectItem>
              <SelectItem value="not-required">No PSR Support</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={resetFilters}
            className="h-10 px-3"
          >
            Reset filters
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
