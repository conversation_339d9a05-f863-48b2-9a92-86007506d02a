"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { User, formatFullName } from "../../users/data/schema"
import { DataTableColumnHeader } from "./data-table-column-header"
import { DataTableRowActions } from "./data-table-row-actions"
import { format } from "date-fns"

export const columns: ColumnDef<User>[] = [
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => <div className="font-medium">{row.getValue("email")}</div>,
    enableSorting: true,
  },
  {
    id: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      const user = row.original;
      return <div>{formatFullName(user)}</div>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "phone_number",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Phone" />
    ),
    cell: ({ row }) => <div>{row.getValue("phone_number") || "Not available"}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "date_of_birth",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="DOB" />
    ),
    cell: ({ row }) => {
      const dob = row.getValue("date_of_birth") as string | null;
      if (!dob) return <div>Not available</div>;
      
      try {
        return <div>{format(new Date(dob), "dd MMM yyyy")}</div>;
      } catch (e) {
        return <div>{dob}</div>;
      }
    },
    enableSorting: true,
  },
  {
    accessorKey: "requires_psr_support",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="PSR Support" />
    ),
    cell: ({ row }) => {
      const requiresSupport = row.getValue("requires_psr_support") as boolean;
      return (
        <Badge variant={requiresSupport ? "destructive" : "outline"}>
          {requiresSupport ? "Required" : "Not Required"}
        </Badge>
      );
    },
    enableSorting: true,
    filterFn: (row, id, value) => {
      const requiresSupport = row.getValue(id) as boolean;
      return value === 'all' ? true : 
             value === 'required' ? requiresSupport : 
             !requiresSupport;
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const date = row.getValue("created_at") as string;
      return <div>{format(new Date(date), "dd MMM yyyy")}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Updated" />
    ),
    cell: ({ row }) => {
      const date = row.getValue("updated_at") as string;
      return <div>{format(new Date(date), "dd MMM yyyy HH:mm")}</div>;
    },
    enableSorting: true,
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]
