"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  FilterFn,
  PaginationState,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { DataTablePagination } from "../data-table-pagination"
import { DataTableToolbar } from "./data-table-toolbar"
import { User, formatFullName } from "../../users/data/schema"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void
  currentPage?: number
  pageSize?: number
  totalItems?: number
  onSearch?: (searchValue: string) => void
  initialSearchValue?: string
  onFilterChange?: (filters: { psrSupport?: string, search?: string }) => void
  initialPsrSupport?: string
}

// Define a global filter function that searches across multiple fields
const globalFilterFn: FilterFn<any> = (row, columnId, filterValue) => {
  // If no filter value, return all rows
  if (!filterValue || typeof filterValue !== 'string' || filterValue === '') {
    return true
  }

  const searchTerm = filterValue.toLowerCase()
  const user = row.original as User

  // Search in email
  const email = (user.email || '').toLowerCase()
  if (email.includes(searchTerm)) return true

  // Search in name
  const fullName = formatFullName(user).toLowerCase()
  if (fullName.includes(searchTerm)) return true

  // Search in phone number
  const phone = (user.phone_number || '').toLowerCase()
  if (phone.includes(searchTerm)) return true

  return false
}

export function DataTable<TData, TValue>({
  columns,
  data,
  onPageChange,
  onPageSizeChange,
  currentPage,
  pageSize,
  totalItems,
  onSearch,
  initialSearchValue,
  onFilterChange,
  initialPsrSupport,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  )
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [globalFilter, setGlobalFilter] = React.useState('')
  const searchTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  
  // Add debounced search function with proper cleanup
  const debouncedSearch = React.useCallback((value: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Save current active element to restore focus later
    const activeElement = document.activeElement;
    const activeElementId = activeElement instanceof HTMLElement ? activeElement.id : null;
    
    searchTimeoutRef.current = setTimeout(() => {
      if (onSearch) {
        onSearch(value);
        
        // More robust focus restoration with multiple fallbacks
        setTimeout(() => {
          // Try to find the element by ID first
          if (activeElementId) {
            const elementById = document.getElementById(activeElementId);
            if (elementById) {
              elementById.focus();
              return;
            }
          }
          
          // Then try the element reference
          if (activeElement instanceof HTMLElement) {
            activeElement.focus();
            return;
          }
          
          // Last resort - find any search input
          const searchInputs = document.querySelectorAll('input[placeholder*="Search"]');
          if (searchInputs.length > 0 && searchInputs[0] instanceof HTMLElement) {
            searchInputs[0].focus();
          }
        }, 10); // Small delay to ensure DOM is ready
      }
    }, 500); // Wait 500ms after user stops typing
  }, [onSearch]);
  
  // Clean up timeouts when component unmounts
  React.useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);
  
  // Set up controlled pagination state
  const [{ pageIndex, pageSize: tablePgSize }, setPagination] = React.useState<PaginationState>({
    pageIndex: (currentPage || 1) - 1, // Adjust for 0-based index in the table
    pageSize: pageSize || 10,
  })
  
  // Update pagination state when props change
  React.useEffect(() => {
    if (currentPage !== undefined) {
      setPagination(prev => ({ ...prev, pageIndex: currentPage - 1 }))
    }
  }, [currentPage])
  
  React.useEffect(() => {
    if (pageSize !== undefined) {
      setPagination(prev => ({ ...prev, pageSize: pageSize }))
    }
  }, [pageSize])

  // Define pagination event handlers
  const handlePageChange = (newPageIndex: number) => {
    if (onPageChange) {
      // Convert 0-based index to 1-based page number for API
      onPageChange(newPageIndex + 1)
    }
  }
  
  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize)
    }
  }

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      globalFilter,
      pagination: {
        pageIndex,
        pageSize: tablePgSize,
      },
    },
    pageCount: totalItems ? Math.ceil(totalItems / (tablePgSize || 10)) : undefined,
    enableRowSelection: true,
    manualPagination: !!onPageChange, // Enable manual pagination when onPageChange is provided
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    globalFilterFn: globalFilterFn,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  // Listen for pagination changes
  React.useEffect(() => {
    if (onPageChange && pageIndex !== (currentPage || 1) - 1) {
      handlePageChange(pageIndex)
    }
  }, [pageIndex])
  
  React.useEffect(() => {
    if (onPageSizeChange && tablePgSize !== pageSize) {
      handlePageSizeChange(tablePgSize)
    }
  }, [tablePgSize])

  return (
    <div className="space-y-4">
      <DataTableToolbar
        table={table}
        onSearch={debouncedSearch}
        initialSearchValue={initialSearchValue}
        onFilterChange={onFilterChange}
        initialPsrSupport={initialPsrSupport}
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-foreground">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} onPageSizeChange={handlePageSizeChange} />
    </div>
  )
}
