"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  FilterFn,
  PaginationState,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { DataTablePagination } from "../data-table-pagination"
import { DataTableToolbar } from "./data-table-toolbar"
import { SecurityEvent } from "@/lib/security-events"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isLoading?: boolean
  pageSize?: number
  onPageSizeChange?: (newPageSize: number) => void
  onPageChange?: (page: number) => void
  currentPage?: number
  totalItems?: number
  onSearch?: (searchValue: string) => void
  initialSearchValue?: string
  onFilterChange?: (filters: { eventType?: string, search?: string }) => void
  initialEventType?: string
}

// Define a global filter function for security events
const globalFilterFn: FilterFn<any> = (row, columnId, filterValue) => {
  // If no filter value, return all rows
  if (!filterValue || typeof filterValue !== 'string' || filterValue === '') {
    return true
  }

  const searchTerm = filterValue.toLowerCase()
  const event = row.original as SecurityEvent

  // Search in email
  const email = (event.email || '').toLowerCase()
  if (email.includes(searchTerm)) return true

  // Search in event type
  const eventType = (event.event_type || '').toLowerCase()
  if (eventType.includes(searchTerm)) return true

  // Search in IP address
  const ipAddress = (event.ip_address || '').toLowerCase()
  if (ipAddress.includes(searchTerm)) return true

  // Search in details if available
  if (event.details) {
    const detailsStr = JSON.stringify(event.details).toLowerCase()
    if (detailsStr.includes(searchTerm)) return true
  }

  return false
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isLoading = false,
  pageSize = 10,
  onPageSizeChange,
  onPageChange,
  currentPage,
  totalItems,
  onSearch,
  initialSearchValue,
  onFilterChange,
  initialEventType,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [globalFilter, setGlobalFilter] = React.useState('')
  
  // Set up controlled pagination state
  const [{ pageIndex, pageSize: tablePgSize }, setPagination] = React.useState<PaginationState>({
    pageIndex: (currentPage || 1) - 1, // Adjust for 0-based index in the table
    pageSize: pageSize || 10,
  })
  
  // Update pagination state when props change
  React.useEffect(() => {
    if (currentPage !== undefined) {
      setPagination(prev => ({ ...prev, pageIndex: currentPage - 1 }))
    }
  }, [currentPage])
  
  React.useEffect(() => {
    if (pageSize !== undefined) {
      setPagination(prev => ({ ...prev, pageSize: pageSize }))
    }
  }, [pageSize])

  // Define pagination event handlers
  const handlePageChange = (newPageIndex: number) => {
    if (onPageChange) {
      // Convert 0-based index to 1-based page number for API
      onPageChange(newPageIndex + 1)
    }
  }
  
  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize)
    }
  }

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      globalFilter,
      pagination: {
        pageIndex,
        pageSize: tablePgSize,
      },
    },
    pageCount: totalItems ? Math.ceil(totalItems / (tablePgSize || 10)) : undefined,
    manualPagination: !!onPageChange, // Enable manual pagination when onPageChange is provided
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    globalFilterFn: globalFilterFn,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })
  
  // Listen for pagination changes
  React.useEffect(() => {
    if (onPageChange && pageIndex !== (currentPage || 1) - 1) {
      handlePageChange(pageIndex)
    }
  }, [pageIndex])
  
  React.useEffect(() => {
    if (onPageSizeChange && tablePgSize !== pageSize) {
      handlePageSizeChange(tablePgSize)
    }
  }, [tablePgSize])

  return (
    <div className="space-y-4">
      <DataTableToolbar 
        table={table}
        onFilterChange={onFilterChange}
        initialSearchValue={initialSearchValue}
        initialEventType={initialEventType}
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading && data.length === 0 && (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex justify-center items-center space-x-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    <span>Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            )}
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              !isLoading && (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination 
        table={table}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  )
} 