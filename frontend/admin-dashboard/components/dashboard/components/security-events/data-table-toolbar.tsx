"use client"

import { RxCross2 } from "react-icons/rx"
import { Table } from "@tanstack/react-table"
import { Search, X } from "lucide-react"
import { useState, useEffect, useRef } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DataTableViewOptions } from "../users/data-table-view-options"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onFilterChange?: (filters: { eventType?: string, search?: string }) => void
  initialEventType?: string
  initialSearchValue?: string
}

// Define common event types for filtering
const COMMON_EVENT_TYPES = [
  "verification_success",
  "verification_failure",
  "verification_code_sent",
  "rate_limit_triggered",
  "login_success",
  "account_locked"
]

export function DataTableToolbar<TData>({
  table,
  onFilterChange,
  initialEventType,
  initialSearchValue,
}: DataTableToolbarProps<TData>) {
  const [searchValue, setSearchValue] = useState(initialSearchValue?.trim() || "")
  const [eventType, setEventType] = useState<string | null>(initialEventType || null)
  const inputRef = useRef<HTMLInputElement>(null)
  
  const isFiltered = 
    table.getState().columnFilters.length > 0 || searchValue.trim() !== "" || (eventType !== null)
  
  // Update state when props change
  useEffect(() => {
    if (initialEventType !== undefined && initialEventType !== eventType) {
      setEventType(initialEventType);
    }
  }, [initialEventType]);
  
  useEffect(() => {
    if (initialSearchValue !== undefined && initialSearchValue.trim() !== searchValue) {
      setSearchValue(initialSearchValue);
      // Maintain focus if input was previously focused
      if (document.activeElement === inputRef.current) {
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 10);
      }
    }
  }, [initialSearchValue]);

  // Function to handle search
  const handleSearch = (value: string) => {
    setSearchValue(value)
    
    if (onFilterChange) {
      onFilterChange({ 
        eventType: eventType || undefined,
        search: value
      })
    } else {
      // Fall back to client-side search if column is available
      if (table.getColumn("email")) {
        table.getColumn("email")?.setFilterValue(value)
      }
    }
  }

  // Function to handle event type change
  const handleEventTypeChange = (value: string) => {
    const newEventType = value === "all" ? "all" : value
    setEventType(newEventType)
    
    if (onFilterChange) {
      onFilterChange({ 
        eventType: newEventType || undefined, 
        search: searchValue 
      })
    } else {
      // Fall back to client-side filtering
      if (value === "all") {
        table.getColumn("event_type")?.setFilterValue("all")
      } else {
        table.getColumn("event_type")?.setFilterValue(value)
      }
    }
  }

  // Function to reset all filters
  const resetFilters = () => {
    setSearchValue("")
    setEventType(null)
    
    if (onFilterChange) {
      onFilterChange({ eventType: "all", search: "" })
    } else {
      table.resetColumnFilters()
    }
  }

  return (
    <div className="flex items-center justify-between gap-4 mb-4">
      <div className="flex flex-1 items-center space-x-2">
        {/* Search Input */}
        <div className="relative w-[250px]">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            id="security-events-search-input"
            ref={inputRef}
            placeholder="Search events..."
            value={searchValue}
            onChange={(event) => handleSearch(event.target.value)}
            className="h-9 pl-8"
          />
        </div>
        
        {/* Event Type Filter */}
        <div className="relative">
          <Select
            value={eventType || "all"}
            onValueChange={handleEventTypeChange}
          >
            <SelectTrigger className="h-9 w-[200px] bg-background text-foreground">
              <SelectValue placeholder="Event Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Event Types</SelectItem>
              {COMMON_EVENT_TYPES.map((type) => (
                <SelectItem key={type} value={type}>
                  {type.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase())}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={resetFilters}
            className="h-9 px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
} 