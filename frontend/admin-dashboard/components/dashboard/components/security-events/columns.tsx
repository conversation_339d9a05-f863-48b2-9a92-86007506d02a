"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { type SecurityEvent } from "@/lib/security-events"
import { DataTableColumnHeader } from "./data-table-column-header"
import { DataTableRowActions } from "./data-table-row-actions"
import { format } from "date-fns"

// Helper to determine badge style based on event type
const getEventTypeBadgeVariant = (eventType: string) => {
  switch (eventType) {
    case 'verification_success':
      return "default"
    case 'verification_failure':
      return "destructive"
    case 'verification_code_sent':
      return "secondary"
    case 'rate_limit_triggered':
      return "destructive"
    default:
      return "outline"
  }
}

// Format event type for display
const formatEventType = (eventType: string) => {
  return eventType
    .replace(/_/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase())
}

export const columns: ColumnDef<SecurityEvent>[] = [
  {
    accessorKey: "event_type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Event Type" />
    ),
    cell: ({ row }) => {
      const eventType = row.getValue("event_type") as string
      return (
        <Badge variant={getEventTypeBadgeVariant(eventType)}>
          {formatEventType(eventType)}
        </Badge>
      )
    },
    enableSorting: true,
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => {
      const email = row.getValue("email") as string | null
      return <div className="font-medium">{email || "N/A"}</div>
    },
    enableSorting: true,
  },
  {
    accessorKey: "ip_address",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="IP Address" />
    ),
    cell: ({ row }) => {
      const ipAddress = row.getValue("ip_address") as string | null
      return <div>{ipAddress || "N/A"}</div>
    },
    enableSorting: true,
  },
  {
    accessorKey: "details",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Details" />
    ),
    cell: ({ row }) => {
      const details = row.getValue("details") as Record<string, any> | null
      
      if (!details) return <div className="text-muted-foreground">No details</div>
      
      // Display only the first few key-value pairs if there are many
      const entries = Object.entries(details).slice(0, 2)
      
      return (
        <div className="max-w-[300px] truncate">
          {entries.map(([key, value]) => (
            <div key={key} className="text-xs">
              <span className="font-semibold">{key}:</span>{" "}
              {typeof value === 'object' ? JSON.stringify(value).substring(0, 30) : String(value).substring(0, 30)}
              {(typeof value === 'object' && JSON.stringify(value).length > 30) || 
               (typeof value === 'string' && value.length > 30) ? "..." : ""}
            </div>
          ))}
          {Object.keys(details).length > 2 && (
            <div className="text-xs text-muted-foreground">
              +{Object.keys(details).length - 2} more fields
            </div>
          )}
        </div>
      )
    },
    enableSorting: false,
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const date = row.getValue("created_at") as string
      return <div>{format(new Date(date), "dd MMM yyyy HH:mm:ss")}</div>
    },
    enableSorting: true,
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
] 