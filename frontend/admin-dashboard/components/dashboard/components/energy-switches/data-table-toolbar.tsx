"use client"

import { RxCross2 } from "react-icons/rx"
import { Table } from "@tanstack/react-table"
import { Search } from "lucide-react"
import { useState, useEffect, useRef } from "react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DataTableViewOptions } from "../users/data-table-view-options"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onSearch: (value: string) => void
  onStatusChange?: (value: string | null) => void
  initialSearchValue?: string
  initialStatus?: string | null
}

// Define switch statuses for filtering
const SWITCH_STATUSES = [
  "pending",
  "completed",
  "failed",
  "cancelled"
]

export function DataTableToolbar<TData>({
  table,
  onSearch,
  onStatusChange,
  initialSearchValue,
  initialStatus,
}: DataTableToolbarProps<TData>) {
  const [searchValue, setSearchValue] = useState(initialSearchValue?.trim() || "")
  const [selectedStatus, setSelectedStatus] = useState<string>(initialStatus || "all")
  const inputRef = useRef<HTMLInputElement>(null)
  
  const isFiltered = 
    table.getState().columnFilters.length > 0 || searchValue.trim() !== "" || selectedStatus !== "all"
  
  // Update searchValue when initialSearchValue changes
  useEffect(() => {
    if (initialSearchValue !== undefined && initialSearchValue.trim() !== searchValue) {
      setSearchValue(initialSearchValue);
      // Maintain focus if input was previously focused
      if (document.activeElement === inputRef.current) {
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 10);
      }
    }
  }, [initialSearchValue]);
  
  // Update selectedStatus when initialStatus changes
  useEffect(() => {
    if (initialStatus !== undefined) {
      setSelectedStatus(initialStatus || "all");
      
      // Also update the table's column filter
      if (table.getColumn("status")) {
        if (initialStatus) {
          table.getColumn("status")?.setFilterValue(initialStatus);
        } else {
          table.getColumn("status")?.setFilterValue(null);
        }
      }
    }
  }, [initialStatus, table]);
  
  // Force focus restoration when component updates
  useEffect(() => {
    const activeElement = document.activeElement;
    if (activeElement?.id === "energy-switches-search-input") {
      setTimeout(() => inputRef.current?.focus(), 10);
    }
  });

  // Function to handle search
  const handleSearch = (value: string) => {
    setSearchValue(value)
    // The actual search will be triggered by the debounced function in the parent
    onSearch(value)
  }

  // Function to reset all filters
  const resetFilters = () => {
    table.resetColumnFilters()
    setSearchValue("")
    setSelectedStatus("all")
    onSearch("")
    onStatusChange?.(null)
  }

  return (
    <div className="flex flex-col gap-4 mb-4">
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative w-[280px]">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              id="energy-switches-search-input"
              ref={inputRef}
              placeholder="Search by Reference Number or Email..."
              value={searchValue}
              onChange={(event) => handleSearch(event.target.value)}
              className="h-10 pl-8 bg-background text-foreground"
            />
          </div>
          
          {/* Status Filter */}
          {table.getColumn("status") && (
            <div className="relative">
              <Select
                value={selectedStatus}
                onValueChange={(value) => {
                  // Update local state first for immediate UI feedback
                  setSelectedStatus(value);
                  
                  // Then update table state and trigger backend filter
                  if (value === "all") {
                    table.getColumn("status")?.setFilterValue(null);
                    onStatusChange?.(null);
                  } else {
                    table.getColumn("status")?.setFilterValue(value);
                    onStatusChange?.(value);
                  }
                }}
              >
                <SelectTrigger className="h-10 w-[170px] bg-background text-foreground">
                  <SelectValue placeholder="Status">
                    {selectedStatus === "all" ? "All Statuses" : 
                     selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1)}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {SWITCH_STATUSES.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {isFiltered && (
            <Button
              variant="ghost"
              onClick={resetFilters}
              className="h-10 px-3"
            >
              Reset filters
              <RxCross2 className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
} 