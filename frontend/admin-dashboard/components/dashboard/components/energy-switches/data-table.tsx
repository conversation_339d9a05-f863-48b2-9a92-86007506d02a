"use client"

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  VisibilityState,
  FilterFn,
  PaginationState,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useState, useEffect, useRef, useCallback } from "react"
import { DataTablePagination } from "../data-table-pagination"
import { DataTableToolbar } from "./data-table-toolbar"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  isLoading?: boolean
  pageSize?: number
  onPageSizeChange?: (newPageSize: number) => void
  onPageChange?: (page: number) => void
  currentPage?: number
  totalItems?: number
  onSearch?: (searchValue: string) => void
  initialSearchValue?: string
  onStatusChange?: (status: string | null) => void
  initialStatus?: string | null
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isLoading = false,
  pageSize = 10,
  onPageSizeChange,
  onPageChange,
  currentPage,
  totalItems,
  onSearch,
  initialSearchValue,
  onStatusChange,
  initialStatus,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [globalFilter, setGlobalFilter] = useState<string>("")
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Debounced search function
  const debouncedSearch = useCallback((value: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Save current active element to restore focus later
    const activeElement = document.activeElement;
    
    searchTimeoutRef.current = setTimeout(() => {
      if (onSearch) {
        onSearch(value);
        
        // Restore focus to the previously focused element
        if (activeElement instanceof HTMLElement) {
          setTimeout(() => activeElement.focus(), 0);
        }
      }
    }, 500); // Wait 500ms after user stops typing
  }, [onSearch]);
  
  // Set up controlled pagination state
  const [{ pageIndex, pageSize: tablePgSize }, setPagination] = useState<PaginationState>({
    pageIndex: (currentPage || 1) - 1, // Adjust for 0-based index in the table
    pageSize: pageSize || 10,
  })
  
  // Update pagination state when props change
  useEffect(() => {
    if (currentPage !== undefined) {
      setPagination(prev => ({ ...prev, pageIndex: currentPage - 1 }))
    }
  }, [currentPage])
  
  useEffect(() => {
    if (pageSize !== undefined) {
      setPagination(prev => ({ ...prev, pageSize: pageSize }))
    }
  }, [pageSize])

  // Define pagination event handlers
  const handlePageChange = (newPageIndex: number) => {
    if (onPageChange) {
      // Convert 0-based index to 1-based page number for API
      onPageChange(newPageIndex + 1)
    }
  }
  
  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize)
    }
  }

  // Custom global filter function
  const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
    const searchTerm = value.toLowerCase();
    
    // Get the reference number value, safely handle null/undefined
    const refNumber = row.getValue("reference_number");
    const safeRefNumber = refNumber ? String(refNumber).toLowerCase() : '';
    
    // Get the email value, safely handle null/undefined
    const userEmail = row.getValue("userEmail");
    const safeUserEmail = userEmail ? String(userEmail).toLowerCase() : '';
    
    // Also try to match against the row's ID as fallback
    const id = (row.original as any)?.id;
    const safeId = id ? String(id).toLowerCase() : '';
    
    // Return true if any field contains the search term
    return (
      safeRefNumber.includes(searchTerm) || 
      safeUserEmail.includes(searchTerm) ||
      safeId.includes(searchTerm)
    );
  };

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    pageCount: totalItems ? Math.ceil(totalItems / (tablePgSize || 10)) : undefined,
    manualPagination: !!onPageChange, // Enable manual pagination when onPageChange is provided
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
      pagination: {
        pageIndex,
        pageSize: tablePgSize,
      },
    },
    // Use our custom fuzzy filter function
    globalFilterFn: fuzzyFilter,
  })

  // Listen for pagination changes
  useEffect(() => {
    if (onPageChange && pageIndex !== (currentPage || 1) - 1) {
      handlePageChange(pageIndex)
    }
  }, [pageIndex])
  
  useEffect(() => {
    if (onPageSizeChange && tablePgSize !== pageSize) {
      handlePageSizeChange(tablePgSize)
    }
  }, [tablePgSize])

  return (
    <div className="space-y-4">
      <DataTableToolbar 
        table={table} 
        onSearch={debouncedSearch} 
        initialSearchValue={initialSearchValue} 
        onStatusChange={onStatusChange}
        initialStatus={initialStatus}
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Loading...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No energy switches found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination 
        table={table} 
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  )
} 