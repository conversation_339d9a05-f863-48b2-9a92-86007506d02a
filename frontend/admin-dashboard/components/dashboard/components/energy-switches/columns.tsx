"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatDistanceToNow } from 'date-fns'
import { Eye, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { EnergySwitch as LibEnergySwitch } from '@/lib/energy-switches'

// Define the EnergySwitch type (should match the one in lib/energy-switches.ts)
export type EnergySwitch = {
  id: string
  userId: string
  userEmail?: string
  fromSupplier: string
  toSupplier: string
  status: 'draft' | 'confirmed' | 'submitted_to_supplier' | 'supplier_processing' | 'switched' | 'rejected_by_supplier'
  createdAt: string
  completedAt?: string
  failureReason?: string
  reference_number: string
}

// Convert lib EnergySwitch status to columns EnergySwitch status
export const mapEnergySwitch = (libSwitch: LibEnergySwitch): EnergySwitch => {
  // Map the status from lib format to columns format
  let mappedStatus: EnergySwitch['status'] = 'draft';
  
  switch(libSwitch.status) {
    case 'pending':
      mappedStatus = 'draft';
      break;
    case 'completed':
      mappedStatus = 'switched';
      break;
    case 'cancelled':
      mappedStatus = 'rejected_by_supplier';
      break;
    case 'failed':
      mappedStatus = 'rejected_by_supplier';
      break;
    case 'confirmed':
      mappedStatus = 'confirmed';
      break;
    case 'submitted_to_supplier':
      mappedStatus = 'submitted_to_supplier';
      break;
    default:
      mappedStatus = 'draft';
  }
  
  return {
    ...libSwitch,
    status: mappedStatus,
    reference_number: libSwitch.reference_number || 'N/A'
  };
};

// Badge variants based on status
const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case 'switched':
      return 'success'
    case 'draft':
    case 'confirmed':
      return 'outline'
    case 'submitted_to_supplier':
    case 'supplier_processing':
      return 'secondary'
    case 'rejected_by_supplier':
      return 'destructive'
    default:
      return 'default'
  }
}

export const columns: ColumnDef<EnergySwitch, unknown>[] = [
  {
    accessorKey: "referenceNumber",
    header: "Reference Number",
    cell: ({ row }) => {
      const reference = row.getValue("referenceNumber") as string
      return <div className="font-medium">{reference}</div>
    },
    filterFn: (row, id, value) => {
      const reference = row.getValue(id) as string;
      return reference?.toLowerCase().includes((value as string).toLowerCase()) || false;
    },
  },
  {
    accessorKey: "userEmail",
    header: "User Email",
    cell: ({ row }) => {
      return <div>{row.getValue("userEmail") || "N/A"}</div>
    },
    filterFn: (row, id, value) => {
      const email = row.getValue(id) as string | undefined;
      return email?.toLowerCase().includes((value as string).toLowerCase()) || false;
    },
  },
  {
    accessorKey: "fromSupplier",
    header: "From Supplier",
    filterFn: "equals",
  },
  {
    accessorKey: "toSupplier",
    header: "To Supplier",
    filterFn: "equals",
  },
  {
    accessorKey: "status",
    header: "Status",
    filterFn: "equals",
    cell: ({ row }) => {
      const status = row.getValue("status") as string
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Badge variant={getStatusBadgeVariant(status)}>
                {status === 'confirmed' 
                  ? 'Confirmed' 
                  : status.charAt(0).toUpperCase() + status.slice(1)}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              {status === 'failed' && row.original.failureReason 
                ? `Failure reason: ${row.original.failureReason}` 
                : `Switch ${status}`}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created",
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt") as string)
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div>{formatDistanceToNow(date, { addSuffix: true })}</div>
            </TooltipTrigger>
            <TooltipContent>
              {date.toLocaleString()}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    },
  },
  {
    accessorKey: "completedAt",
    header: "Completed",
    cell: ({ row }) => {
      const completedAt = row.getValue("completedAt") as string | undefined
      
      if (!completedAt) {
        return <div className="text-muted-foreground">-</div>
      }
      
      const date = new Date(completedAt)
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div>{formatDistanceToNow(date, { addSuffix: true })}</div>
            </TooltipTrigger>
            <TooltipContent>
              {date.toLocaleString()}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const id = row.original.id as string;
      
      return (
        <div className="flex items-center justify-end space-x-2">
          <Link href={`/dashboard/energy-switches/${id}`} passHref>
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>View</span>
            </Button>
          </Link>
        </div>
      );
    },
  },
] 