import { z } from "zod"

export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  first_name: z.string().nullable().optional(),
  last_name: z.string().nullable().optional(),
  title: z.string().nullable().optional(),
  phone_number: z.string().nullable().optional(),
  date_of_birth: z.string().nullable().optional(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  requires_psr_support: z.boolean().default(false),
});

export type User = z.infer<typeof userSchema>;

// Helper function to format full name
export function formatFullName(user: User): string {
  if (!user.first_name && !user.last_name) return 'No Name';
  return `${user.title || ''} ${user.first_name || ''} ${user.last_name || ''}`.trim();
}