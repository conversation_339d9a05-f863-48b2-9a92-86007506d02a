'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import useSWR from 'swr';
import { columns } from "../components/users/columns"
import { DataTable } from "../components/users/data-table"
import DashboardLayout from "@/components/layout";
import { User, getUsers, GetUsersParams, testApiConnection } from '@/lib/users';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2, Bug, RefreshCw } from 'lucide-react';
import { Button } from "@/components/ui/button";

export default function UserList() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get URL params or defaults
  const pageParam = searchParams.get('page');
  const pageSizeParam = searchParams.get('limit');
  const searchParam = searchParams.get('search');
  const psrSupportParam = searchParams.get('psr_support');
  
  // State for pagination and filters
  const [page, setPage] = useState(pageParam ? parseInt(pageParam) : 1);
  const [pageSize, setPageSize] = useState(pageSizeParam ? parseInt(pageSizeParam) : 10);
  const [searchTerm, setSearchTerm] = useState(searchParam || '');
  const [psrSupport, setPsrSupport] = useState(psrSupportParam || '');
  const [isClient, setIsClient] = useState(false);
  const [testingApi, setTestingApi] = useState(false);
  const [apiTestResult, setApiTestResult] = useState<any>(null);
  
  // Set isClient to true on component mount to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Update URL when filters change
  const updateUrl = useCallback(() => {
    const params = new URLSearchParams();
    
    if (page > 1) params.set('page', page.toString());
    if (pageSize !== 10) params.set('limit', pageSize.toString());
    if (searchTerm) params.set('search', searchTerm);
    if (psrSupport) params.set('psr_support', psrSupport);
    
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.push(newUrl, { scroll: false });
  }, [page, pageSize, searchTerm, psrSupport, router]);
  
  // Update URL when filters change
  useEffect(() => {
    if (isClient) {
      updateUrl();
    }
  }, [page, pageSize, searchTerm, psrSupport, isClient, updateUrl]);
  
  // Create fetcher function with the pagination parameters
  const fetcher = async () => {
    try {
      console.log('Fetching users with params:', { 
        page, 
        pageSize, 
        search: searchTerm,
        psr_support: psrSupport
      });
      
      const params: GetUsersParams = {
        page,
        limit: pageSize,
        search: searchTerm || undefined,
        psr_support: psrSupport || undefined
      };
      
      const result = await getUsers(params);
      return result;
    } catch (error) {
      console.error('Error in fetcher:', error);
      throw error;
    }
  };
  
  // Fetch data using SWR with the current parameters
  const { data, error, isLoading, mutate } = useSWR(
    isClient ? [`/api/switch_users`, page, pageSize, searchTerm, psrSupport] : null, 
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000, // 10 seconds
      onError: (err) => {
        console.error('SWR error:', err);
      }
    }
  );
  
  // Handle manual refresh
  const handleRefresh = async () => {
    console.log('Manual refresh triggered');
    // Use the revalidate API from SWR, with true to force revalidation
    // This ensures the isLoading state is properly set during refresh
    await mutate(undefined, { revalidate: true });
  };
  
  // Test API connection
  const handleTestApi = async () => {
    setTestingApi(true);
    setApiTestResult(null);
    try {
      const result = await testApiConnection();
      setApiTestResult(result);
    } catch (error) {
      setApiTestResult({ success: false, error: (error as Error).message });
    } finally {
      setTestingApi(false);
    }
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    console.log('Page changed to:', newPage);
    setPage(newPage);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    console.log('Page size changed to:', newPageSize);
    setPageSize(newPageSize);
    setPage(1); // Reset to first page when changing page size
  };
  
  // Handle search
  const handleSearch = (value: string) => {
    const trimmedValue = value.trim();
    setSearchTerm(trimmedValue);
    setPage(1); // Reset to first page when search changes
  };
  
  // Handle filter changes
  const handleFilterChange = (filters: { psrSupport?: string, search?: string }) => {
    // Reset to first page when changing filters
    setPage(1);
    
    // Update filter states
    if (filters.search !== undefined) {
      setSearchTerm(filters.search);
    }
    
    if (filters.psrSupport !== undefined) {
      const psrValue = filters.psrSupport === 'required' ? 'required' : 
                        filters.psrSupport === 'not-required' ? 'not-required' : '';
      setPsrSupport(psrValue);
    }
  };

  return (
    <DashboardLayout
      title="Energy Switch Users"
      description="Manage and view all registered users for energy switching"
    >
      <div className="w-full px-4 sm:px-6 lg:px-8">
        {/* Debug info during development - remove in production */}
        {process.env.NEXT_PUBLIC_DEBUG === 'true' && (
          <div className="bg-yellow-50 border border-yellow-200 p-4 mb-6 rounded-md">
            <div className="flex justify-between items-center">
              <h3 className="font-medium text-yellow-800">Debug Information</h3>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleTestApi} 
                disabled={testingApi}
                className="text-yellow-800 border-yellow-800 hover:bg-yellow-100"
              >
                {testingApi ? (
                  <><Loader2 className="h-4 w-4 animate-spin mr-2" /> Testing API</>
                ) : (
                  <><Bug className="h-4 w-4 mr-2" /> Test API Connection</>
                )}
              </Button>
            </div>
            <pre className="mt-2 text-xs overflow-auto max-h-40">
              {JSON.stringify({ 
                isClient, 
                isLoading, 
                hasError: !!error, 
                errorMessage: error?.message,
                hasData: !!data,
                userData: data ? `${data.users?.length} users` : 'No data',
                apiUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'Not set',
                currentPage: page,
                currentPageSize: pageSize,
                filters: {
                  searchTerm,
                  psrSupport
                }
              }, null, 2)}
            </pre>
            
            {apiTestResult && (
              <div className={`mt-4 p-2 rounded ${apiTestResult.success ? 'bg-green-100' : 'bg-red-100'}`}>
                <h4 className={`font-medium ${apiTestResult.success ? 'text-green-800' : 'text-red-800'}`}>
                  API Test Result: {apiTestResult.success ? 'Success' : 'Failed'}
                </h4>
                <pre className="mt-1 text-xs overflow-auto max-h-60">
                  {JSON.stringify(apiTestResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}

        <div className="flex justify-end mb-6">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            className="gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            {isLoading ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
        
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load users: {error.message}
            </AlertDescription>
          </Alert>
        )}
        
        <div className="w-full rounded-lg">
          <div>
            {isLoading && (!data?.users || data.users.length === 0) && (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading users...</span>
              </div>
            )}
            
            {(!isLoading || (data?.users && data.users.length > 0)) && (
              <>
                <DataTable 
                  columns={columns} 
                  data={data?.users || []} 
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  currentPage={page}
                  pageSize={pageSize}
                  totalItems={data?.total || 0}
                  onFilterChange={handleFilterChange}
                  initialSearchValue={searchTerm}
                  initialPsrSupport={psrSupport}
                />
                
                {data?.total > 0 && (
                  <div className="text-sm text-muted-foreground mt-2 px-2">
                    Total Users: {data.total}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
