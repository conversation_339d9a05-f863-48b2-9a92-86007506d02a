'use client';

import { useState, useEffect } from 'react';
import useSWR from 'swr';
import { columns } from "../components/suppliers/columns";
import { DataTable } from "../components/suppliers/data-table";
import DashboardLayout from "@/components/layout";
import { Supplier, getSuppliers, GetSuppliersParams } from '@/lib/suppliers';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2, Search, RefreshCw } from 'lucide-react';
import { Button } from "@/components/ui/button";

export default function SupplierList() {
  // State for pagination
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [isClient, setIsClient] = useState(false);
  
  // Set isClient to true on component mount to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Create fetcher function with the pagination parameters
  const fetcher = async () => {
    try {
      console.log('Fetching suppliers with params:', { page, pageSize, searchTerm });
      
      const params: GetSuppliersParams = {
        page,
        limit: pageSize,
      };
      
      if (searchTerm) {
        params.search = searchTerm;
      }
      
      const result = await getSuppliers(params);
      return result;
    } catch (error) {
      console.error('Error in fetcher:', error);
      throw error;
    }
  };
  
  // Fetch data using SWR with the current parameters
  const { data, error, isLoading, mutate } = useSWR(
    isClient ? [`/api/suppliers`, page, pageSize, searchTerm] : null, 
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 10000, // 10 seconds
      onError: (err) => {
        console.error('SWR error:', err);
      }
    }
  );
  
  // Handle manual refresh
  const handleRefresh = async () => {
    console.log('Manual refresh triggered');
    // Use the revalidate API from SWR, with true to force revalidation
    await mutate(undefined, { revalidate: true });
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    console.log('Page changed to:', newPage);
    setPage(newPage);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    console.log('Page size changed to:', newPageSize);
    setPageSize(newPageSize);
    setPage(1); // Reset to first page when changing page size
  };

  // Handle search
  const handleSearch = (value: string) => {
    const trimmedValue = value.trim();
    setSearchTerm(trimmedValue);
    setPage(1); // Reset to first page when search changes
  };

  return (
    <DashboardLayout
      title="Energy Suppliers"
      description="Manage and view all energy suppliers and their tariffs"
    >
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="flex justify-end mb-6">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            className="gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            {isLoading ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
        
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load suppliers: {error.message}
            </AlertDescription>
          </Alert>
        )}
        
        <div className="w-full rounded-lg">
          <div>
            {isLoading && !data?.suppliers && (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading suppliers...</span>
              </div>
            )}
            
            {data?.suppliers && (
              <DataTable 
                columns={columns} 
                data={data.suppliers}
                isLoading={isLoading}
                pageSize={pageSize}
                onPageSizeChange={handlePageSizeChange}
                onPageChange={handlePageChange}
                currentPage={page}
                totalItems={data.total}
                onSearch={handleSearch}
                initialSearchValue={searchTerm}
              />
            )}
            
            {data?.suppliers && data.suppliers.length > 0 && (
              <div className="text-sm text-muted-foreground mt-2 px-2">
                Total Suppliers: {data.total}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 