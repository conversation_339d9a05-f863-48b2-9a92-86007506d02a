'use client';

/* eslint-disable */
import NavLink from '@/components/link/NavLink';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { IRoute } from '@/types/types';
import { useTheme } from 'next-themes';
import { usePathname, useRouter } from 'next/navigation';
import { PropsWithChildren, useCallback, useEffect, useState } from 'react';
import { FaCircle } from 'react-icons/fa';
import { IoMdAdd } from 'react-icons/io';

interface SidebarLinksProps extends PropsWithChildren {
  routes: IRoute[];
  [x: string]: any;
}

export function SidebarLinks(props: SidebarLinksProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [activePathOverride, setActivePathOverride] = useState<string | null>(null);

  const { routes, onOpen } = props;

  // Prefetch all routes to make navigation feel instant
  useEffect(() => {
    routes.forEach((route) => {
      if (!route.disabled) {
        const path = route.layout ? route.layout + route.path : route.path;
        router.prefetch(path);
      }
    });
  }, [routes, router]);

  // Reset active path override when pathname changes
  useEffect(() => {
    setActivePathOverride(null);
  }, [pathname]);

  // verifies if routeName is the one active (in browser input)
  const activeRoute = useCallback(
    (routeName: string) => {
      // First check if we have an active path override
      if (activePathOverride) {
        return activePathOverride.includes(routeName);
      }
      // Otherwise use the actual pathname
      return pathname?.includes(routeName);
    },
    [pathname, activePathOverride],
  );

  // Handle navigation - add a click handler to optimize perceived performance
  const handleNavigate = (e: React.MouseEvent, href: string, routePath: string) => {
    // Immediately set the active path to give instant feedback
    setActivePathOverride(routePath);
    
    // This is crucial: We need to manually push the route to get immediate highlight
    // while keeping the next/link benefits for SEO
    e.preventDefault();
    router.push(href);
  };

  // this function creates the links and collapses that appear in the sidebar (left menu)
  const createLinks = (routes: IRoute[]) => {
    return routes.map((route, key) => {
      if (route.disabled) {
        return (
          <div
            key={key}
            className={`flex w-full max-w-full cursor-not-allowed items-center justify-between rounded-lg py-3 pl-8 font-medium`}
          >
            <div className="w-full items-center justify-center">
              <div className="flex w-full items-center justify-center">
                <div
                  className={`text mr-3 mt-1.5 text-zinc-950 opacity-30 dark:text-white`}
                >
                  {route.icon}
                </div>
                <p
                  className={`mr-auto text-sm text-zinc-950 opacity-30 dark:text-white`}
                >
                  {route.name}
                </p>
              </div>
            </div>
          </div>
        );
      } else {
        const href = route.layout ? route.layout + route.path : route.path;
        return (
          <div key={key}>
            <div
              className={`flex w-full max-w-full items-center justify-between rounded-lg py-3 pl-8 ${
                activeRoute(route.path.toLowerCase())
                  ? 'bg-primary font-semibold text-white dark:bg-white dark:text-primary'
                  : 'font-medium text-primary dark:text-zinc-400'
              }`}
            >
              <NavLink
                href={href}
                key={key}
                styles={{ width: '100%' }}
                onClick={(e: React.MouseEvent) => handleNavigate(e, href, route.path)}
              >
                <div className="w-full items-center justify-center">
                  <div className="flex w-full items-center justify-center">
                    <div
                      className={`text mr-3 mt-1.5 ${
                        activeRoute(route.path.toLowerCase())
                          ? 'font-semibold text-white dark:text-primary'
                          : 'text-primary dark:text-white'
                      } `}
                    >
                      {route.icon}
                    </div>
                    <p
                      className={`mr-auto text-sm ${
                        activeRoute(route.path.toLowerCase())
                          ? 'font-semibold text-white dark:text-zinc-950'
                          : 'font-medium text-zinc-950 dark:text-zinc-400'
                      }`}
                    >
                      {route.name}
                    </p>
                  </div>
                </div>
              </NavLink>
            </div>
          </div>
        );
      }
    });
  };
  // this function creates the links from the secondary accordions (for example auth -> sign-in -> default)
  const createAccordionLinks = (routes: IRoute[]) => {
    return routes.map((route: IRoute, key: number) => {
      const href = route.layout + route.path;
      return (
        <li className="mb-2.5 ml-[28px] flex max-w-full items-center" key={key}>
          <NavLink 
            href={href} 
            key={key}
            onClick={(e: React.MouseEvent) => handleNavigate(e, href, route.path)}
          >
            <FaCircle className="mr-2 h-1.5 w-1.5 text-zinc-950 dark:text-white" />
            <p
              className={`text-xs ${
                activeRoute(route.path.toLowerCase()) ? 'font-semibold' : ''
              } ${
                activeRoute(route.path.toLowerCase())
                  ? 'text-zinc-950 dark:text-white'
                  : 'text-zinc-950 dark:text-white'
              }`}
            >
              {route.name}
            </p>
          </NavLink>
        </li>
      );
    });
  };
  //  BRAND
  return <>{createLinks(routes)}</>;
}

export default SidebarLinks;
