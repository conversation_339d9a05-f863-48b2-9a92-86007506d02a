'use client';

import { Badge } from '../ui/badge';
import { But<PERSON> } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  renderThumb,
  renderTrack,
  renderView,
} from '@/components/scrollbar/Scrollbar';
import Links from '@/components/sidebar/components/Links';
import SidebarCard from '@/components/sidebar/components/SidebarCard';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card } from '@/components/ui/card';
import { IRoute } from '@/types/types';
import { Database } from '@/types/types_db';
import { useRouter } from 'next/navigation';
import React, { PropsWithChildren } from 'react';
import { useState } from 'react';
import { Scrollbars } from 'react-custom-scrollbars-2';
import { HiX } from 'react-icons/hi';
import { HiBolt } from 'react-icons/hi2';
import { HiOutlineArrowRightOnRectangle } from 'react-icons/hi2';
import Image from 'next/image';
import { signOut, useSession } from 'next-auth/react';
import { toast } from 'sonner';

export interface SidebarProps extends PropsWithChildren {
  routes: IRoute[];
  [x: string]: any;
}
interface SidebarLinksProps extends PropsWithChildren {
  routes: IRoute[];
  [x: string]: any;
}

type Price = Database['public']['Tables']['prices']['Row'];

function Sidebar(props: SidebarProps) {
  const router = useRouter();
  const { data: session, status } = useSession()
  const { routes } = props;
  const [signOutDialogOpen, setSignOutDialogOpen] = useState(false);

  const handleSignOutClick = () => {
    setSignOutDialogOpen(true);
  };

  const handleConfirmedSignOut = async () => {
    toast.info('Signing out...');
    await signOut({ callbackUrl: '/auth/login' });
  };

  // SIDEBAR
  return (
    <>
      <Dialog open={signOutDialogOpen} onOpenChange={setSignOutDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Sign out confirmation</DialogTitle>
            <DialogDescription>
              Are you sure you want to sign out of your account?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-row justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setSignOutDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmedSignOut}>
              Sign Out
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div
        className={`lg:!z-99 fixed !z-[99] min-h-full w-[300px] transition-all md:!z-[99] xl:!z-0 ${
          props.variant === 'auth' ? 'xl:hidden' : 'xl:block'
        } ${props.open ? '' : '-translate-x-[120%] xl:translate-x-[unset]'}`}
      >
        <Card
          className={`m-3 ml-3 h-[96.5vh] w-full overflow-hidden !rounded-lg border-zinc-200 pe-4 sm:my-4 sm:mr-4 md:m-5 md:mr-[-50px]`}
        >
          <Scrollbars
            autoHide
            renderTrackVertical={renderTrack}
            renderThumbVertical={renderThumb}
            renderView={renderView}
          >
            <div className="flex h-full flex-col justify-between">
              <div>
                <span
                  className="absolute top-4 block cursor-pointer text-zinc-200 xl:hidden"
                  onClick={() => props.setOpen(false)}
                >
                  <HiX />
                </span>
                <div className={`mt-8 flex items-center justify-center`}>
                  <Image 
                    priority
                    src="/meet-george-logo.png"
                    alt="Meet George Logo" 
                    width={180} 
                    height={60} 
                    className="h-auto" 
                    style={{
                      height: 'auto'
                    }}
                  />
                </div>
                <div className="mb-8 mt-8 h-px bg-zinc-200" />
                {/* Nav item */}
                <ul>
                  <Links routes={routes} />
                </ul>
              </div>
              <div className="mb-9 mt-7">
                {/* <div className="flex justify-center">
                  <SidebarCard />
                </div> */}
                {/* Sidebar profile info */}
                <div className="mt-5 flex w-full items-center rounded-lg border border-zinc-200">
                  <a href="/dashboard/settings">
                    <Avatar className="min-h-10 min-w-10">
                      <AvatarFallback className="font-bold">
                        SA
                      </AvatarFallback>
                    </Avatar>
                  </a>
                  <a href="/dashboard/settings">
                    <p className="ml-2 mr-3 flex items-center text-sm font-semibold leading-none text-zinc-950">
                      {session?.user?.name
                        ? session?.user?.name
                        : 'System Admin'}
                    </p>
                  </a>
                  <Button
                    variant="outline"
                    className="ml-auto flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full p-0 text-center text-sm font-medium hover:text-white"
                    onClick={handleSignOutClick}
                    title="Sign out"
                  >
                    <HiOutlineArrowRightOnRectangle
                      className="h-4 w-4 stroke-2 text-zinc-950"
                      width="16px"
                      height="16px"
                      color="inherit"
                    />
                  </Button>
                </div>
              </div>
            </div>
          </Scrollbars>
        </Card>
      </div>
    </>
  );
}

// PROPS

export default Sidebar;
