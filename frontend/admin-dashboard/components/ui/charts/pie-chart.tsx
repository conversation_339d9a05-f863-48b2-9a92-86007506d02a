"use client"

import { <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartPrim<PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer, Legend, TooltipProps } from "recharts"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

export interface PieChartProps {
  data: Array<{ name: string; value: number }>
  title?: string
  description?: string
  colors?: string[]
  height?: number
  showLegend?: boolean
  valueFormatter?: (value: number) => string
  className?: string
  children?: React.ReactNode
}

export function PieChart({
  data,
  title,
  description,
  colors = [
    "hsl(var(--primary))",
    "hsl(var(--primary) / 0.8)",
    "hsl(var(--primary) / 0.6)",
    "hsl(var(--primary) / 0.4)",
    "hsl(var(--muted))",
    "hsl(var(--muted) / 0.7)",
  ],
  height = 320,
  showLegend = true,
  valueFormatter = (value: number) => value.toString(),
  classN<PERSON>,
  children,
}: PieChartProps) {
  // Debug the incoming data
  console.log("<PERSON><PERSON>hart received data:", data);
  
  // If data is not an array or empty, provide sample data
  if (!Array.isArray(data) || !data.length) {
    console.log("PieChart - no data or invalid data format, using fallback");
    
    // Sample data for displaying a placeholder chart
    const sampleData = [
      { name: "No Data", value: 1 },
      { name: "Available", value: 1 }
    ];
    
    return (
      <Card className={cn(className)}>
        {title && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        <CardContent>
          <div className="flex justify-center items-center" style={{ height: height - 80 }}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChartPrimitive>
                <Pie
                  data={sampleData}
                  cx="50%"
                  cy="50%"
                  innerRadius={height > 200 ? 60 : 40}
                  outerRadius={height > 200 ? 80 : 60}
                  fill="#8884d8"
                  paddingAngle={2}
                  dataKey="value"
                  nameKey="name"
                >
                  {sampleData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill="hsl(var(--muted) / 0.5)" 
                    />
                  ))}
                </Pie>
              </PieChartPrimitive>
            </ResponsiveContainer>
          </div>
          <div className="text-center mt-4">
            <p className="text-muted-foreground">No data available</p>
          </div>
        </CardContent>
        {children && <CardFooter>{children}</CardFooter>}
      </Card>
    )
  }
  
  // Validate that data has name and value properties
  const validData = data.every(item => 'name' in item && 'value' in item);
  console.log("PieChart data validity check:", validData);
  
  if (!validData) {
    console.error("PieChart received invalid data - missing name or value properties");
    return (
      <Card className={cn(className)}>
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          <CardDescription>Chart data format error</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center p-6" style={{ height }}>
          <p className="text-muted-foreground">Chart data is missing required properties</p>
        </CardContent>
        {children && <CardFooter>{children}</CardFooter>}
      </Card>
    )
  }

  // Calculate total for percentage display
  const total = data.reduce((sum, item) => sum + item.value, 0)

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      const dataItem = payload[0].payload
      const percentage = ((dataItem.value / total) * 100).toFixed(1)
      
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <div className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: payload[0].color }}
            />
            <p className="font-semibold">{dataItem.name}</p>
          </div>
          <p className="mt-1">
            Value: {valueFormatter(dataItem.value)} ({percentage}%)
          </p>
        </div>
      )
    }
    return null
  }

  // Custom legend
  const CustomLegend = ({ payload }: any) => {
    return (
      <ul className="flex flex-wrap gap-4 justify-center mt-4">
        {payload.map((entry: any, index: number) => (
          <li key={`legend-${index}`} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm">{entry.value}</span>
          </li>
        ))}
      </ul>
    )
  }

  return (
    <Card className={cn(className)}>
      {title && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent className="p-0 px-1">
        <ResponsiveContainer width="100%" height={height}>
          <PieChartPrimitive>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={height > 200 ? 60 : 40}
              outerRadius={height > 200 ? 80 : 60}
              paddingAngle={2}
              dataKey="value"
              nameKey="name"
              labelLine={false}
              isAnimationActive={true}
              animationDuration={800}
              animationEasing="ease-out"
            >
              {data.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={colors[index % colors.length]} 
                  stroke="transparent"
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            {showLegend && (
              <Legend content={<CustomLegend />} />
            )}
          </PieChartPrimitive>
        </ResponsiveContainer>
      </CardContent>
      {children && <CardFooter>{children}</CardFooter>}
    </Card>
  )
}