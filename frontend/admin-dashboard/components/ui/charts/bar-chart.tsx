"use client"

import { <PERSON><PERSON><PERSON> as BarChartPrimitive, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, TooltipProps } from "recharts"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

export interface BarChartProps {
  data: any[]
  x: string
  y: string | string[]
  title?: string
  description?: string
  legendLabels?: Record<string, string>
  colors?: string[]
  height?: number
  showLegend?: boolean
  showXAxis?: boolean
  showYAxis?: boolean
  showGrid?: boolean
  valueFormatter?: (value: number) => string
  className?: string
  children?: React.ReactNode
}

export function BarChart({
  data,
  x,
  y,
  title,
  description,
  legendLabels,
  colors = ["hsl(var(--primary))", "hsl(var(--primary) / 0.8)", "hsl(var(--primary) / 0.6)"],
  height = 320,
  showLegend = false,
  showXAxis = true,
  showYAxis = true,
  showGrid = true,
  valueFormatter = (value: number) => value.toString(),
  className,
  children,
}: BarChartProps) {
  // Debug the incoming data
  console.log("BarChart received data:", data);
  console.log("x:", x, "y:", y);
  
  // Early return for empty data with debug indicator
  if (!data?.length) {
    console.log("BarChart has no data, showing empty state");
    return (
      <Card className={cn(className)}>
        {title && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        <CardContent className="flex justify-center items-center p-6" style={{ height }}>
          <p className="text-muted-foreground">No data available</p>
        </CardContent>
        {children && <CardFooter>{children}</CardFooter>}
      </Card>
    )
  }
  
  // Validate that data has the required properties
  const validData = data.every(item => x in item && (Array.isArray(y) ? y.some(key => key in item) : y in item));
  console.log("Data validity check:", validData);
  
  if (!validData) {
    console.error("BarChart received invalid data - missing x or y properties");
    return (
      <Card className={cn(className)}>
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          <CardDescription>Chart data format error</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center p-6" style={{ height }}>
          <p className="text-muted-foreground">Chart data is missing required properties</p>
        </CardContent>
        {children && <CardFooter>{children}</CardFooter>}
      </Card>
    )
  }

  // Create data fields array
  const yFields = Array.isArray(y) ? y : [y];

  // Calculate optimal bar size based on data length
  const barSize = Math.min(30, Math.max(10, 200 / data.length));

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: TooltipProps<any, any>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <p className="font-semibold">{label}</p>
          <div className="mt-1">
            {payload.map((entry, index) => {
              const fieldName = entry.dataKey as string
              const displayName = legendLabels?.[fieldName] || fieldName
              return (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }}
                  />
                  <p>
                    {displayName}: {valueFormatter ? valueFormatter(entry.value) : entry.value}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      )
    }
    return null
  }

  return (
    <Card className={cn(className)}>
      {title && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent className="p-0 px-1">
        <ResponsiveContainer width="100%" height={height}>
          <BarChartPrimitive
            data={data}
            margin={{
              top: 10,
              right: 15,
              left: 0,
              bottom: 0,
            }}
            barSize={barSize}
          >
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                stroke="hsl(var(--muted) / 0.5)"
                vertical={false} 
              />
            )}
            
            {showXAxis && (
              <XAxis 
                dataKey={x} 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                // Handle formatting of X axis ticks
                tickFormatter={(value) => {
                  if (data.length > 12) {
                    // For many data points, show fewer labels
                    const index = data.findIndex(item => item[x] === value);
                    return index % 3 === 0 ? value : '';
                  }
                  if (typeof value === 'string' && value.length > 10) {
                    return value.substring(0, 10) + '...';
                  }
                  return value;
                }}
              />
            )}
            
            {showYAxis && (
              <YAxis 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                // Format Y axis values
                tickFormatter={(value) => {
                  if (value >= 1000) {
                    return `${(value / 1000).toFixed(1)}k`;
                  }
                  return value;
                }}
              />
            )}
            
            <Tooltip content={<CustomTooltip />} cursor={{ fill: 'hsl(var(--muted) / 0.1)' }} />
            
            {showLegend && <Legend />}
            
            {yFields.map((field, index) => (
              <Bar
                key={field}
                dataKey={field}
                fill={colors[index % colors.length]}
                radius={[4, 4, 0, 0]}
                // Add some animation
                isAnimationActive={true}
                animationDuration={800}
                animationEasing="ease-in-out"
              />
            ))}
          </BarChartPrimitive>
        </ResponsiveContainer>
      </CardContent>
      {children && <CardFooter>{children}</CardFooter>}
    </Card>
  )
}