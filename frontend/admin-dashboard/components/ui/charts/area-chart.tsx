"use client"

import { AreaChart as AreaChartPrimitive, Area, XAxis, YA<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, Legend, TooltipProps } from "recharts"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

export interface AreaChartProps {
  data: any[]
  x: string
  y: string
  title?: string
  description?: string
  legendLabels?: Record<string, string>
  colors?: string[]
  height?: number
  showLegend?: boolean
  showXAxis?: boolean
  showYAxis?: boolean
  showGrid?: boolean
  className?: string
  children?: React.ReactNode
}

export function AreaChart({
  data,
  x,
  y,
  title,
  description,
  legendLabels,
  colors = ["hsl(var(--primary))", "hsl(var(--primary) / 0.3)"],
  height = 320,
  showLegend = false,
  showXAxis = true,
  showYAxis = true,
  showGrid = true,
  className,
  children,
}: AreaChartProps) {
  // Early return for empty data
  if (!data?.length) {
    return (
      <Card className={cn(className)}>
        {title && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        <CardContent className="flex justify-center items-center p-6" style={{ height }}>
          <p className="text-muted-foreground">No data available</p>
        </CardContent>
        {children && <CardFooter>{children}</CardFooter>}
      </Card>
    )
  }

  // Create data fields array
  const dataFields = Object.keys(data[0]).filter(key => key !== x)
  const yFields = typeof y === "string" ? [y] : dataFields.filter(field => field !== x)

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: TooltipProps<any, any>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-2 text-sm">
          <p className="font-semibold">{label}</p>
          <div className="mt-1">
            {payload.map((entry, index) => {
              const fieldName = entry.dataKey as string
              const displayName = legendLabels?.[fieldName] || fieldName
              return (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }}
                  />
                  <p>
                    {displayName}: {entry.value}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      )
    }
    return null
  }

  return (
    <Card className={cn(className)}>
      {title && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent className="p-0 px-1">
        <ResponsiveContainer width="100%" height={height}>
          <AreaChartPrimitive
            data={data}
            margin={{
              top: 10,
              right: 15,
              left: 0,
              bottom: 0,
            }}
          >
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                stroke="hsl(var(--muted) / 0.5)"
                vertical={false} 
              />
            )}
            
            {showXAxis && (
              <XAxis 
                dataKey={x} 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => {
                  // Handle different date formats or truncate long strings
                  if (value && value.length > 10) {
                    return value.substring(0, 10) + '...';
                  }
                  return value;
                }}
              />
            )}
            
            {showYAxis && (
              <YAxis 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={(value) => {
                  // Format large numbers
                  if (value >= 1000) {
                    return `${(value / 1000).toFixed(1)}k`;
                  }
                  return value;
                }}
              />
            )}
            
            <Tooltip content={<CustomTooltip />} />
            
            {showLegend && <Legend />}
            
            {yFields.map((field, index) => (
              <Area
                key={field}
                type="monotone"
                dataKey={field}
                stroke={colors[index % colors.length] || 'hsl(var(--primary))'}
                fill={colors[index % colors.length] || 'hsl(var(--primary) / 0.2)'}
                fillOpacity={0.2}
                strokeWidth={2}
                activeDot={{ r: 6, strokeWidth: 0 }}
                dot={false}
              />
            ))}
          </AreaChartPrimitive>
        </ResponsiveContainer>
      </CardContent>
      {children && <CardFooter>{children}</CardFooter>}
    </Card>
  )
}