'use client';

import Footer from '@/components/footer/FooterAdmin';
import Navbar from '@/components/navbar/NavbarAdmin';
import { routes } from '@/components/routes';
import Sidebar from '@/components/sidebar/Sidebar';
import { Toaster } from '@/components/ui/toaster';
import { getActiveRoute } from '@/utils/navigation';
import { useRouter, usePathname } from 'next/navigation';
import React, { useEffect } from 'react';

interface Props {
  children: React.ReactNode;
  title: string;
  description: string;
}

const DashboardLayout: React.FC<Props> = (props: Props) => {
  const pathname = usePathname();
  const router = useRouter();
  const [open, setOpen] = React.useState(false);

  // Track loading state for navigation
  const [isNavigating, setIsNavigating] = React.useState(false);
  
  // Create a navigation context that can be used by child components
  const navigationContext = React.useMemo(() => ({
    startNavigation: () => setIsNavigating(true),
    endNavigation: () => setIsNavigating(false),
    isNavigating
  }), [isNavigating]);

  // Prefetch all routes to make navigation feel faster
  useEffect(() => {
    // Prefetch all routes to improve navigation speed
    routes.forEach((route) => {
      if (!route.disabled) {
        const path = route.layout ? route.layout + route.path : route.path;
        router.prefetch(path);
      }
    });
  }, [router]);

  return (
    <div className="dark:bg-background-900 flex h-full w-full bg-white">
      <Toaster />
      <Sidebar
        routes={routes}
        open={open}
        setOpen={() => setOpen(!open)}
      />
      {/* Render the main layout immediately without waiting for data */}
      <div className="h-full w-full dark:bg-zinc-950">
        <main
          className={`mx-2.5 flex-none transition-all dark:bg-zinc-950 md:pr-2 xl:ml-[328px] flex flex-col min-h-screen`}
        >
          <Navbar
            onOpen={() => setOpen(!open)}
            brandText={getActiveRoute(routes, pathname)}
          />
          <div className="mx-auto w-full flex-grow p-2 !pt-[90px] md:p-2 md:!pt-[118px]">
            {props.children}
          </div>
          <div className="mt-auto">
            <Footer />
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
