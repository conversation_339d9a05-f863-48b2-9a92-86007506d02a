# Use the official Node.js image as the base image
FROM node:18.17.0

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Install the dependencies
RUN npm install

RUN npm run init

# Copy the rest of the application code to the working directory
COPY . .

# Expose the port the app will run on
EXPOSE 3001

# Start the application
CMD ["npm", "run", "dev"]
