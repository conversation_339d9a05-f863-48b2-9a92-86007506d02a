# Onboarding Flow with JWT Authentication

## Overview

The onboarding flow now uses a secure JWT token-based authentication mechanism. When a user clicks on an onboarding link from their email, the following sequence occurs:

1. User receives an email with a link: `https://example.com/onboarding?token=<jwt_token>`
2. Clicking the link brings them to the `/onboarding` page
3. The page extracts the JWT token from the URL query parameter
4. It makes a server-side API request to verify the token at `/api/v1/onboarding/verify?token=<token>`
5. If verification is successful, the user is redirected to `/onboarding/energy-switches/<energy_switch_id>`
6. The energy results page uses secure API routes to fetch the necessary data

## Security Improvements

- No sensitive information is exposed in URL paths
- JWT tokens are one-time use to prevent replay attacks
- API requests to fetch energy switch data use server-side authentication
- Rate limiting is implemented to prevent brute force attacks

## Implementation Details

### 1. `/onboarding` Page

This page verifies the JWT token and redirects to the appropriate page:

- Extracts the token from `?token=` query parameter
- Makes a server-side request to verify the token
- On success, redirects to the energy results page
- On failure, shows an error message

### 2. API Routes

The frontend uses local API routes to securely proxy requests to the backend:

- `/api/energy-switch/[id]` - Fetches energy switch data
- `/api/energy-switch/[id]/status` - Fetches status information

### 3. Environment Variables

The following environment variables are used:

- `API_URL` - Backend API URL (server-side)
- `NEXT_PUBLIC_API_URL` - Public API URL (client-side)

## Error Handling

- Invalid or expired tokens show a user-friendly error message
- Missing tokens redirect to the home page
- API errors are properly handled and displayed

## Deployment Notes

1. Ensure the `.env.local` file is configured with the correct API URLs
2. The backend API must support the JWT token verification endpoint
3. Cross-Origin Resource Sharing (CORS) must be configured to allow requests from the frontend
