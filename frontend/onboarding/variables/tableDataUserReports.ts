type RowObj = {
 checked?: string;
 email: string;
 provider: string;
 created: string;
 lastsigned: string;
 uuid: string;
 menu?: string;
};

const tableDataUserReports: RowObj[] = [
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:29', 
  lastsigned: '06 Nov, 2023 11:29', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Email',
  created: '06 Nov, 2023 11:21', 
  lastsigned: '06 Nov, 2023 11:21', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
 {
  checked:'',
  email: '<EMAIL>', 
  provider: 'Google',
  created: '06 Nov, 2023 11:33', 
  lastsigned: '06 Nov, 2023 11:33', 
  uuid: 'f3f42fc419-ce32-49fc-92df...',
 }, 
];

export default tableDataUserReports;
