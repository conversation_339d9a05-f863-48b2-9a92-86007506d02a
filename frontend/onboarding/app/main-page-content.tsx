'use client';

import React from 'react';
import { Shield, Lock, Mail, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export default function MainPageContent() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="w-full max-w-md px-4">
        <Card className="border-destructive/30 shadow-lg">
          <CardHeader className="space-y-1 flex flex-col items-center text-center pb-2">
            <div className="h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center mb-2">
              <Lock className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-2xl font-bold text-destructive">Access Denied</CardTitle>
            <CardDescription className="text-gray-500">
              You don't have permission to access this page
            </CardDescription>
          </CardHeader>
          <Separator className="my-2" />
          <CardContent className="space-y-4 pt-4">
            <div className="bg-muted p-4 rounded-lg space-y-2">
              <h3 className="font-medium text-sm">Need access?</h3>
              <p className="text-sm text-muted-foreground">
                Please contact your administrator to request access to this resource.
              </p>
              <div className="flex items-center gap-2 mt-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium"><EMAIL></span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-2">
            <Button className="w-full" variant="default">
              <Mail className="mr-2 h-4 w-4" />
              Contact Administrator
            </Button>
            <Button 
              className="w-full" 
              variant="outline" 
              onClick={() => { window.location.href = 'https://meetgeorge.co.uk/'}}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Website
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
} 