import CredentialsProvider from 'next-auth/providers/credentials';

/**
 * NextAuth configuration using JWT for verifying onboarding tokens
 */
export const authOptions = {
  providers: [
    CredentialsProvider({
      id: 'onboarding-token',
      name: 'Onboarding Token',
      credentials: {
        token: { label: "Token", type: "text" }
      },
      async authorize(credentials) {
        if (!credentials?.token) {
          return null;
        }

        try {
          // Call the token verification API
          const apiUrl = process.env.NEXT_PUBLIC_API_URL;
          const response = await fetch(`${apiUrl}/api/v1/onboarding/verify`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${credentials.token}`
            },
          });

          if (!response.ok) {
            console.log('Token verification failed:', response.status, response.statusText);
            return null;
          }

          const data = await response.json();
          
          // If verification is successful, create a user object with the energy switch data
          if (data.success) {
            return {
              id: data.user_id,
              energySwitchId: data.energy_switch_id,
              hasGas: data.has_gas,
              hasElectricity: data.has_electricity,
              accessToken: credentials.token
            };
          }
          
          return null;
        } catch (error) {
          console.error('Error authorizing token:', error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/onboarding'
  },
  callbacks: {
    async jwt({ token, user }) {
      // Include energy switch data in the JWT token
      if (user) {
        token.energySwitchId = user.energySwitchId;
        token.hasGas = user.hasGas;
        token.hasElectricity = user.hasElectricity;
        token.status = user.status;
        token.accessToken = user.accessToken;
      }
      return token;
    },
    async session({ session, token }) {
      // Pass energy switch data to the client session
      if (token) {
        session.user.energySwitchId = token.energySwitchId;
        session.user.hasGas = token.hasGas;
        session.user.hasElectricity = token.hasElectricity;
        session.accessToken = token.accessToken;
      }
      return session;
    }
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
}; 