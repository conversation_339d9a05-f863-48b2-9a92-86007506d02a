'use client';

import React from 'react';
import { Button } from "@/components/ui/button";

export default function CompletedSwitchClient() {
  return (
    <div className="min-h-screen bg-[#fff7ed] flex justify-center items-center">
      <div className="w-[1200px] p-6 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <CircleCheckIcon className="w-16 h-16 text-green-500 mx-auto" />
          <h2 className="mt-4 text-2xl font-bold">Your Switch is Complete!</h2>
          <p className="mt-2 text-gray-600">Your energy switch request has been sent to Tomato Energy.</p>
        </div>
        <div className="mt-6 p-4 bg-blue-50 rounded-md">
          <h3 className="font-bold text-blue-900">What happens next?</h3>
          <ul className="mt-2 text-blue-900 list-disc list-inside space-y-1">
            <li>Tomato Energy will process your switch request</li>
            <li>You'll receive a welcome email from them within 5 working days</li>
            <li>The switch process typically takes 14-21 days to complete</li>
            <li>Your energy supply will not be interrupted during the switch</li>
          </ul>
        </div>
        <div className="mt-6">
          <h3 className="font-bold">Your New Energy Plan Details</h3>
          <div className="mt-2 p-4 bg-gray-50 rounded-md grid grid-cols-2 gap-4">
            <div>
              <p>
                <strong>Supplier:</strong> Tomato Energy
              </p>
              <p>
                <strong>Fuel Type:</strong> Electricity & Gas
              </p>
              <p>
                <strong>Estimated Annual Cost:</strong> £1,250
              </p>
            </div>
            <div>
              <p>
                <strong>Tariff:</strong> Tomato Lifestyle
              </p>
              <p>
                <strong>Tariff Type:</strong> Variable
              </p>
              <p>
                <strong>Monthly Direct Debit:</strong> £104.16
              </p>
            </div>
          </div>
        </div>
        <div className="mt-6">
          <h3 className="font-bold">Important Information</h3>
          <ul className="mt-2 text-gray-600 list-disc list-inside space-y-1">
            <li>Keep paying your current supplier until the switch is complete</li>
            <li>Take a meter reading on your switch date (we'll email you a reminder)</li>
            <li>You have a 14-day cooling-off period if you change your mind</li>
          </ul>
        </div>
        <p className="mt-6 text-gray-600">We've sent a confirmation email to your registered address.</p>
        <div className="mt-4 text-center">
          <Button className="bg-blue-600 text-white px-4 py-2 rounded-md">Return to Dashboard</Button>
        </div>
      </div>
    </div>
  );
}

function CircleCheckIcon(props) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <path d="m9 12 2 2 4-4" />
    </svg>
  );
} 