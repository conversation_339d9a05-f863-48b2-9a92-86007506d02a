import { Suspense } from 'react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import OnboardingClient from './onboarding-client';

export default function OnboardingPage() {
  return (
    <Suspense 
      fallback={
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <div className="text-center">
            <LoadingSpinner size="large" />
            <h2 className="mt-4 text-xl font-semibold text-gray-700">Loading...</h2>
            <p className="mt-2 text-gray-500">Please wait while we prepare your page.</p>
          </div>
        </div>
      }
    >
      <OnboardingClient />
    </Suspense>
  );
} 