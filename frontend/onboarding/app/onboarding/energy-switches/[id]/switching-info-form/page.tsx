"use client"

import SwitchingInfoForm from '@/components/onboarding/switching-info-form';
import { useEnergySwitch } from '@/app/hooks/useEnergySwitchData';
import { useParams, useSearchParams } from 'next/navigation';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';

export default function SwitchingInfoFormPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const energySwitchId = params.id as string;
  const switchTo = searchParams.get('switch_to') || '';
  
  // Use SWR hook to fetch energy switch data
  const { energySwitch, isLoading, isError } = useEnergySwitch(energySwitchId);
  
  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="large" />
      </div>
    );
  }
  
  // Handle error state
  if (isError) {
    return (
      <ErrorMessage 
        title="Error Loading Data"
        message="We couldn't load your energy switch details. Please try again."
        actionText="Go Back"
        actionHref={`/onboarding/energy-switches/${energySwitchId}`}
      />
    );
  }
  
  // Handle missing data
  if (!energySwitch || !switchTo) {
    return (
      <ErrorMessage 
        title="Missing Information"
        message="We couldn't find all the information needed to complete your switch."
        actionText="Go Back"
        actionHref={`/onboarding/energy-switches/${energySwitchId}`}
      />
    );
  }

  return (
    <SwitchingInfoForm
      energySwitch={energySwitch}
      switchTo={switchTo}
    />
  );
}
