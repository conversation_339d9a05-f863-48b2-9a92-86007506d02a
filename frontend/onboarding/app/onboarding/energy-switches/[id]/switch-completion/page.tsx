"use client"

import SwitchCompletion from '@/components/onboarding/switch-completion';
import { useEnergySwitch } from '@/app/hooks/useEnergySwitchData';
import { useParams, useSearchParams } from 'next/navigation';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';

export default function SwitchCompletionPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const energySwitchId = params.id as string;
  const switchTo = searchParams.get('switch_to') || '';
  
  // Use SWR hook to fetch energy switch data
  const { energySwitch, isLoading, isError } = useEnergySwitch(energySwitchId);
  
  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="large" />
      </div>
    );
  }
  
  // Handle error state
  if (isError) {
    return (
      <ErrorMessage 
        title="Error Loading Data"
        message="We couldn't load your energy switch details. Please try again."
        actionText="Go Back"
        actionHref={`/onboarding/energy-switches/${energySwitchId}`}
      />
    );
  }
  
  // Handle missing data
  if (!energySwitch) {
    return (
      <ErrorMessage 
        title="Missing Information"
        message="We couldn't find the information needed to display your switch confirmation."
        actionText="Go Back"
        actionHref="/onboarding"
      />
    );
  }

  return <SwitchCompletion energySwitch={energySwitch} switchTo={switchTo} />;
}
