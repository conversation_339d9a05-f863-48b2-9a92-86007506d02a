"use client"

import { EnergyResults } from '@/components/onboarding/energy-results';
import SwitchCompletion from '@/components/onboarding/switch-completion';
import { useEnergySwitch } from '@/app/hooks/useEnergySwitchData';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';
import { NetworkErrorBoundary, useNetworkErrorHandler } from '@/components/ui/network-error-boundary';

function EnergyResultsPageContent() {
  const router = useRouter();
  const params = useParams();
  const energySwitchId = params.id as string;
  const { data: session } = useSession();
  const { handleError } = useNetworkErrorHandler();

  // Use SWR hook to fetch energy switch data
  const { energySwitch, isLoading, isError } = useEnergySwitch(energySwitchId);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  // Handle error state with network error detection
  if (isError) {
    const errorInfo = handleError(isError);
    return (
      <ErrorMessage
        title={errorInfo.isNetworkError ? "Connection Timeout" : "Error Loading Data"}
        message={errorInfo.userMessage}
        actionText="Try Again"
        actionHref={`/onboarding/energy-switches/${energySwitchId}`}
      />
    );
  }

  // Handle missing data
  if (!energySwitch) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return energySwitch.status === 'draft' ? (
    <EnergyResults energySwitch={energySwitch} />
  ) : (
    <SwitchCompletion energySwitch={energySwitch} switchTo={energySwitch.switching_to_tariff_id} />
  );
}

export default function EnergyResultsPage() {
  return (
    <NetworkErrorBoundary>
      <EnergyResultsPageContent />
    </NetworkErrorBoundary>
  );
}
