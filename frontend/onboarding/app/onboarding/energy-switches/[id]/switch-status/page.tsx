'use client';

import SwitchStatus from '@/components/onboarding/switch-status';
import { useEnergySwitchStatus } from '@/app/hooks/useEnergySwitchData';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';
import { useToast } from '@/components/ui/use-toast';

export default function SwitchStatusPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const params = useParams();
  const energySwitchId = params?.id as string;
  const { toast } = useToast();
  
  // Use SWR hook to fetch switch status data
  const { status: statusType, isLoading, isError } = useEnergySwitchStatus(energySwitchId);

  // Handle authentication loading state
  if (sessionStatus === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="large" />
        <span className="ml-3 text-gray-600">Authenticating...</span>
      </div>
    );
  }
  
  // Redirect unauthenticated users
  if (sessionStatus === 'unauthenticated') {
    router.push('/auth/login?callbackUrl=' + encodeURIComponent(`/onboarding/energy-switches/${energySwitchId}/switch-status`));
    return null;
  }
  
  // Handle data loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <LoadingSpinner size="large" />
        <span className="mt-4 text-gray-600">Loading your switch status...</span>
      </div>
    );
  }
  
  // Handle error state
  if (isError) {
    toast({
      title: "Error",
      description: "Failed to load switch status information",
      variant: "destructive",
    });
    
    return (
      <ErrorMessage 
        title="Error loading switch status"
        message="We had trouble retrieving your energy switch status. Please try again later."
        actionText="Return to Dashboard"
        actionHref="/onboarding"
      />
    );
  }
  
  // Handle missing data
  if (!statusType) {
    return (
      <ErrorMessage 
        title="Switch not found"
        message="We couldn't find details for this energy switch. Please check the URL or contact support."
        actionText="View all switches"
        actionHref="/onboarding"
      />
    );
  }

  return <SwitchStatus switchStatus={statusType} />;
}
