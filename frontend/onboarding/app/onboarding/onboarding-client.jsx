'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { signIn, useSession } from 'next-auth/react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';

export default function OnboardingClient() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tokenVerified, setTokenVerified] = useState(false);
  const [verificationAttempted, setVerificationAttempted] = useState(false);
  const {data: session, status} = useSession();

  console.log('Onboarding Client', { status, sessionExists: !!session, tokenVerified });

  // Handle token verification
  useEffect(() => {
    // This prevents multiple verification attempts
    if (verificationAttempted) {
      return;
    }
    
    async function verifyToken() {
      try {
        // Get token from sessionStorage (set by OnboardingLinkHandler)
        let token = null;
        if (typeof window !== 'undefined') {
          token = sessionStorage.getItem('onboardingToken');
          console.log('Retrieved token from sessionStorage:', token ? 'Token present' : 'No token');
        }
        
        if (!token) {
          console.warn('No token found in sessionStorage');
          setError('No token provided. Please use the link from your email.');
          setLoading(false);
          setVerificationAttempted(true);
          return;
        }

        console.log('Verifying token with authentication provider...');
        // Use NextAuth signIn to verify token
        const result = await signIn('onboarding-token', {
          token,
          redirect: false
        });
        
        if (result?.error) {
          console.error('Token verification failed:', result.error);
          setError('Invalid or expired token. Please use the link from your email.');
          setLoading(false);
          setVerificationAttempted(true);
          return;
        }

        console.log('Token verified successfully:', result?.ok ? 'Success' : 'Failed');
        // Mark token as verified so we can safely redirect later
        setTokenVerified(true);
        setVerificationAttempted(true);
        
        // Clear token from sessionStorage once used
        sessionStorage.removeItem('onboardingToken');
        console.log('Token removed from sessionStorage');
        
        // The session will be populated automatically with the user
      } catch (err) {
        console.error('Error in token verification:', err);
        setError('An unexpected error occurred. Please try again later.');
        setLoading(false);
        setVerificationAttempted(true);
      }
    }

    // Run verification if we don't have a session yet
    if (status === 'unauthenticated') {
      verifyToken();
    } else if (status === 'authenticated') {
      // If we already have a session, mark token as verified
      setTokenVerified(true);
      setVerificationAttempted(true);
    }
  }, [status, verificationAttempted]);

  // If authenticated, redirect to energy results page
  useEffect(() => {
    // Helper to safely access nested properties
    const getEnergySwitchId = () => {
      if (!session) return null;
      return session?.user?.energySwitchId || null;
    };
    
    // Only redirect after:
    // 1. We have an authenticated status
    // 2. Token has been verified (or an existing session is accepted)
    // 3. We have a session object
    if (status === 'authenticated' && tokenVerified && session) {
      const switchId = getEnergySwitchId();
      
      if (switchId) {
        console.log('✅ Authentication complete! Redirecting to energy switch:', switchId);
        router.push(`/onboarding/energy-switches/${switchId}`);
      } else {
        console.error('⚠️ Session is missing energy switch data:', JSON.stringify(session));
        setError('Session is missing energy switch data. Please try again.');
        setLoading(false);
      }
    } else if (status === 'authenticated' && !tokenVerified) {
      console.log('Session exists but token not yet verified - waiting before redirect');
    } else if (status === 'loading') {
      console.log('Session is still loading...');
    }
  }, [session, status, router, tokenVerified]);

  // Show appropriate UI based on the current state
  
  // Error state
  if (error) {
    return (
      <ErrorMessage 
        title="Verification Failed" 
        message={
          error || 
          'We were unable to verify your onboarding link. Please check that you\'re using the complete link from your most recent email. If you continue to experience issues, please contact our support <NAME_EMAIL> or try requesting a new link.'
        }
        actionText="Return to Website"
        actionHref="https://meetgeorge.co.uk/"
      />
    );
  }
  
  // Loading state - verifying the token
  if ((loading && status !== 'authenticated') || (status === 'unauthenticated' && !verificationAttempted)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-4 text-xl font-semibold text-gray-700">Verifying your link...</h2>
          <p className="mt-2 text-gray-500">Please wait while we confirm your details.</p>
        </div>
      </div>
    );
  }
  
  // Authenticated and verified, waiting for redirect
  if (status === 'authenticated' && tokenVerified && session?.user?.energySwitchId) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="small" />
          <h2 className="text-xl font-semibold text-gray-700">Redirecting...</h2>
          <p className="mt-2 text-gray-500">You are being redirected to your energy switch details.</p>
        </div>
      </div>
    );
  }
  
  // Authenticated but waiting for session data
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center">
        <LoadingSpinner size="large" />
        <h2 className="text-xl font-semibold text-gray-700">Loading your information...</h2>
        <p className="mt-2 text-gray-500">Please wait while we prepare your page.</p>
        <p className="mt-4 text-sm text-gray-400">Status: {status}, Session: {session ? 'Available' : 'Not available'}</p>
      </div>
    </div>
  );
} 