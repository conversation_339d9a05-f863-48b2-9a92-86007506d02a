"use client";

import { ThemeProvider } from './theme-provider';
import { Toaster } from '@/components/ui/toaster';
import { SessionProvider } from 'next-auth/react';
import OnboardingLinkHandler from './components/OnboardingLinkHandler';

export function ClientProviders({ children }) {
  return (
    <ThemeProvider forcedTheme="light" attribute="class" defaultTheme="light" enableSystem={false}>
      <SessionProvider>
        <Toaster />
        <OnboardingLinkHandler>
          <main id="skip">{children}</main>
        </OnboardingLinkHandler>
      </SessionProvider>
    </ThemeProvider>
  );
}