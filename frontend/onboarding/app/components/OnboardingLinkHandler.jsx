'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { signOut } from 'next-auth/react';

/**
 * Component that handles onboarding links with tokens
 * This component will only clear any existing session when a token is present
 * and let OnboardingClient handle the actual authentication.
 */
export default function OnboardingLinkHandler({ children }) {
  console.log('Onboarding Link Handler');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isProcessed, setIsProcessed] = useState(false);

  useEffect(() => {
    // This function handles token from URL
    const handleToken = async () => {
      // Check if we have a token in the URL hash fragment
      if (typeof window !== 'undefined') {
        const hash = window.location.hash;
        const tokenMatch = hash.match(/token=([^&]*)/);
        const token = tokenMatch ? tokenMatch[1] : null;
        
        if (token) {
          console.log('Found token in URL, beginning processing...');
          setIsProcessing(true);
          
          try {
            console.log('Processing token: clearing existing session and storage...');
            
            // First clear sessionStorage to remove any previous data
            sessionStorage.clear();
            
            // Sign out to clear any existing session
            await signOut({ redirect: false });
            console.log('Session signed out');
            
            // Store token in sessionStorage for OnboardingClient to use
            sessionStorage.setItem('onboardingToken', token);
            
            // Delay to ensure everything is processed
            await new Promise(resolve => setTimeout(resolve, 300));
            
            // Clear hash from URL to prevent reprocessing if page is refreshed
            if (window.history.replaceState) {
              // Remove the hash without causing a page refresh
              window.history.replaceState(null, document.title, window.location.pathname + window.location.search);
              console.log('Removed token from URL hash');
            }
            
            // Mark as processed so children can render
            setIsProcessed(true);
            setIsProcessing(false);
          } catch (error) {
            console.error('Error processing token:', error);
            setIsProcessing(false);
            setIsProcessed(true); // Continue even if there's an error
          }
        } else {
          // No token in URL, no processing needed
          console.log('No token found in URL, continuing normally');
          setIsProcessed(true);
        }
      } else {
        // Not in browser environment
        setIsProcessed(true);
      }
    };
    
    // Run the token handler
    handleToken();
  }, []);

  // Show loading state while processing token
  if (isProcessing) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700">Processing your link...</h2>
          <p className="mt-2 text-gray-500">Please wait a moment.</p>
        </div>
      </div>
    );
  }

  // Only render children after token is processed
  return isProcessed ? children : null;
} 