import '@/styles/globals.css';
import { ClientProviders } from './client-providers';

export const dynamic = 'force-dynamic';

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <title>
          Meet <PERSON>boarding
        </title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/img/favicon.ico" />
      </head>
      <body id={'root'} className="loading bg-white">
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
} 