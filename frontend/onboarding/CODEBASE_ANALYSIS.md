# Frontend Onboarding Codebase Analysis

## Overview
The frontend/onboarding is a Next.js 15 application that handles secure JWT-based onboarding flows for energy switching services. It uses NextAuth for authentication and provides a multi-step form interface for users to complete their energy provider switching process.

## Architecture

### Framework & Core Technologies
- **Next.js 15** - React framework with App Router
- **NextAuth** - Authentication library for JWT token verification
- **TypeScript/JavaScript** - Mixed TS/JS codebase
- **Tailwind CSS** - Styling framework
- **Shadcn/ui** - UI component library
- **Framer Motion** - Animation library

### Key Dependencies
- React 18.3.1
- Next.js 15.1.0
- NextAuth 4.24.6
- Tailwind CSS 3.4.3
- Radix UI components
- Axios for HTTP requests
- React Hook Form for form handling
- Zod for validation

## Application Structure

### Pages & Routing
- `/onboarding` - Main onboarding entry point with JWT token verification
- `/onboarding/energy-switches/[id]` - Energy switch details and forms
- `/onboarding/energy-switches/[id]/switch-completion` - Completion page

### Authentication Flow
1. User receives email with <PERSON><PERSON><PERSON> token in URL parameter
2. `OnboardingLinkHandler` extracts token from URL and stores in sessionStorage
3. `OnboardingClient` verifies token via NextAuth custom provider
4. On success, redirects to energy switch form
5. Uses server-side API routes for secure backend communication

### Key Components

#### Authentication Components
- `OnboardingLinkHandler` - Extracts and stores JWT tokens from URL
- `OnboardingClient` - Handles token verification and session management
- Custom NextAuth provider for JWT token validation

#### Form Components
- `SwitchingInfoForm` - Multi-step form (Personal, Security, Payment)
- `PersonalInfoTab`, `SecurityInfoTab`, `PaymentInfoTab` - Form steps
- `ComparisonCards` - Energy tariff comparison
- `TariffLightbox` - Detailed tariff information modal

#### UI Components
- Extensive use of Shadcn/ui components
- Custom loading spinners and error messages
- Responsive design with Tailwind CSS

### Security Features
- JWT-based authentication with one-time use tokens
- Server-side token verification
- Rate limiting protection
- Secure API routes for backend communication
- No sensitive data in URL paths

### State Management
- NextAuth session management
- React Hook Form for form state
- SessionStorage for temporary token storage
- Local component state with React hooks

## Configuration Files

### Build & Development
- `next.config.js` - Next.js configuration with image domains
- `package.json` - Dependencies and scripts
- `Dockerfile` - Container configuration
- `docker-compose.yml` - Development environment

### Styling & UI
- `tailwind.config.ts` - Tailwind configuration with custom theme
- `components.json` - Shadcn/ui configuration
- `postcss.config.js` - PostCSS configuration

### TypeScript
- `tsconfig.json` - TypeScript configuration
- Mixed JS/TS files throughout the codebase

## API Integration
- Uses environment variables for API URLs (`API_URL`, `NEXT_PUBLIC_API_URL`)
- Server-side API routes in `/api` directory
- Axios for HTTP client
- CORS configuration required for backend communication

## Deployment
- Runs on port 3001
- Docker containerization
- Environment-specific configuration via `.env.local`
- Vercel deployment ready

## Current Challenges
1. Mixed JS/TS codebase could benefit from full TypeScript migration
2. NextAuth dependency for simple JWT verification
3. Next.js overhead for what could be a simpler SPA
4. Complex authentication flow with multiple redirects
5. Heavy dependency footprint

## Strengths
- Secure JWT-based authentication
- Responsive, accessible UI
- Good separation of concerns
- Comprehensive error handling
- Docker containerization
- Well-documented onboarding flow