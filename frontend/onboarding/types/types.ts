import { ComponentType, ReactNode } from 'react';


export type OpenAIModel = 'gpt-3.5-turbo' | 'gpt-4' | 'gpt-4-1106-preview'| 'gpt-4o';


export interface Deal {
  id?: string;
  supplier_name: string;
  tariff_name: string;
  profile_class: number;
  tariff_type: string;
  fuel_type?: string; // Keeping this as it might be useful in other contexts
  electricity?: {
    unit_rate?: number;
    standing_charge: number;
    day_unit_rate?: number;
    night_unit_rate?: number;
    weekend_night_rate?: number;
    estimated_monthly_cost?: number;
    estimated_yearly_cost?: number;
    payment_method?: string;
    meter_serial_number?: string;
    mpan?: string;
    monthly_usage?: number;
    formatted_estimated_cost?: string;
    estimated_costs?: number[];
  };
  gas?: {
    unit_rate?: number;
    standing_charge: number;
    day_unit_rate?: number;
    night_unit_rate?: number;
    weekend_night_rate?: number;
    estimated_monthly_cost?: number;
    estimated_yearly_cost?: number;
    payment_method?: string;
    meter_serial_number?: string;
    mprn?: string;
    monthly_usage?: number;
    estimated_costs?: number[];
    formatted_estimated_cost?: string;
    meter_point_reference_number?: string;
  };
  exit_fee: number;
  estimated_cost: { monthly: number; yearly: number };
  estimated_saving: { amount: number; percentage: number };
  payment_methods: string[];
  tariff_ends: string;
  price_guaranteed: string;
  discounts: string;
  additional_charges: string;
  additional_services: string;
  logo: string;
  trustpilot_rating: number;
  about: string;
  fuel_types: string[];
  total_estimated_costs: {
    gas: number[];
    electricity: number[];
    total: number[];
  };
}

export interface EnergySwitch {
  id: string;
  status: string;
  available_tariffs: Deal[];
  reference_number: string;
  current_supplier: Deal;
  switch_user: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string
  };
  address: {
    postcode: string;
    full_address: string;
    posttown: string;
  };
  gas: null | {
    meter_point_reference_number: string;
    meter_point_administration_number: string | null;
    meter_serial_number: string;
    unit_rate: number;
    standing_charge: number;
    supplier_name: string | null;
    payment_method: string | null;
    tariff_type: string;
    estimated_annual_usage: number | null;
    monthly_usage: number | null;
  };
  electricity: null | {
    meter_point_reference_number: string;
    meter_point_administration_number: string | null;
    meter_serial_number: string;
    unit_rate: number;
    standing_charge: number;
    supplier_name: string | null;
    payment_method: string | null;
    tariff_type: string;
    estimated_annual_usage: number | null;
    monthly_usage: number | null;
  };
  switching_to_tariff_id: string | null;
}

export interface SwitchStatusType {
  currentStage: number;
  referenceNumber: string;
  supplier: string;
  switchDate: string;
  isRejected: boolean;
  stages: string[];
}

export interface IRoute {
  path: string;
  name: string;
  layout?: string;
  exact?: boolean;
  component?: ComponentType;
  disabled?:boolean
  icon?: JSX.Element ;
  secondary?: boolean;
  collapse?: boolean;
  items?: IRoute[];
  rightElement?: boolean;
  invisible?: boolean;
}