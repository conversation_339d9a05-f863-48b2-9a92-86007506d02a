// Auth Imports
import { <PERSON><PERSON><PERSON><PERSON> } from '@/types/types';
import {
  HiOutlineHome,
  HiOutlineCpuChip,
  HiOutlineUsers,
  HiOutlineUser,
  HiOutlineCog8Tooth,
  HiOutlineCreditCard,
  HiOutlineDocumentText,
  HiOutlineCurrencyDollar
} from 'react-icons/hi2';

export const routes: IRoute[] = [
  // {
  //   name: 'Main Dashboard',
  //   path: '/dashboard/main',
  //   icon: <HiOutlineHome className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />,
  //   collapse: false
  // },
  // {
  //   name: '<PERSON> Chat',
  //   path: '/dashboard/ai-chat',
  //   icon: (
  //     <HiOutlineCpuChip className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
  //   ),
  //   collapse: false
  // },
  {
    name: 'Energy Results',
    path: '/onboarding/energy-switches',
    icon: (
      <HiOutlineUsers className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
    ),
    collapse: false
  },
  // {
  //   name: 'Profile Settings',
  //   path: '/dashboard/settings',
  //   icon: (
  //     <HiOutlineCog8Tooth className="-mt-[7px] h-4 w-4 stroke-2 text-inherit" />
  //   ),
  //   collapse: false,
  //   disabled: true
  // },
];
