import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, Card<PERSON><PERSON><PERSON>, Card<PERSON>oot<PERSON> } from "@/components/ui/card"
import { CheckCircle, Circle, XCircle, Info, CalendarDays, Mail, Clock, AlertTriangle, ChevronRight, Home, Phone, ArrowUpRight } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useMemo } from "react"
import { Footer } from "../footer"
import { SwitchStatusType } from "@/types/types"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatDate } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

// Theme constants
const THEME = {
  primary: "#FE6232",
  success: "#22c55e",
  error: "#ef4444",
  warning: "#f59e0b",
  info: "#3b82f6",
  background: "#FAFAFA"
}

/**
 * Component that displays the current status of an energy switch
 */
export default function SwitchStatus({ switchStatus }: { switchStatus: SwitchStatusType }) {
  const stages = switchStatus.stages;
  
  // Calculate progress percentage for the progress bar
  const progressPercentage = useMemo(() => {
    // Adjust for zero-indexing and calculate percentage
    const progress = (switchStatus.currentStage - 1) / (stages.length - 1);
    return Math.max(0, Math.min(100, progress * 100)); // Ensure between 0-100%
  }, [switchStatus.currentStage, stages.length]);
  
  // Determine status details based on current stage and rejection status
  const statusDetails = useMemo(() => {
    if (switchStatus.isRejected) {
      return {
        title: "Rejected",
        message: "Your switch request has been rejected.",
        badgeText: "Rejected",
        badgeVariant: "destructive",
        className: "bg-red-50 border-red-100",
        progressBarClass: "bg-red-500",
        icon: <XCircle className="w-5 h-5 text-red-500 mr-2" aria-hidden="true" />,
        headerIcon: <AlertTriangle className="w-10 h-10 text-red-500 mb-2" aria-hidden="true" />
      };
    }
    
    // Map stage to appropriate status information
    const statusMap = {
      1: {
        message: "Your switch request has been received and is being prepared for processing.",
        badgeText: "Received",
        badgeVariant: "outline",
        headerIcon: <Clock className="w-10 h-10 text-blue-500 mb-2" aria-hidden="true" />
      },
      2: {
        message: "We're currently processing your switch request with your new supplier.",
        badgeText: "Processing",
        badgeVariant: "secondary",
        headerIcon: <Clock className="w-10 h-10 text-amber-500 mb-2" aria-hidden="true" />
      },
      3: {
        message: `Your switch to ${switchStatus.supplier} is in progress.`,
        badgeText: "In Progress",
        badgeVariant: "secondary",
        headerIcon: <Clock className="w-10 h-10 text-amber-500 mb-2" aria-hidden="true" />
      },
      4: {
        message: `Congratulations! Your switch to ${switchStatus.supplier} is now complete.`,
        badgeText: "Completed",
        badgeVariant: "success",
        headerIcon: <CheckCircle className="w-10 h-10 text-green-500 mb-2" aria-hidden="true" />
      }
    };
    
    return {
      title: stages[switchStatus.currentStage - 1],
      className: "bg-green-50 border-green-100",
      progressBarClass: "bg-green-500",
      icon: <CheckCircle className="w-5 h-5 text-green-500 mr-2" aria-hidden="true" />,
      ...statusMap[switchStatus.currentStage as keyof typeof statusMap]
    };
  }, [switchStatus, stages]);

  return (
    <div className="font-sans min-h-screen bg-gray-50">
      <div className="bg-gradient-to-b from-[#FAFAFA] to-gray-50 pb-0">
        <div className="max-w-[1200px] mx-auto px-4 py-8">
          <header className="flex justify-between items-center mb-8">
            <div className="flex items-center">
              <Image 
                priority
                src="/meet-george-logo.png"
                alt="Meet George Logo" 
                width={180} 
                height={60} 
                className="h-auto" 
                onError={(e) => {
                  // Fallback if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    const fallback = document.createElement('div');
                    fallback.className = 'text-2xl font-bold text-[#FE6232]';
                    fallback.textContent = 'Meet George';
                    parent.appendChild(fallback);
                  }
                }}
              />
              <Badge 
                variant="outline" 
                className="ml-4 text-xs bg-white border-gray-200 px-3 py-1 font-normal"
              >
                Switch Status
              </Badge>
            </div>
          </header>

          <main className="flex-grow max-w-[1200px] w-full mx-auto px-0 sm:px-6 lg:px-8 py-4">
            {/* Main status card */}
            <Card className="mb-8 w-full border border-gray-200 shadow-sm rounded-xl overflow-hidden">
              <div className="bg-gradient-to-r from-gray-50 to-white border-b p-6">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
                  <div>
                    <div className="flex items-center mb-2">
                      <Badge variant={statusDetails.badgeVariant as any} className="mr-2">
                        {statusDetails.badgeText}
                      </Badge>
                      {switchStatus.switchDate && (
                        <span className="text-sm text-gray-500 flex items-center">
                          <CalendarDays className="w-3.5 h-3.5 mr-1 inline" aria-hidden="true" />
                          {formatDate(switchStatus.switchDate)}
                        </span>
                      )}
                    </div>
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">
                      Energy Switch to {switchStatus.supplier}
                    </h1>
                    <p className="text-gray-600 text-sm">
                      Reference: <span className="font-medium">{switchStatus.referenceNumber}</span>
                    </p>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-start sm:items-center">
                    <Button 
                      variant="outline" 
                      size="sm"
                      asChild
                      className="rounded-full text-xs hover:bg-gray-100"
                    >
                      <Link href="https://meetgeorge.co.uk/" target="_blank">
                        <Home className="w-3.5 h-3.5 mr-1.5" aria-hidden="true" />
                        Website
                      </Link>
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      asChild
                      className="rounded-full text-xs hover:bg-gray-100"
                    >
                      <Link href="mailto:<EMAIL>" target="_blank">
                        <Mail className="w-3.5 h-3.5 mr-1.5" aria-hidden="true" />
                        Email Support
                      </Link>
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      asChild
                      className="rounded-full text-xs hover:bg-gray-100"
                    >
                      <Link href="tel:+447492819305">
                        <Phone className="w-3.5 h-3.5 mr-1.5" aria-hidden="true" />
                        Call Support
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  {/* Progress visualization */}
                  <div className="md:col-span-3">
                    <h2 className="text-lg font-semibold mb-4">Switch Progress</h2>
                    
                    {/* Timeline with progress indicators */}
                    <div className="flex justify-between items-center mb-4 relative">
                      {/* Line connecting stages */}
                      <div className="absolute h-[3px] bg-gray-200 top-4 left-4 right-4 -z-10"></div>
                      
                      {stages.map((stage, index) => (
                        <div key={stage} className="flex flex-col items-center relative z-10">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex flex-col items-center group">
                                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                    index < switchStatus.currentStage - 1 ? 
                                      'bg-green-100 ring-4 ring-green-50' : 
                                    index === switchStatus.currentStage - 1 ? 
                                      switchStatus.isRejected ? 
                                        'bg-red-100 ring-4 ring-red-50' : 
                                        'bg-green-100 ring-4 ring-green-50' : 
                                      'bg-gray-100 ring-4 ring-gray-50'
                                  } transition-all duration-300 group-hover:shadow-md`}>
                                    {index < switchStatus.currentStage - 1 ? (
                                      <CheckCircle className="w-6 h-6 text-green-500" />
                                    ) : index === switchStatus.currentStage - 1 ? (
                                      switchStatus.isRejected ? (
                                        <XCircle className="w-6 h-6 text-red-500" />
                                      ) : (
                                        <CheckCircle className="w-6 h-6 text-green-500" />
                                      )
                                    ) : (
                                      <Circle className="w-6 h-6 text-gray-300" />
                                    )}
                                  </div>
                                  <span className="text-sm mt-2 text-center font-medium whitespace-nowrap text-gray-700">
                                    {switchStatus.isRejected && index === stages.length - 1 ? "Rejected" : stage}
                                  </span>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{switchStatus.isRejected && index === stages.length - 1 ? 
                                  "This switch request has been rejected" : 
                                  index < switchStatus.currentStage ? 
                                    "Completed" : 
                                    "Pending"
                                }</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      ))}
                    </div>
                    
                    {/* Progress bar */}
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className={`h-full ${statusDetails.progressBarClass}`}
                        style={{ width: `${progressPercentage}%`, transition: "width 1s ease-in-out" }}
                        role="progressbar" 
                        aria-valuenow={progressPercentage} 
                        aria-valuemin={0} 
                        aria-valuemax={100}
                      ></div>
                    </div>
                  </div>
                </div>
                
                <Separator className="my-6" />

                {/* Current status section */}
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <h2 className="text-lg font-semibold mb-4 flex items-center">
                      {statusDetails.icon}
                      Current Status: {statusDetails.title}
                    </h2>
                    
                    <div className={`p-6 rounded-lg border mb-4 ${statusDetails.className}`}>
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                        <div className="flex-shrink-0">
                          {statusDetails.headerIcon}
                        </div>
                        <div>
                          <p className="text-gray-700 font-medium mb-2">
                            {statusDetails.message}
                            {switchStatus.currentStage === 4 && !switchStatus.isRejected && switchStatus.switchDate && (
                              <span className="font-medium"> Your switch was completed on {formatDate(switchStatus.switchDate)}.</span>
                            )}
                          </p>
                          <p className="text-sm text-gray-600">
                            {switchStatus.isRejected 
                              ? "Our team is available to help you resolve any issues with your switch request."
                              : switchStatus.currentStage === 4
                                ? "You should receive a final bill from your previous supplier and welcome information from your new supplier."
                                : "We'll keep you updated via email as your switch progresses."}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                        <h3 className="font-semibold text-lg mb-3 flex items-center">
                          <Info className="w-5 h-5 text-blue-500 mr-2" aria-hidden="true" />
                          Next Steps:
                        </h3>
                        <ul className="list-disc list-outside ml-5 text-gray-600 space-y-2">
                          {switchStatus.isRejected ? (
                            <>
                              <li>Please contact our support team for more information about why your switch was rejected.</li>
                              <li>We can help you explore other options or resolve any issues that led to the rejection.</li>
                              <li>Don't worry, your current energy supply will continue uninterrupted.</li>
                            </>
                          ) : (
                            <>
                              <li>Keep an eye on your email for updates from your new supplier.</li>
                              <li>Ensure you submit final meter readings when prompted.</li>
                              <li>Your energy supply will not be interrupted during this process.</li>
                            </>
                          )}
                        </ul>
                      </div>
                      
                      <div className="flex flex-col">
                        {switchStatus.isRejected ? (
                          <div className="bg-red-50 p-6 rounded-lg border border-red-100 h-full">
                            <h4 className="font-medium text-lg mb-3">Common reasons for rejection include:</h4>
                            <ul className="list-disc list-outside ml-5 text-gray-600 space-y-2">
                              <li>Incorrect personal or address information</li>
                              <li>Outstanding debt with current supplier</li>
                              <li>Technical issues with your meter</li>
                              <li>Current fixed-term contract with exit fees</li>
                            </ul>
                          </div>
                        ) : (
                          <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 h-full">
                            <h4 className="font-medium text-lg mb-3 flex items-center">
                              <ArrowUpRight className="w-5 h-5 text-gray-500 mr-2" aria-hidden="true" />
                              Helpful Resources
                            </h4>
                            <ul className="space-y-3">
                              <li>
                                <Link 
                                  href="https://www.ofgem.gov.uk/information-consumers/energy-advice-households/switching-energy-tariff-or-supplier" 
                                  target="_blank"
                                  className="text-blue-600 hover:underline flex items-center"
                                >
                                  <span>Official switching guide</span>
                                  <ArrowUpRight className="w-3 h-3 ml-1" aria-hidden="true" />
                                </Link>
                              </li>
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </main>
          
          <Footer />
        </div>
      </div>
    </div>
  )
}
