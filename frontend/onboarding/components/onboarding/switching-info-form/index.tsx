"use client"

import React, { useState, useId, useMemo } from 'react'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft } from 'lucide-react'
import { AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { Deal, EnergySwitch } from '@/types/types'
import { useToast } from "@/components/ui/use-toast"
import { Header } from '../header'
import { Footer } from '../footer'
import { useSession } from 'next-auth/react'
import { useConfirmEnergySwitch, useEnergySwitchStatus } from '@/app/hooks/useEnergySwitchData'
import { useFormValidation } from '@/lib/hooks/useFormValidation'

// Import our extracted components
import PersonalInfoTab from './tabs/PersonalInfoTab'
import SecurityInfoTab from './tabs/SecurityInfoTab'
import PaymentInfoTab from './tabs/PaymentInfoTab'
import ComparisonCards from './components/ComparisonCards'
import TariffLightbox from './components/TariffLightbox'

interface Props {
  energySwitch: EnergySwitch
  switchTo: string;
}

const tabContentVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -10 }
};

export default function SwitchingInfoForm({ energySwitch, switchTo }: Props) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("personal")
  const animationKey = useId()
  const [isLightboxOpen, setIsLightboxOpen] = useState(false)
  const newTariff = useMemo(() => 
    energySwitch.available_tariffs.find(deal => deal.id === switchTo) || energySwitch.available_tariffs[0], 
    [energySwitch.available_tariffs, switchTo]
  )
  const [isLoading, setIsLoading] = useState(false)
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null)
  
  // Get the SWR status data and mutate function
  const { status, mutate } = useEnergySwitchStatus(energySwitch.id)
  
  // Add the useConfirmEnergySwitch hook
  const { trigger: confirmSwitch, isMutating } = useConfirmEnergySwitch(energySwitch.id)

  // Initialize form state with data from the energy switch
  const initialFormData = {
    firstName: energySwitch.switch_user.first_name,
    lastName: energySwitch.switch_user.last_name,
    address: energySwitch.address.full_address,
    livedThreeYears: 'no',
    email: energySwitch.switch_user.email,
    phoneNumber: energySwitch.switch_user.phone_number || '',
    dateOfBirth: null,
    requiresSupport: 'no',
    accountHolder: '',
    sortCode: '',
    accountNumber: '',
    isBillingAddressSame: 'yes',
    billingAddress: '',
    paymentMethodPreference: '',
    switchPreference: '',
  };

  // Use our custom form validation hook
  const { 
    formData, 
    errors, 
    handleInputChange, 
    handleRadioChange, 
    handleDateChange,
    handleSelectChange,
    validateForm
  } = useFormValidation(initialFormData);

  const openLightbox = (deal: Deal) => {
    setIsLightboxOpen(true)
    setSelectedDeal(deal)
  }

  const closeLightbox = () => {
    setIsLightboxOpen(false)
  }

  // Tab change handler with validation
  const handleTabChange = (value: string) => {
    // Check if moving backward
    const isMovingBackward = 
      (activeTab === "security" && value === "personal") ||
      (activeTab === "payment" && (value === "security" || value === "personal"));
  
    if (isMovingBackward || validateForm(activeTab)) {
      setActiveTab(value);
      // Only call handleButtonClick when moving forward
      if (
        (activeTab === "personal" && value === "security") ||
        (activeTab === "security" && value === "payment")
      ) {
        handleButtonClick();
      }
    } else {
      // If form validation fails, don't change the tab
      toast({
        title: "Form Validation Error",
        description: "Please fill in all required fields correctly before proceeding.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  // Function to handle button click
  const handleButtonClick = async () => {
    if (validateForm(activeTab)) {
      switch (activeTab) {
        case "personal":
          setActiveTab("security")
          break
        case "security":
          setActiveTab("payment")
          break
        case "payment":
          setIsLoading(true);
          try {
            const result = await confirmSwitch({ 
              switchTo, 
              formData 
            });
            
            // Navigate to completion page
            router.push(`/onboarding/energy-switches/${energySwitch.id}/switch-completion?switch_to=${switchTo}`)
          } catch (error) {
            console.error("Error confirming switch:", error);
            
            // Show error toast
            toast({
              title: "Error",
              description: "Failed to confirm switch. Please try again.",
              variant: "destructive",
              duration: 2000,
            })
          } finally {
            setIsLoading(false);
          }
          break
      }
    } else {
      toast({
        title: "Form Validation Error",
        description: "Please fill in all required fields correctly before proceeding.",
        variant: "destructive",
        duration: 2000,
      });
    }
  }

  // Function to handle previous button click
  const handlePreviousClick = () => {
    switch (activeTab) {
      case "security":
        setActiveTab("personal")
        break
      case "payment":
        setActiveTab("security")
        break
    }
  }

  const handleBackToResults = () => {
    router.back()
  }

  return (
    <div className="font-sans">
      <div className="bg-white pb-0">
        <div className="max-w-[1200px] mx-auto px-4 py-8">
          <Header progressPercentage={75} />

          <div className="mb-8">
            <Button
              variant="outline"
              className="flex items-center text-[#fe6232] border-[#fe6232] hover:bg-[#fe6232]/10 transition-colors"
              onClick={handleBackToResults}
              aria-label="Return to energy tariffs selection"
            >
              <ChevronLeft className="w-5 h-5 mr-2" aria-hidden="true" />
              Back to Energy Tariffs
            </Button>
          </div>

          {/* Comparison cards showing current and new tariff */}
          <ComparisonCards 
            currentSupplier={energySwitch.current_supplier}
            newTariff={newTariff}
          />

          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full" id="switching-form-tabs">
            <TabsList className="grid w-full grid-cols-3 p-1 bg-gray-100 rounded-lg">
              {['personal', 'security', 'payment'].map((tab) => (
                <TabsTrigger
                  key={tab}
                  value={tab}
                  className={`text-xs sm:text-sm md:text-base py-1 px-1 sm:px-2 md:px-4 rounded-md transition-all duration-200 ${
                    activeTab === tab
                      ? 'bg-white text-[#fe6232] shadow-sm'
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <span className="block sm:hidden">
                    {tab === 'personal' && 'Personal'}
                    {tab === 'security' && 'Security'}
                    {tab === 'payment' && 'Payment'}
                  </span>
                  <span className="hidden sm:block">
                    {tab === 'personal' && 'Personal Information'}
                    {tab === 'security' && 'Security Information'}
                    {tab === 'payment' && 'Payment Information'}
                  </span>
                </TabsTrigger>
              ))}
            </TabsList>
            
            <div className="mt-6 relative">
              <AnimatePresence mode="wait">
                {activeTab === "personal" && (
                  <PersonalInfoTab
                    formData={formData}
                    errors={errors}
                    handleInputChange={handleInputChange}
                    handleRadioChange={handleRadioChange}
                    handleButtonClick={handleButtonClick}
                    tabContentVariants={tabContentVariants}
                    animationKey={animationKey}
                  />
                )}
                
                {activeTab === "security" && (
                  <SecurityInfoTab
                    formData={formData}
                    errors={errors}
                    handleInputChange={handleInputChange}
                    handleRadioChange={handleRadioChange}
                    handleDateChange={handleDateChange}
                    handleButtonClick={handleButtonClick}
                    handlePreviousClick={handlePreviousClick}
                    tabContentVariants={tabContentVariants}
                    animationKey={animationKey}
                  />
                )}
                
                {activeTab === "payment" && (
                  <PaymentInfoTab
                    formData={formData}
                    errors={errors}
                    newTariff={newTariff}
                    isLoading={isLoading}
                    handleInputChange={handleInputChange}
                    handleRadioChange={handleRadioChange}
                    handleSelectChange={handleSelectChange}
                    handleButtonClick={handleButtonClick}
                    handlePreviousClick={handlePreviousClick}
                    tabContentVariants={tabContentVariants}
                    animationKey={animationKey}
                  />
                )}
              </AnimatePresence>
            </div>
          </Tabs>

          <Footer />
        </div>

        <AnimatePresence>
          {isLightboxOpen && selectedDeal && 
            <TariffLightbox deal={selectedDeal} onClose={closeLightbox} />
          }
        </AnimatePresence>
      </div>
    </div>
  )
}
