"use client"

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { HelpCircle, Loader2 } from 'lucide-react';
import { Deal } from '@/types/types';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormErrors, SwitchingFormData } from '@/lib/hooks/useFormValidation';
import { motion } from 'framer-motion';

interface PaymentInfoTabProps {
  formData: SwitchingFormData;
  errors: FormErrors;
  newTariff: Deal;
  isLoading: boolean;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleRadioChange: (field: string) => (value: string) => void;
  handleSelectChange: (field: string) => (value: string) => void;
  handleButtonClick: () => void;
  handlePreviousClick: () => void;
  tabContentVariants: any;
  animationKey: string;
}

export default function PaymentInfoTab({
  formData,
  errors,
  newTariff,
  isLoading,
  handleInputChange,
  handleRadioChange,
  handleSelectChange,
  handleButtonClick,
  handlePreviousClick,
  tabContentVariants,
  animationKey
}: PaymentInfoTabProps) {
  const [psrTab, setPsrTab] = useState("why");

  const inputClassName = (fieldName: keyof FormErrors) =>
    `w-full ${errors[fieldName] ? 'border-[#fe6232]' : ''}`;

  return (
    <motion.div
      key={`payment-${animationKey}`}
      variants={tabContentVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <Card className="rounded-xl shadow-lg">
        <CardHeader className="pb-4">
          {/* <h3 className="text-2xl font-semibold text-gray-800">Payment Information</h3> */}
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Direct Debit Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-700">Direct Debit Details</h4>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-800">
                Your estimated monthly Direct Debit payment to {newTariff.supplier_name} will be <span className="font-semibold">
                £{(newTariff.fuel_types || []).reduce((total, fuelType) => total + (newTariff.estimated_cost?.[fuelType]?.monthly || 0), 0).toFixed(2)}
                </span>.
              </p>
            </div>
            <p className="text-sm text-gray-600">
              By providing your bank account information, you confirm that you have the authority to establish a Direct Debit for this account.
            </p>
          </div>

          {/* Bank Account Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-700">Bank Account Information</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="accountHolder" className="text-sm font-medium text-gray-700 mb-1 block">Full name of account holder</Label>
                <Input 
                  id="accountHolder" 
                  placeholder="Enter Full Name" 
                  className={inputClassName('accountHolder')}
                  value={formData.accountHolder}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Label htmlFor="sortCode" className="text-sm font-medium text-gray-700 mb-1 block">Sort Code</Label>
                <Input 
                  id="sortCode" 
                  placeholder="e.g., 123456" 
                  className={inputClassName('sortCode')}
                  value={formData.sortCode}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="accountNumber" className="text-sm font-medium text-gray-700 mb-1 block">Account Number</Label>
              <Input 
                id="accountNumber" 
                placeholder="Enter 8-digit account number" 
                className={inputClassName('accountNumber')}
                value={formData.accountNumber}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {/* Payment Method Preference */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-700">Payment Method Preference</h4>
            <div>
              <Label htmlFor="paymentMethodPreference" className="text-sm font-medium text-gray-700 mb-1 block">
                Select your preferred payment method
              </Label>
              <Select
                value={formData.paymentMethodPreference}
                onValueChange={handleSelectChange('paymentMethodPreference')}
              >
                <SelectTrigger 
                  id="paymentMethodPreference"
                  className={`w-full ${errors.paymentMethodPreference ? 'border-[#fe6232]' : ''}`}
                >
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  {newTariff.payment_methods.map((method) => (
                    <SelectItem key={method} value={method}>
                      {method}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Direct Debit Instructions */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-700">Direct Debit Instructions</h4>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full text-left h-auto py-3 px-4 whitespace-normal">
                  <span className="block sm:hidden">View full instructions</span>
                  <span className="hidden sm:block">View full instructions for your bank or building society</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[800px] w-11/12">
                <div className="flex justify-between items-start">
                  <DialogHeader>
                    <DialogTitle>Direct Debit Instructions</DialogTitle>
                    <DialogDescription>
                      Please read the following information carefully.
                    </DialogDescription>
                  </DialogHeader>
                </div>
                <Tabs defaultValue="company" className="mt-4">
                  <TabsList className="grid w-full grid-cols-3 gap-1 h-auto">
                    <TabsTrigger value="company" className="text-xs px-2 py-1 h-auto">
                      <span className="block sm:hidden">Company</span>
                      <span className="hidden sm:block">Company Name</span>
                    </TabsTrigger>
                    <TabsTrigger value="instructions" className="text-xs px-2 py-1 h-auto">
                      <span className="block sm:hidden">Info</span>
                      <span className="hidden sm:block">Instructions</span>
                    </TabsTrigger>
                    <TabsTrigger value="guarantee" className="text-xs px-2 py-1 h-auto">
                      <span className="block sm:hidden">Guarantee</span>
                      <span className="hidden sm:block">Direct Debit Guarantee</span>
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="company" className="mt-4 text-sm text-gray-700">
                    <p>The company name that will appear on your bank statement is {newTariff.supplier_name}. {newTariff.supplier_name} will lodge the instruction within 10 working days, or if not will inform you of when it is lodged and will send confirmation by post.</p>
                  </TabsContent>
                  <TabsContent value="instructions" className="mt-4 text-sm text-gray-700">
                    <p>Please pay {newTariff.supplier_name} Direct Debits from the account detailed in this Instruction subject to the safeguard assured by the Direct Debit Guarantee. I understand that this instruction may remain with {newTariff.supplier_name} and, if so, details will be passed electronically to my bank or building society.</p>
                    <p className="mt-2">Date: September 25th, 2024</p>
                    <p className="mt-2 font-medium">Banks and building societies may not accept Direct Debits for some types of accounts.</p>
                  </TabsContent>
                  <TabsContent value="guarantee" className="mt-4 text-sm text-gray-700">
                    <ul className="list-disc pl-5 space-y-2">
                      <li>The Guarantee is offered by all banks and building societies that accept instructions to pay Direct Debits.</li>
                      <li>If there are any changes to the amount, date or frequency of your Direct Debit, {newTariff.supplier_name} will notify you ten working days in advance of your account being debited or as otherwise agreed. If you request {newTariff.supplier_name} to collect a payment, confirmation of the amount and date will be given to you at the time of the request.</li>
                      <li>If an error is made in the payment of your Direct Debit, by {newTariff.supplier_name} or your bank or building society, you are entitled to a full and immediate refund of the amount paid from your bank or building society.</li>
                      <li>If you receive a refund you are not entitled to, you must pay it back when {newTariff.supplier_name} asks you to.</li>
                      <li>You can cancel a Direct Debit at any time by simply contacting your bank or building society. Written confirmation may be required. Please also notify {newTariff.supplier_name}.</li>
                    </ul>
                  </TabsContent>
                </Tabs>
              </DialogContent>
            </Dialog>
          </div>

          {/* Billing Address */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-700">Billing Address</h4>
            <div>
              <Label className="text-sm font-medium text-gray-700 mb-2 block">Is your billing address the same as your supply address?</Label>
              <RadioGroup 
                value={formData.isBillingAddressSame} 
                onValueChange={handleRadioChange('isBillingAddressSame')} 
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="billing-yes" />
                  <Label htmlFor="billing-yes" className="text-sm">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="billing-no" />
                  <Label htmlFor="billing-no" className="text-sm">No</Label>
                </div>
              </RadioGroup>
            </div>
            {formData.isBillingAddressSame === "no" && (
              <div className="mt-4">
                <Label htmlFor="billingAddress" className="text-sm font-medium text-gray-700 mb-1 block">Billing Address</Label>
                <Input 
                  id="billingAddress" 
                  placeholder="Enter your billing address" 
                  className={inputClassName('billingAddress')}
                  value={formData.billingAddress}
                  onChange={handleInputChange}
                />
              </div>
            )}
          </div>

          {/* Terms and Conditions */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-2">
              <h4 className="text-lg font-medium text-gray-700">Energy Switch Guarantee (ESG)?</h4>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-0 h-auto">
                    <HelpCircle className="h-4 w-4 text-black" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[800px] w-11/12">
                  <DialogHeader>
                    <DialogTitle>Energy Switch Guarantee (ESG)</DialogTitle>
                    <DialogDescription>
                    </DialogDescription>
                  </DialogHeader>
                  <Tabs value={psrTab} onValueChange={setPsrTab} className="mt-4">
                    <TabsList className="grid w-full grid-cols-3 sm:grid-cols-3">
                      <TabsTrigger value="why">Why</TabsTrigger>
                      <TabsTrigger value="what">What</TabsTrigger>
                      <TabsTrigger value="who">Who</TabsTrigger>
                    </TabsList>
                    <TabsContent value="why" className="mt-4 text-sm text-gray-700">
                      <p>Choosing the right energy tariff is more crucial than ever. Whether you're seeking a better deal or improved customer service, the Energy Switch Guarantee ensures a confident switching experience. Remember, you can often switch tariffs without changing suppliers to find a plan that suits your needs</p>
                      <p>The Guarantee pushes the energy sector to exceed regulatory requirements, aiming to:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-2">
                        <li>Raise switching standards continuously</li>
                        <li>Encourage companies to go above and beyond their obligations</li>
                        <li>Keep consumers at the forefront of industry practices</li>
                      </ul>
                      <br />
                      <p>This approach not only simplifies the switching process but also empowers you to make informed decisions about your energy provider and plan.</p>
                    </TabsContent>
                    <TabsContent value="what" className="mt-4 text-sm text-gray-700">
                      <p>The Energy Switch Guarantee makes choosing or changing your energy tariff Simple, Speedy, and Safe. Our members are committed to helping customers across Britain find the right tariff and switch if needed.</p>
                      <br />
                      <p>Suppliers who sign up to the Guarantee commit to these key standards:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-2">
                        <li>Quick Switches: Change to a new tariff within 5 working days, whether you're switching suppliers or just changing plans with your current provider</li>
                        <li>Hassle-Free Process: Your new supplier handles the entire transfer. You only need to provide additional information if requested</li>
                        <li>Prompt Problem-Solving: In rare cases of issues, your new supplier will work quickly to resolve them</li>
                      </ul>
                    </TabsContent>
                    <TabsContent value="who" className="mt-4 text-sm text-gray-700">
                      <p>The Energy Switch Guarantee applies to all British households switching tariffs or suppliers among participating companies. Membership in this program is voluntary, with each supplier making a commitment to elevate their service standards. These members pledge to adhere not only to the letter of the Guarantee but also to its spirit, aiming to provide an enhanced switching process.</p>
                      <br />
                      <p>It's important to note that while many suppliers participate, not all energy companies in Britain are part of this initiative.</p>
                      <br />
                      <p>This Guarantee represents a voluntary effort by its members to go beyond basic requirements and offer customers a more reliable and efficient switching process</p>
                    </TabsContent>
                  </Tabs>
                  <div className="mt-4">
                    <a href="https://www.energy-uk.org.uk/wp-content/uploads/2023/03/Energy-Switch-Guarantee_A4-Customer-Update-2022.pdf" target="_blank" rel="noopener noreferrer">
                      <Button size="sm" className="bg-[#fe6232] hover:bg-[#fe6232]/90 text-white font-medium">
                        Full Text of the Energy Switch Guarantee
                      </Button>
                    </a>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            <div className="space-y-4">
              <p className="text-sm font-medium text-gray-700">
                Would you prefer your switch to proceed as follows?
              </p>
              <ol className="list-decimal pl-5 text-sm text-gray-600">
                <li>Your switch happening within the next 5 days.</li>
                <li>If you cancel during the 14-day cooling-off period, you'll pay the new supplier for any energy used.</li>
              </ol>
              <RadioGroup 
                value={formData.switchPreference} 
                onValueChange={handleRadioChange('switchPreference')} 
                className="space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="switch-yes" />
                  <Label htmlFor="switch-yes" className="text-sm">Yes, proceed within the next 5-days.</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="switch-no" />
                  <Label htmlFor="switch-no" className="text-sm">No, I'd prefer to switch after the 14-day cooling-off period.</Label>
                </div>
              </RadioGroup>
            </div>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-2">
              <p className="text-sm font-medium text-gray-700">
                By confirming my switch, I understand that I am:
              </p>
              <ul className="list-disc pl-5 text-sm text-gray-600">
                <li>Entering into a new contract with {newTariff.supplier_name}.</li>
                <li>Accepting {newTariff.supplier_name}'s Terms and Conditions.</li>
                <li>Agreeing to pay for my energy as outlined in these terms.</li>
              </ul>
            </div>
            <p className="text-sm text-gray-600">
              {newTariff.supplier_name} may conduct a soft credit check, which won't affect your credit score.
            </p>
          </div>
        </CardContent>
      </Card>
      <div className="mt-8 flex justify-end">
        <Button 
          size="lg" 
          variant="outline"
          className="px-8 py-2 text-md text-gray-600 border-gray-300 hover:bg-gray-100 mr-4"
          onClick={handlePreviousClick}
        >
          Previous
        </Button>
        <Button 
          size="lg" 
          className="px-8 py-2 text-md bg-[#fe6232] hover:bg-[#fe6232]/90 text-white"
          onClick={handleButtonClick}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            'Confirm My Switch'
          )}
        </Button>
      </div>
    </motion.div>
  );
}