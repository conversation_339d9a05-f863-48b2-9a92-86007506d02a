"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { HelpCircle, CalendarIcon } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { format, subYears, setYear, startOfYear } from "date-fns";
import { FormErrors, SwitchingFormData } from '@/lib/hooks/useFormValidation';
import { motion } from 'framer-motion';

interface SecurityInfoTabProps {
  formData: SwitchingFormData;
  errors: FormErrors;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleRadioChange: (field: string) => (value: string) => void;
  handleDateChange: (date: Date | undefined) => void;
  handleButtonClick: () => void;
  handlePreviousClick: () => void;
  tabContentVariants: any;
  animationKey: string;
}

export default function SecurityInfoTab({
  formData,
  errors,
  handleInputChange,
  handleRadioChange,
  handleDateChange,
  handleButtonClick,
  handlePreviousClick,
  tabContentVariants,
  animationKey
}: SecurityInfoTabProps) {
  const [psrTab, setPsrTab] = useState("purpose");
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const [calendarDate, setCalendarDate] = useState<Date | undefined>(formData.dateOfBirth || undefined);

  const maxDate = subYears(new Date(), 18);
  const minDate = subYears(new Date(), 100);
  const years = Array.from({ length: 83 }, (_, i) => maxDate.getFullYear() - i);

  const inputClassName = (fieldName: keyof FormErrors) =>
    `w-full ${errors[fieldName] ? 'border-[#fe6232]' : ''}`;

  const handleYearSelect = (year: string) => {
    const selectedYear = parseInt(year);
    const newDate = startOfYear(setYear(new Date(), selectedYear));
    setCalendarDate(newDate);
    handleDateChange(newDate);
  };

  return (
    <motion.div
      key={`security-${animationKey}`}
      variants={tabContentVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <Card className="rounded-xl shadow-lg">
        <CardHeader className="pb-4">
          {/* <h2 className="text-3xl font-light text-gray-800">Security Information</h2> */}
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-muted-foreground">
            Your new supplier needs some information for security purposes. These details will help them confirm your identity when you contact them, such as during phone inquiries.
          </p>
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email Address</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" className="h-4 w-4 p-0">
                    <HelpCircle className="h-4 w-4" />
                    <span className="sr-only">Email info</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <p className="text-sm text-muted-foreground">
                    This email address cannot be changed as it has been verified during onboarding.
                  </p>
                </PopoverContent>
              </Popover>
            </div>
            <Input 
              id="email"
              type="email" 
              value={formData.email}
              className={`w-full ${errors.email ? 'border-[#fe6232]' : ''} bg-gray-100`}
              readOnly
            />
          </div>
          <div>
            <Label htmlFor="phoneNumber" className="text-sm font-medium text-gray-700 mb-1 block">Phone Number</Label>
            <Input 
              id="phoneNumber" 
              type="tel" 
              placeholder="Enter your phone number" 
              className={`w-full ${errors.phoneNumber ? 'border-[#fe6232]' : ''}`}
              value={formData.phoneNumber}
              onChange={handleInputChange}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="dateOfBirth" className="text-sm font-medium text-gray-700">Date of Birth</Label>
            <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={datePickerOpen}
                  className={`w-full justify-between text-left font-normal ${
                    !formData.dateOfBirth && "text-muted-foreground"
                  } ${errors.dateOfBirth ? 'border-[#fe6232]' : ''}`}
                >
                  {formData.dateOfBirth ? format(formData.dateOfBirth, "PPP") : <span>Select date of birth</span>}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="flex flex-col space-y-2 p-2">
                  <Select
                    onValueChange={handleYearSelect}
                    value={calendarDate?.getFullYear().toString()}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a year" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="p-2">
                    <Calendar
                      mode="single"
                      selected={formData.dateOfBirth}
                      onSelect={(date) => {
                        handleDateChange(date);
                        setCalendarDate(date);
                      }}
                      disabled={(date) => date > maxDate || date < minDate}
                      initialFocus
                      month={calendarDate}
                      onMonthChange={setCalendarDate}
                      className="rounded-md border"
                      classNames={{
                        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                        month: "space-y-4",
                        caption: "flex justify-center pt-1 relative items-center",
                        caption_label: "text-sm font-medium",
                        nav: "space-x-1 flex items-center",
                        nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
                        nav_button_previous: "absolute left-1",
                        nav_button_next: "absolute right-1",
                        table: "w-full border-collapse space-y-1",
                        head_row: "flex",
                        head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
                        row: "flex w-full mt-2",
                        cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                        day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
                        day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                        day_today: "bg-accent text-accent-foreground",
                        day_outside: "text-muted-foreground opacity-50",
                        day_disabled: "text-muted-foreground opacity-50",
                        day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                        day_hidden: "invisible",
                      }}
                    />
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Label className="text-sm font-medium text-gray-700">Do you or anyone in your household require additional support?</Label>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-0 h-auto">
                    <HelpCircle className="h-4 w-4 text-black" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[800px] w-11/12">
                  <DialogHeader>
                    <DialogTitle>Priority Services Register (PSR)</DialogTitle>
                    <DialogDescription>
                      The Priority Services Register (PSR) is a free service offered by energy suppliers and network operators in the UK to provide extra support to vulnerable customers.
                    </DialogDescription>
                  </DialogHeader>
                  <Tabs value={psrTab} onValueChange={setPsrTab} className="mt-4">
                    <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
                      <TabsTrigger value="purpose">Purpose & Eligibility</TabsTrigger>
                      <TabsTrigger value="services">Services Offered</TabsTrigger>
                      <TabsTrigger value="registration">Registration Process</TabsTrigger>
                      <TabsTrigger value="considerations">Important Considerations</TabsTrigger>
                    </TabsList>
                    <TabsContent value="purpose" className="mt-4 text-sm text-gray-700">
                      <p>The PSR is designed to ensure that vulnerable customers receive additional assistance and priority treatment from their energy providers. You may be eligible if you:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-2">
                        <li>Are of pensionable age</li>
                        <li>Have a disability or long-term medical condition</li>
                        <li>Have children under 5</li>
                        <li>Are recovering from an injury</li>
                        <li>Have special communication needs</li>
                        <li>Are in a vulnerable situation (e.g., mental health condition, recent bereavement)</li>
                      </ul>
                    </TabsContent>
                    <TabsContent value="services" className="mt-4 text-sm text-gray-700">
                      <p>Being on the PSR can provide various benefits, including:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-2">
                        <li>Priority support during power cuts</li>
                        <li>Advance notice of planned power interruptions</li>
                        <li>Password schemes for identification of genuine callers</li>
                        <li>Bills in accessible formats (e.g., large print, Braille)</li>
                        <li>Meter reading services</li>
                        <li>Nomination of a trusted person to handle communications</li>
                        <li>Help with prepayment meter access</li>
                      </ul>
                    </TabsContent>
                    <TabsContent value="registration" className="mt-4 text-sm text-gray-700">
                      <p>To join the PSR:</p>
                      <ol className="list-decimal pl-5 space-y-1 mt-2">
                        <li>Contact your energy supplier directly</li>
                        <li>Provide information about your needs and circumstances</li>
                        <li>Your supplier will assess your eligibility and add you to their register</li>
                      </ol>
                    </TabsContent>
                    <TabsContent value="considerations" className="mt-4 text-sm text-gray-700">
                      <ul className="list-disc pl-5 space-y-1">
                        <li>You need to register separately with each utility provider (gas, electricity, water)</li>
                        <li>If you switch energy suppliers, you'll need to register again with the new company</li>
                        <li>The PSR is separate from but complementary to government initiatives for energy bill support</li>
                      </ul>
                      <p className="mt-4">Remember, the PSR is a voluntary system designed to ensure vulnerable customers receive appropriate support from their energy providers. It's free to join and can provide valuable assistance in managing your energy needs.</p>
                    </TabsContent>
                  </Tabs>
                </DialogContent>
              </Dialog>
            </div>
            <RadioGroup 
              value={formData.requiresSupport} 
              onValueChange={handleRadioChange('requiresSupport')} 
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="support-yes" />
                <Label htmlFor="support-yes" className="text-sm">Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="support-no" />
                <Label htmlFor="support-no" className="text-sm">No</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>
      <div className="mt-8 flex justify-end">
        <Button 
          size="lg" 
          variant="outline"
          className="px-8 py-2 text-md text-gray-600 border-gray-300 hover:bg-gray-100 mr-4"
          onClick={handlePreviousClick}
        >
          Previous
        </Button>
        <Button 
          size="lg" 
          className="px-8 py-2 text-md bg-[#fe6232] hover:bg-[#fe6232]/90 text-white"
          onClick={handleButtonClick}
        >
          Next
        </Button>
      </div>
    </motion.div>
  );
}