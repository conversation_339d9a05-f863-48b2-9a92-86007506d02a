"use client"

import React from 'react';
import { Button } from "@/components/ui/button";
import { X } from 'lucide-react';
import Image from 'next/image';
import { Deal } from '@/types/types';
import { motion } from 'framer-motion';
import OfferDetails from '../offer_details';

interface TariffLightboxProps {
  deal: Deal;
  onClose: () => void;
}

export default function TariffLightbox({ deal, onClose }: TariffLightboxProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-medium text-gray-800">{deal.supplier_name} - {deal.tariff_name}</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-6 w-6" />
          </Button>
        </div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="relative w-16 h-16 rounded-full overflow-hidden">
              <Image 
                src={deal.logo}
                alt={deal.supplier_name}
                layout="fill"
                objectFit="cover"
              />
            </div>
            <div>
              <p className="text-lg font-medium text-gray-600">Estimated Annual Saving</p>
              { deal.estimated_saving && <p className="text-3xl font-medium text-green-600">£{deal.estimated_saving['total'].amount} ({deal.estimated_saving["total"].percentage}%)</p> }
            </div>
          </div>
        </div>
        <OfferDetails deal={deal} />
      </div>
    </motion.div>
  );
}