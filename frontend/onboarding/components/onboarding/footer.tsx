import Image from 'next/image'

export function Footer() {
  return (
    <footer className="mt-12 sm:mt-16 py-6 sm:py-8 border-t border-gray-200">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start space-y-6 lg:space-y-0">
        <div className="max-w-2xl">
          <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">About <PERSON>'s service</h3>
          <p className="text-xs sm:text-sm text-gray-600 mb-6 sm:mb-8 font-small leading-relaxed">
            <PERSON> offers a free service that lets you compare all available tariffs for your property, which you can switch to through our website. We gather details about you, your home, and your current energy supplier to help you find the most suitable tariff. Your energy offers are calculated using your estimated annual consumption and the unit rates from our partner energy suppliers. Meet <PERSON> receives a fee from energy suppliers for every successful switch.
          </p>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <p className="text-xs sm:text-sm text-gray-600 font-small">© 2024 Meet George Limited. All rights reserved.</p>
            <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-4">
              <a href="https://www.meetgeorge.co.uk/privacy" target="_blank" className="text-xs sm:text-sm font-small text-[#FE6232] hover:underline">Privacy Policy</a>
              <a href="https://www.meetgeorge.co.uk/terms" target="_blank" className="text-xs sm:text-sm font-small text-[#FE6232] hover:underline">Terms of Use</a>
            </div>
          </div>
        </div>
        <div className="flex-shrink-0 lg:ml-8 flex justify-center lg:justify-end">
          <Image
            src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/calculator-5GWNCXJeKBBD9JsCDDWVehRaO2XkCI.svg"
            alt="Calculator illustration"
            width={120}
            height={120}
            className="w-20 h-20 sm:w-24 sm:h-24 lg:w-[120px] lg:h-[120px]"
          />
        </div>
      </div>
    </footer>
  )
}
