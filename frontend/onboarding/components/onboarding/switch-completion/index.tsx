"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, CheckCircle, Mail, Hash, PhoneCall, Star, ChevronLeft, ArrowLeft } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { Header } from "../header"
import { Footer } from "../footer"
import { EnergySwitch } from "@/types/types"

export default function CompletedSwitch({ energySwitch, switchTo }: { energySwitch: EnergySwitch, switchTo: string }) {
  // In a real application, this would be dynamically generated or fetched
  const referenceNumber = energySwitch.reference_number;
  const newTariff = energySwitch.available_tariffs.find(deal => deal.id === switchTo)

  return (
    <div className="font-sans">
      <div className="bg-[#FAFAFA] pb-0">
        <div className="max-w-[1200px] mx-auto px-4 py-8">
          <Header progressPercentage={100} />

          <Card className="w-full max-w-[1200px] shadow-lg relative mb-12 bg-gradient-to-br from-green-50 to-green-100 border-2 border-green-200 pb-16">
            {/* Reference number positioned for mobile-first design */}
            <div className="absolute top-4 right-4 md:flex hidden bg-white bg-opacity-90 text-green-800 p-2 rounded-md text-sm font-medium items-center">
              <Hash className="w-4 h-4 mr-1 text-green-600" />
              Reference Number: <span className="font-bold ml-1">{referenceNumber}</span>
            </div>
            <CardHeader className="text-center">
              {/* Mobile reference number - above tick icon */}
              <div className="md:hidden bg-white bg-opacity-90 text-green-800 p-2 rounded-md text-sm font-medium flex items-center justify-center mb-4 mx-auto w-fit">
                <Hash className="w-4 h-4 mr-1 text-green-600" />
                Reference Number: <span className="font-bold ml-1">{referenceNumber}</span>
              </div>
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <CardTitle className="text-3xl font-bold text-green-800">Your Switch is Submitted!</CardTitle>
              <p className="text-green-700 mt-2">Your energy switch request has been sent to {newTariff.supplier_name}.</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <NextSteps newTariff={newTariff} />
              <EnergyPlanDetails newTariff={newTariff} />
              <ImportantInformation referenceNumber={referenceNumber} />
              <EmailConfirmation />
            </CardContent>
            <div className="absolute bottom-6 right-6">
              <Link
                href="https://meetgeorge.co.uk/"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Return to Home
              </Link>
            </div>
          </Card>
          
          <div className="mt-12">
            <CustomerSupportContact referenceNumber={referenceNumber} newTariff={newTariff} />
          </div>
          <Footer />
        </div>
      </div>
    </div>
  )
}

function NextSteps({ newTariff }: { newTariff }) {
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader>
        <CardTitle className="text-xl text-black flex items-center">
          <ArrowRight className="w-6 h-6 mr-2 text-green-600" />
          What happens next?
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="text-black list-disc list-inside space-y-2">
          <li>{newTariff.supplier_name} will process your switch request</li>
          <li>You'll receive a welcome email from them within 5 working days</li>
          <li>The switch process typically takes 14-21 days to complete</li>
          <li>Your energy supply will not be interrupted during the switch</li>
        </ul>
      </CardContent>
    </Card>
  )
}

function EnergyPlanDetails({ newTariff }: { newTariff }) {
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader>
        <CardTitle className="text-xl text-black flex items-center">
          <Star className="w-6 h-6 mr-2 text-green-600" />
          Your New Energy Plan Details
        </CardTitle>
      </CardHeader>
      <CardContent className="grid sm:grid-cols-2 gap-6">
        <div className="space-y-2 text-black">
          <p><span className="font-semibold">Supplier:</span> {newTariff.supplier_name}</p>
          <p><span className="font-semibold">Fuel Type:</span> {newTariff.fuel_type}</p>
          {/* <p><span className="font-semibold">Estimated Annual Cost:</span> £{newTariff.estimated_cost.annual}</p> */}
        </div>
        <div className="space-y-2 text-black">
          <p><span className="font-semibold">Tariff:</span> {newTariff.tariff_name}</p>
          <p><span className="font-semibold">Tariff Type:</span> {newTariff.tariff_type}</p>
          
          {
            newTariff.fuel_types.map(fuelType => (
              newTariff.estimated_cost[fuelType] && (
                <p key={`estimated-cost-${fuelType}`}>
                  <span className="font-semibold">
                    Monthly Direct Debit:
                  </span>
                  {' '}£{newTariff.fuel_types.reduce((total, fuelType) => total + (newTariff.estimated_cost[fuelType]?.monthly || 0), 0).toFixed(2)}
                </p>
              )
            ))
          }
        </div>
      </CardContent>
    </Card>
  )
}

function ImportantInformation({ referenceNumber }: { referenceNumber: string }) {
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader>
        <CardTitle className="text-xl text-black">Important Information</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="text-black list-disc list-inside space-y-2">
          <li>Keep paying your current supplier until the switch is complete</li>
          <li>Take a meter reading on your switch date (we'll email you a reminder)</li>
          <li>You have a 14-day cooling-off period if you change your mind</li>
          <li>If you have any questions, please quote your reference number: <span className="font-semibold">{referenceNumber}</span></li>
        </ul>
      </CardContent>
    </Card>
  )
}

function EmailConfirmation() {
  return (
    <div className="flex items-center justify-center text-black bg-white p-4 rounded-lg shadow-sm mb-4">
      <Mail className="w-6 h-6 mr-2 text-green-600" />
      <p>We've sent a confirmation email to your verified email address.</p>
    </div>
  )
}

function CustomerSupportContact({ referenceNumber, newTariff }: { referenceNumber: string, newTariff }) {
  return (
    <Card className="w-full bg-white border border-gray-200 shadow-md">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-gray-800 flex items-center">
          <PhoneCall className="w-5 h-5 mr-2 text-blue-600" />
          Customer Support Contact
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-semibold text-gray-700">Meet George</h4>
          <p className="text-gray-600">Email: <EMAIL></p>
        </div>
        {/* <div>
          <h4 className="font-semibold text-gray-700">Tomato Energy</h4>
          <p className="text-gray-600">Email: <EMAIL></p>
        </div> */}
        <p className="text-sm text-gray-500 italic">
          Please include your reference number ({referenceNumber}) in all communications.
        </p>
        <div className="mt-4 p-3 bg-[#F9FAFB] border border-gray-200 rounded-md">
          <p className="text-sm text-gray-800">
            <strong>Important:</strong> If you wish to cancel your switch during the 14-day cooling-off period, 
            please contact {newTariff.supplier_name} directly. They will handle your cancellation request.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}