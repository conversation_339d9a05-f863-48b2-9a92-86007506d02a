import React from 'react'
import { Deal } from '@/types/types'

interface OfferDetailsProps {
  deal: Deal
  fuelType: 'electricity' | 'gas'
}

const OfferDetails: React.FC<OfferDetailsProps> = ({ deal, fuelType }) => {
  const fuelData = deal[fuelType]

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">Tariff Details</h3>
          {'tariff_type' in fuelData ? <p><span className="font-medium">Tariff Type:</span> {fuelData.tariff_type as string}</p> : null}
          <p><span className="font-medium">Payment Method:</span> {fuelData.payment_method as string}</p>
          {'unit_rate' in fuelData ? <p><span className="font-medium">Unit Rate:</span> {fuelData.unit_rate as number}p per kWh</p> : null}
          <p><span className="font-medium">Standing Charge:</span> {fuelData.standing_charge as number}p per day</p>
        </div>
        <div>
          <h3 className="text-lg font-semibold mb-2">Estimated Costs</h3>
          <p><span className="font-medium">Monthly:</span> £{fuelData.estimated_monthly_cost}</p>
          <p><span className="font-medium">Yearly:</span> £{fuelData.estimated_yearly_cost}</p>
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-2">Additional Information</h3>
        {'exit_fees' in fuelData && (
          <p>
            <span className="font-medium">Exit Fees:</span> {fuelData.exit_fees ? `£${fuelData.exit_fees}` : 'None'}
          </p>
        )}
        {'discounts' in fuelData && (
          <p>
            <span className="font-medium">Discounts:</span> {fuelData.discounts as string || 'None'}
          </p>
        )}
        {'additional_benefits' in fuelData && (
          <p>
            <span className="font-medium">Additional Benefits:</span> {fuelData.additional_benefits as string || 'None'}
          </p>
        )}
      </div>
      {/* Add more sections as needed */}
    </div>
  )
}

export default OfferDetails
