import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

const tabContentVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -10 }
};

interface OfferDetailsTabsProps {
  deal: any;
  currentSupplier: any;
  TrustpilotStars: any;
}

const OfferDetailsTabs = ({ deal, currentSupplier, TrustpilotStars }: OfferDetailsTabsProps) => {
  const overlappingFuelTypes = deal.fuel_types.filter(fuel => currentSupplier.fuel_types.includes(fuel));

  const [lightboxFuelType, setLightboxFuelType] = useState(deal['gas'] ? 'gas' : 'electricity');
  const [compareFuelType, setCompareFuelType] = useState(
    overlappingFuelTypes.includes("gas") ? "gas" : "electricity"
  );
  const [activeTab, setActiveTab] = useState("offer-details");

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const currentSupplierTariff = lightboxFuelType === "electricity"
    ? currentSupplier['electricity']
    : currentSupplier['gas'];

  const compareSupplierTariff = compareFuelType === "electricity"
    ? currentSupplier['electricity']
    : currentSupplier['gas'];

  const hasOverlappingFuelTypes = overlappingFuelTypes.length > 0;

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange}>
      <TabsList className={`grid w-full ${hasOverlappingFuelTypes ? 'grid-cols-3' : 'grid-cols-2'}`}>
        <TabsTrigger value="offer-details" className="text-sm font-light">Offer Details</TabsTrigger>
        {hasOverlappingFuelTypes && (
          <TabsTrigger value="compare" className="text-sm font-light">Compare</TabsTrigger>
        )}
        <TabsTrigger value="supplier" className="text-sm font-light">Energy Supplier</TabsTrigger>
      </TabsList>
      <div className="h-[70vh] mt-6 relative">
        <AnimatePresence mode="wait">
          {activeTab === "offer-details" && (
            <motion.div
              key="offer-details"
              variants={tabContentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <div className="flex justify-start space-x-4 mb-4">
                {deal['gas'] && (
                  <Button 
                    variant={lightboxFuelType === "gas" ? "default" : "outline"} 
                    onClick={() => setLightboxFuelType("gas")}
                  >
                    Gas
                  </Button>
                )}
                {deal['electricity'] && (
                  <Button 
                    variant={lightboxFuelType === "electricity" ? "default" : "outline"} 
                    onClick={() => setLightboxFuelType("electricity")}
                  >
                    Electricity
                  </Button>
                )}
              </div>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Supplier</TableCell>
                    <TableCell className="break-words font-light">{deal.supplier_name}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Name</TableCell>
                    <TableCell className="break-words font-light">{deal.tariff_name}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Type</TableCell>
                    <TableCell className="break-words font-light">{deal.tariff_type}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Payment Method</TableCell>
                    <TableCell className="break-words font-light">{deal.payment_methods.join(', ')}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">{lightboxFuelType === "electricity" ? "Electricity" : "Gas"} Unit Rate</TableCell>
                    <TableCell className="break-words font-light">{deal[lightboxFuelType].unit_rate} p/kWh</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">{lightboxFuelType === "electricity" ? "Electricity" : "Gas"} Standing Charge</TableCell>
                    <TableCell className="break-words font-light">{deal[lightboxFuelType].standing_charge} p/day</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Ends On</TableCell>
                    <TableCell className="break-words font-light">{deal.tariff_ends}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Price Guaranteed Until</TableCell>
                    <TableCell className="break-words font-light">{deal.price_guaranteed}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>Exit Fees</TooltipTrigger>
                          <TooltipContent>
                            <p className="font-light">If you cancel this tariff before the end date</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="break-words font-light">£{deal.exit_fee}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Discounts and Additional Charges</TableCell>
                    <TableCell className="break-words font-light">{deal.discounts}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Additional Products or Services</TableCell>
                    <TableCell className="break-words font-light">{deal.additional_services}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </motion.div>
          )}
          {activeTab === "compare" && (
            <motion.div
              key="compare"
              variants={tabContentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600 p-0">
                      <div className="flex justify-start space-x-4 mb-4">
                        {overlappingFuelTypes.includes("gas") && (
                          <Button 
                            variant={compareFuelType === "gas" ? "default" : "outline"} 
                            onClick={() => setCompareFuelType("gas")}
                          >
                            Gas
                          </Button>
                        )}
                        {overlappingFuelTypes.includes("electricity") && (
                          <Button 
                            variant={compareFuelType === "electricity" ? "default" : "outline"} 
                            onClick={() => setCompareFuelType("electricity")}
                          >
                            Electricity
                          </Button>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-bold text-gray-600 text-right p-0">Current Supplier</TableCell>
                    <TableCell className="font-bold text-gray-600 text-right p-0">{deal.supplier_name}</TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Name</TableCell>
                    <TableCell className="text-right break-words font-light">{currentSupplier.tariff_name}</TableCell>
                    <TableCell className="text-right break-words font-light">{deal.tariff_name}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Type</TableCell>
                    <TableCell className="text-right break-words font-light">{currentSupplier.tariff_type}</TableCell>
                    <TableCell className="text-right break-words font-light">{deal.tariff_type}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="w-1/3 font-bold text-gray-600">Payment Method</TableCell>
                    <TableCell className="w-1/3 text-right break-words font-light">{currentSupplier.payment_method}</TableCell>
                    <TableCell className="w-1/3 text-right break-words font-light">{deal.payment_methods.join(', ')}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">{compareFuelType === "electricity" ? "Electricity" : "Gas"} Unit Rate</TableCell>
                    <TableCell className="text-right break-words font-light">{compareSupplierTariff.unit_rate} p/kWh</TableCell>
                    <TableCell className="text-right break-words font-light">{deal[compareFuelType]?.unit_rate} p/kWh</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">{compareFuelType === "electricity" ? "Electricity" : "Gas"} Standing Charge</TableCell>
                    <TableCell className="text-right break-words font-light">{compareSupplierTariff.standing_charge} p/day</TableCell>
                    <TableCell className="text-right break-words font-light">{deal[compareFuelType]?.standing_charge} p/day</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Ends On</TableCell>
                    <TableCell className="text-right break-words font-light">{currentSupplier.tariff_ends}</TableCell>
                    <TableCell className="text-right break-words font-light">{deal.tariff_ends}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Price Guaranteed Until</TableCell>
                    <TableCell className="text-right break-words font-light">{currentSupplier.price_guaranteed}</TableCell>
                    <TableCell className="text-right break-words font-light">{deal.price_guaranteed}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Exit Fees</TableCell>
                    <TableCell className="text-right break-words font-light">£{currentSupplier.exit_fee}</TableCell>
                    <TableCell className="text-right break-words font-light">£{deal.exit_fee}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Discounts</TableCell>
                    <TableCell className="text-right break-words font-light">{currentSupplier.discounts}</TableCell>
                    <TableCell className="text-right break-words font-light">{deal.discounts}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Additional Services</TableCell>
                    <TableCell className="text-right break-words font-light">{currentSupplier.additional_services}</TableCell>
                    <TableCell className="text-right break-words font-light">{deal.additional_services}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Estimated Yearly Cost</TableCell>
                    <TableCell className="text-right break-words font-light">£{currentSupplier[compareFuelType]?.estimated_costs[0] || '-'}</TableCell>
                    <TableCell className="text-right break-words font-light">£{deal.estimated_cost[compareFuelType].yearly || '-'}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </motion.div>
          )}
          {activeTab === "supplier" && (
            <motion.div
              key="supplier"
            >
              <div className="space-y-4">
                <h3 className="text-xl font-light text-gray-800">{deal.supplier_name}</h3>
                <p className="font-light text-gray-600">{deal.about}</p>
                <div className="flex items-center space-x-2">
                  <span className="font-light text-gray-600">Trustpilot Rating:</span>
                  <TrustpilotStars rating={deal.trustpilot_rating} />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </Tabs>
  );
};

export default OfferDetailsTabs;
