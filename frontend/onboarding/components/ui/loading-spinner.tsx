interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
  centerOnScreen?: boolean;
}

export default function LoadingSpinner({ 
  size = 'medium', 
  className = '',
  centerOnScreen = false
}: LoadingSpinnerProps) {
  const sizeClasses = {
    small: 'h-4 w-4 border-2',
    medium: 'h-8 w-8 border-4',
    large: 'h-12 w-12 border-4',
  };

  const sizeClass = sizeClasses[size];
  
  const spinner = (
    <div 
      className={`inline-block animate-spin rounded-full border-solid border-orange-500 border-r-transparent ${sizeClass} ${className}`} 
      role="status"
      style={{ borderTopColor: '#f97316', borderLeftColor: '#f97316', borderBottomColor: '#f97316' }}
    >
      <span className="sr-only">Loading...</span>
    </div>
  );

  if (centerOnScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white/80 z-50">
        {spinner}
      </div>
    );
  }

  return spinner;
} 