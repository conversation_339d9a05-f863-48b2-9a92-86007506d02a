'use client';

/* eslint-disable */
import AdminNavbarLinks from './NavbarLinksAdmin';
import NavLink from '@/components/link/NavLink';
import { isWindowAvailable } from '@/utils/navigation';
import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { IoIosHelpCircleOutline } from 'react-icons/io';

export default function AdminNavbar(props: {
  brandText: string;
  onOpen: (...args: any[]) => any;
  showSidebar?: boolean;
  [x: string]: any;
}) {
  const [scrolled, setScrolled] = useState(false);
  const { showSidebar = true } = props; // Default to true

  useEffect(() => {
    isWindowAvailable() && window.addEventListener('scroll', changeNavbar);

    return () => {
      isWindowAvailable() && window.removeEventListener('scroll', changeNavbar);
    };
  });

  const { brandText, onOpen } = props;
  const changeNavbar = () => {
    if (isWindowAvailable() && window.scrollY > 1) {
      setScrolled(true);
    } else {
      setScrolled(false);
    }
  };

  return (
    <nav
      className={`fixed top-3 z-[0] flex flex-row items-center justify-between rounded-lg bg-white/30 py-2 backdrop-blur-xl transition-all md:top-4 md:p-2 xl:top-[20px] ${
        showSidebar
          ? 'right-3 w-[calc(100vw_-_6%)] md:right-[30px] md:w-[calc(100vw_-_8%)] lg:w-[calc(100vw_-_6%)] xl:w-[calc(100vw_-_365px)] 2xl:w-[calc(100vw_-_380px)]'
          : 'left-0 right-0 mx-auto w-[calc(100%_-_16px)] md:w-[calc(100%_-_32px)]'
      }`}
    >
      <div className="ml-[6px]">
        {showSidebar ? (
          <div className="h-6 md:mb-2 md:w-[224px] md:pt-1">
            <a
              className="hidden text-xs font-normal text-zinc-950 hover:underline md:inline"
              href=""
            >
              Pages
              <span className="mx-1 text-xs text-zinc-950 hover:text-zinc-950">
                {' '}
                /{' '}
              </span>
            </a>
            <NavLink
              className="text-xs font-normal capitalize text-zinc-950 hover:underline"
              href="#"
            >
              {brandText}
            </NavLink>
          </div>
        ) : null}
        <p className="text-md shrink capitalize text-zinc-950 md:text-3xl">
          <NavLink
            href="#"
            className="font-bold capitalize hover:text-zinc-950"
          >
            Meet George
          </NavLink>
        </p>
      </div>
      <div className={`min-w-max md:ml-auto md:w-[unset] ${showSidebar ? 'w-[154px]' : ''}`}>
      {showSidebar ? (
        <AdminNavbarLinks
          onOpen={onOpen}
          products={props.products}
          subscription={props.subscription}
        />) : (
          <div className="relative flex min-w-max max-w-max flex-grow items-center justify-around gap-1 rounded-lg md:px-2 md:py-2 md:pl-3 xl:gap-2">
            
            <Button
                variant="outline"
                className="flex h-9 min-w-9 cursor-pointer rounded-full border-zinc-200 p-0 text-xl text-zinc-950 md:min-h-10 md:min-w-10"
                onClick={(e) => {
                }}
              >
              <IoIosHelpCircleOutline className="h-6 w-6 stroke-2 text-zinc-950" />
            </Button>

            <a className="w-full" href="#">
              <Avatar className="h-9 min-w-9 md:min-h-10 md:min-w-10">
                <AvatarImage src={''} />
                <AvatarFallback className="font-bold">SA</AvatarFallback>
              </Avatar>
            </a>
          </div>
        )}
      </div>
    </nav>
  );
}
