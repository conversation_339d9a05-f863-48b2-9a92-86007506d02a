import { useState } from 'react';

// Define the form data structure
export interface SwitchingFormData {
  firstName: string;
  lastName: string;
  address: string;
  livedThreeYears: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: Date | null;
  requiresSupport: string;
  accountHolder: string;
  sortCode: string;
  accountNumber: string;
  isBillingAddressSame: string;
  billingAddress: string;
  paymentMethodPreference: string;
  switchPreference: string;
}

// Define the errors structure to match form fields
export interface FormErrors {
  firstName: boolean;
  lastName: boolean;
  address: boolean;
  email: boolean;
  phoneNumber: boolean;
  dateOfBirth: boolean;
  accountHolder: boolean;
  sortCode: boolean;
  accountNumber: boolean;
  billingAddress: boolean;
  paymentMethodPreference: boolean;
  switchPreference: boolean;
}

export function useFormValidation(initialFormData: SwitchingFormData) {
  const [formData, setFormData] = useState<SwitchingFormData>(initialFormData);
  const [errors, setErrors] = useState<FormErrors>({
    firstName: false,
    lastName: false,
    address: false,
    email: false,
    phoneNumber: false,
    dateOfBirth: false,
    accountHolder: false,
    sortCode: false,
    accountNumber: false,
    billingAddress: false,
    paymentMethodPreference: false,
    switchPreference: false,
  });

  // Handle input change for text fields
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
    setErrors(prev => ({ ...prev, [id]: false }));
  };

  // Handle radio button changes
  const handleRadioChange = (field: string) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, checked } = e.target;
    setFormData(prev => ({ ...prev, [id]: checked }));
    setErrors(prev => ({ ...prev, [id]: false }));
  };

  // Handle date changes
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({ ...prev, dateOfBirth: date }));
      setErrors(prev => ({ ...prev, dateOfBirth: false }));
    }
  };

  // Handle select changes
  const handleSelectChange = (field: string) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setErrors(prev => ({ ...prev, [field]: false }));
  };

  // Validate form based on active tab
  const validateForm = (activeTab: string) => {
    let newErrors: Partial<FormErrors> = {};

    // Common validation functions
    const isEmptyString = (value: string = '') => value.trim() === '';
    const isValidEmail = (email: string = '') => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    const isValidUKPhone = (phone: string = '') => /^(\+44|0)\d{10}$/.test(phone.replace(/\s+/g, ''));
    const isValidSortCode = (sortCode: string = '') => /^\d{6}$/.test(sortCode.replace(/[^0-9]/g, ''));
    const isValidAccountNumber = (accNum: string = '') => /^\d{8}$/.test(accNum.replace(/[^0-9]/g, ''));

    // Basic validation for all tabs
    const basicValidation = {
      firstName: isEmptyString(formData.firstName),
      lastName: isEmptyString(formData.lastName),
      address: isEmptyString(formData.address),
    };

    if (activeTab === "personal") {
      newErrors = basicValidation;
    } else if (activeTab === "security") {
      newErrors = {
        ...basicValidation,
        email: !isValidEmail(formData.email),
        phoneNumber: !isValidUKPhone(formData.phoneNumber),
        dateOfBirth: !formData.dateOfBirth,
      };
    } else if (activeTab === "payment") {
      newErrors = {
        ...basicValidation,
        email: !isValidEmail(formData.email),
        phoneNumber: !isValidUKPhone(formData.phoneNumber),
        dateOfBirth: !formData.dateOfBirth,
        accountHolder: isEmptyString(formData.accountHolder),
        sortCode: !isValidSortCode(formData.sortCode),
        accountNumber: !isValidAccountNumber(formData.accountNumber),
        billingAddress: formData.isBillingAddressSame === 'no' && isEmptyString(formData.billingAddress),
        paymentMethodPreference: isEmptyString(formData.paymentMethodPreference),
        switchPreference: isEmptyString(formData.switchPreference),
      };
    }

    setErrors(prev => ({ ...prev, ...newErrors }));
    return !Object.values(newErrors).some(Boolean);
  };

  return {
    formData,
    setFormData,
    errors,
    setErrors,
    handleInputChange,
    handleRadioChange,
    handleCheckboxChange,
    handleDateChange,
    handleSelectChange,
    validateForm
  };
}