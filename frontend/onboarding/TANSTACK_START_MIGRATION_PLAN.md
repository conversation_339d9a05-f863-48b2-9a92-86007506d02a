# TanStack Start Migration Plan

## Phase 1: Project Setup & Core Migration (Week 1-2)

### 1.1 Initialize TanStack Start Project
```bash
npm create @tanstack/start@latest onboarding-tanstack
cd onboarding-tanstack
npm install
```

### 1.2 Core Dependencies Migration
- Replace Next.js with TanStack Start
- Replace NextAuth with TanStack Start's built-in auth
- Keep Tailwind CSS, Shadcn/ui, and other UI libraries
- Replace Axios with TanStack Query's fetch utilities

### 1.3 Project Structure Setup
```
app/
├── routes/
│   ├── __root.tsx
│   ├── index.tsx
│   ├── onboarding/
│   │   ├── index.tsx
│   │   └── energy-switches/
│   │       ├── $id.tsx
│   │       └── $id.switch-completion.tsx
├── components/
├── lib/
├── styles/
└── api/
```

## Phase 2: Authentication System (Week 2-3)

### 2.1 Replace NextAuth with TanStack Start Auth
- Implement JWT token verification using TanStack Start's session management
- Create middleware for token validation
- Set up server-side authentication handlers

### 2.2 Token Flow Refactoring
- Replace `OnboardingLinkHandler` with TanStack Start route loaders
- Implement token extraction and validation in route middleware
- Use TanStack Start's built-in session management

### 2.3 Authentication Components
```typescript
// app/routes/onboarding/index.tsx
import { createFileRoute, redirect } from '@tanstack/react-router'
import { verifyOnboardingToken } from '../lib/auth'

export const Route = createFileRoute('/onboarding/')({
  beforeLoad: async ({ search }) => {
    const { token } = search
    if (!token) {
      throw redirect({ to: '/' })
    }
    
    const session = await verifyOnboardingToken(token)
    if (!session) {
      throw new Error('Invalid token')
    }
    
    return { session }
  },
  component: OnboardingPage
})
```

## Phase 3: Routing & Navigation (Week 3-4)

### 3.1 File-based Routing Migration
- Convert Next.js App Router to TanStack Start file-based routing
- Implement nested layouts
- Set up dynamic routes for energy switch IDs

### 3.2 Route Structure
```
routes/
├── __root.tsx                    # Root layout
├── index.tsx                     # Home page
├── onboarding/
│   ├── index.tsx                 # Token verification & redirect
│   └── energy-switches/
│       ├── $id.tsx               # Energy switch form
│       └── $id.switch-completion.tsx  # Completion page
```

### 3.3 Route Loaders & Actions
- Implement data loading with route loaders
- Set up form actions for switch confirmation
- Add error boundaries and loading states

## Phase 4: Data Fetching & API Integration (Week 4-5)

### 4.1 TanStack Query Integration
- Replace Axios calls with TanStack Query
- Set up query client configuration
- Implement server-side data fetching

### 4.2 API Routes Migration
```typescript
// app/api/energy-switch.ts
import { json } from '@tanstack/start'

export async function GET({ params }: { params: { id: string } }) {
  const energySwitch = await fetchEnergySwitch(params.id)
  return json(energySwitch)
}
```

### 4.3 Query Hooks
```typescript
// hooks/useEnergySwitch.ts
import { useQuery } from '@tanstack/react-query'

export function useEnergySwitch(id: string) {
  return useQuery({
    queryKey: ['energy-switch', id],
    queryFn: () => fetchEnergySwitch(id)
  })
}
```

## Phase 5: Component Migration (Week 5-6)

### 5.1 Form Components
- Migrate `SwitchingInfoForm` to use TanStack Form
- Update form validation with Zod
- Implement form actions with TanStack Start

### 5.2 UI Components
- Keep existing Shadcn/ui components
- Update any Next.js specific components (Image, Link)
- Migrate animations to work with new routing

### 5.3 Layout Components
- Convert Next.js layouts to TanStack Start layouts
- Update navigation components
- Implement error boundaries

## Phase 6: Build & Deployment (Week 6-7)

### 6.1 Build Configuration
- Set up Vite configuration
- Configure TypeScript settings
- Set up environment variables

### 6.2 Docker & Deployment
- Update Dockerfile for TanStack Start
- Modify docker-compose.yml
- Set up production build process

### 6.3 Testing & Validation
- Test authentication flow
- Validate form submissions
- Test error handling
- Performance testing

## Migration Benefits

### Performance Improvements
- Smaller bundle size (estimated 30-40% reduction)
- Faster build times with Vite
- Better tree-shaking and code splitting

### Developer Experience
- Full TypeScript integration
- Better type safety across the stack
- Simplified authentication
- Integrated data fetching

### Maintenance Benefits
- Reduced dependency complexity
- Single framework for full-stack development
- Better error handling and debugging

## Risk Mitigation

### Technical Risks
1. **Learning Curve** - Allocate time for team training
2. **Ecosystem Maturity** - Evaluate critical dependencies
3. **Migration Complexity** - Phase the migration carefully

### Mitigation Strategies
1. **Parallel Development** - Keep Next.js version running during migration
2. **Feature Parity Testing** - Comprehensive testing at each phase
3. **Rollback Plan** - Maintain ability to revert if needed

## Timeline Summary
- **Week 1-2**: Project setup and core migration
- **Week 3-4**: Authentication and routing
- **Week 4-5**: Data fetching and API integration
- **Week 5-6**: Component migration
- **Week 6-7**: Build, deployment, and testing

**Total Estimated Time**: 6-7 weeks with 1-2 developers

## Success Metrics
- Bundle size reduction of 30-40%
- Build time improvement of 50%+
- Maintained feature parity
- Improved type safety coverage
- Reduced authentication complexity