@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --primary: 24.6 95% 53.1%;
  --primary-light: 24.6 95% 65%;
  --primary-foreground: 60 9.1% 97.8%;
  --secondary: 60 4.8% 95.9%;
  --secondary-foreground: 24 9.8% 10%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --accent: 60 4.8% 95.9%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --ring: 24.6 95% 53.1%;
  --radius: 0.5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
}

/* Dark mode removed - application now uses light mode only */

body {
  font-family: 'Inter', sans-serif;
}

html {
  scroll-behavior: smooth;
  font-family: 'Inter', sans-serif;
  color-scheme: unset !important;
}

option {
  color: black;
}

p {
  letter-spacing: 0px;
}

img {
  pointer-events: none;
}
::-moz-selection {
  /* Code for Firefox */
  color: white;
  background: #09090B;
}

::selection {
  color: white;
  background: #09090B;
}

input.defaultCheckbox {
  color: white;
}

input.defaultCheckbox::before {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.66662 10.115L12.7946 3.98633L13.7379 4.92899L6.66662 12.0003L2.42395 7.75766L3.36662 6.81499L6.66662 10.115Z' fill='white'/%3E%3C/svg%3E%0A");
  fill: currentColor;
  opacity: 0;
  height: 16px;
  width: 16px;
  top: 0;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0px);
}

input.defaultCheckbox::before path {
  fill: currentColor;
}

input:checked.defaultCheckbox::before {
  opacity: 1;
}
 