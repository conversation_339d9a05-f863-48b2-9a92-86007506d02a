import { SignJWT } from 'jose'

const JWT_SECRET = new TextEncoder().encode('your-secret-key-here')

async function createTestToken() {
  const payload = {
    userId: '123e4567-e89b-12d3-a456-426614174000',
    energySwitchId: '123e4567-e89b-12d3-a456-426614174001',
    email: '<EMAIL>',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
  }

  const token = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .sign(JWT_SECRET)

  console.log('Test token:')
  console.log(token)
  console.log('\nTest URL:')
  console.log(`http://localhost:3000/onboarding?token=${token}`)
}

createTestToken().catch(console.error)