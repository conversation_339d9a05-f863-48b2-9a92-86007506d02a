import express from 'express'
import cors from 'cors'

const app = express()
app.use(cors())
app.use(express.json())

// Mock data
const mockUser = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  email: '<EMAIL>',
  firstName: '<PERSON>',
  lastName: 'Doe',
}

const mockEnergySwitch = {
  id: '123e4567-e89b-12d3-a456-426614174001',
  userId: '123e4567-e89b-12d3-a456-426614174000',
  status: 'pending',
  currentSupplier: 'British Gas',
  newSupplier: 'Octopus Energy',
  estimatedSavings: 250,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

// API routes
app.get('/api/users/:id', (req, res) => {
  res.json(mockUser)
})

app.patch('/api/users/:id', (req, res) => {
  res.json({ ...mockUser, ...req.body })
})

app.get('/api/energy_switches/:id', (req, res) => {
  res.json(mockEnergySwitch)
})

app.patch('/api/energy_switches/:id', (req, res) => {
  res.json({ ...mockEnergySwitch, ...req.body })
})

app.listen(3003, () => {
  console.log('Mock API server running on http://localhost:3003')
})