{"name": "onboarding-tanstack", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3001", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-router": "^1.120.20", "@tanstack/react-router-devtools": "^1.120.20", "@tanstack/router-devtools": "^1.128.6", "@tanstack/router-plugin": "^1.120.20", "@vitejs/plugin-react": "^4.3.3", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.2.6", "jose": "^5.9.6", "lucide-react": "^0.460.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "tailwindcss-rtl": "^0.9.0", "zod": "^3.23.8"}, "devDependencies": {"@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-tabs": "^1.1.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.8.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^9.12.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.12", "jsdom": "^25.0.1", "postcss": "^8.4.47", "typescript": "^5.6.3", "vite": "^7.0.5", "vitest": "^3.2.4"}, "description": "A modern, type-safe onboarding application built with TanStack Start, featuring JWT authentication, multi-step forms, and seamless API integration.", "main": "app.config.timestamp_1752903043270.js", "keywords": [], "author": "", "engines": {"node": ">=22.17.0"}, "license": "ISC"}