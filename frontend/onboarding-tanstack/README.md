# Onboarding TanStack Start Application

A modern, type-safe onboarding application built with TanStack Start, featuring JWT authentication, multi-step forms, and seamless API integration.

## 🚀 Features

- **TanStack Start** - Full-stack React framework with file-based routing
- **Type Safety** - End-to-end TypeScript with Zod schema validation
- **JWT Authentication** - Secure token-based authentication flow
- **Multi-step Forms** - Progressive form completion with validation
- **Modern UI** - Tailwind CSS with Shadcn/ui components
- **Testing** - Comprehensive test suite with Vitest and Testing Library
- **Docker Support** - Containerized development and production environments

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Docker (for containerized development)

## 🛠️ Setup

### 1. Install Dependencies

```bash
cd frontend/onboarding-tanstack
npm install
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local

# Edit .env.local with your values
JWT_SECRET=your-secret-key-here
API_URL=http://localhost:3003
NEXT_PUBLIC_API_URL=http://localhost:3003
```

### 3. Development Server

```bash
# Start development server
npm run dev

# Application available at http://localhost:3000
```

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui

# Watch mode for development
npm test -- --watch
```

### Authentication Flow Testing

Generate a test JWT token for development:

```bash
# Create test token
node scripts/create-test-token.js

# Use the generated URL to test authentication
# Example: http://localhost:3000/onboarding?token=<jwt_token>
```

### API Integration Testing

Start the mock API server for development:

```bash
# Terminal 1: Start mock API
node scripts/mock-server.js

# Terminal 2: Start development server
npm run dev

# Test the complete flow with mock data
```

### End-to-End Testing Flow

1. **Start Services**:
   ```bash
   # Terminal 1: Mock API
   node scripts/mock-server.js
   
   # Terminal 2: Development server
   npm run dev
   ```

2. **Generate Test Token**:
   ```bash
   node scripts/create-test-token.js
   ```

3. **Test Complete Flow**:
   - Visit the generated URL with token
   - Complete all three form tabs:
     - Personal Information
     - Security Information  
     - Payment Information
   - Verify completion page displays correctly

## 🐳 Docker Development

### Using Docker Compose

```bash
# Build and start all services
docker-compose up --build

# Application available at http://localhost:3001
# API available at http://localhost:3003
```

### Manual Docker Build

```bash
# Build image
docker build -t onboarding-tanstack .

# Run container
docker run -p 3000:3000 onboarding-tanstack
```

## 🏗️ Production Build

```bash
# Build for production
npm run build

# Start production server
npm start

# Test production build
curl http://localhost:3000
```

## 📁 Project Structure

```
app/
├── routes/                    # File-based routing
│   ├── __root.tsx            # Root layout with providers
│   ├── index.tsx             # Home page (redirects)
│   └── onboarding/           # Onboarding flow
│       ├── index.tsx         # Token verification
│       └── energy-switches/  # Form pages
├── components/               # Reusable components
│   ├── forms/               # Form components
│   └── ui/                  # UI primitives
├── lib/                     # Utilities and configuration
│   ├── api.ts              # API client with Zod schemas
│   ├── auth.ts             # JWT token handling
│   └── utils.ts            # Utility functions
├── styles/                 # Global styles
└── test/                   # Test files
    ├── components/         # Component tests
    ├── lib/               # Library tests
    └── routes/            # Route tests
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run test:coverage` - Run tests with coverage
- `npm run test:ui` - Run tests with UI
- `npm run lint` - Run ESLint

## 🔐 Authentication Flow

1. User receives email with onboarding link containing JWT token
2. Token is verified on `/onboarding` page
3. User is redirected to energy switch form
4. Form data is submitted with authenticated API calls
5. Completion page confirms successful submission

## 🎨 UI Components

Built with Shadcn/ui and Tailwind CSS:

- **Forms** - Multi-step form with validation
- **Buttons** - Various button styles and states
- **Inputs** - Form inputs with error handling
- **Loading** - Spinner and skeleton components
- **Tabs** - Tab navigation for form steps
- **Progress** - Progress indicators

## 🔄 Migration from Next.js

This application was migrated from Next.js 14 to TanStack Start, providing:

- **30-40% smaller bundle size**
- **Faster build times** with Vite
- **Better type safety** across the stack
- **Simplified authentication** flow
- **Integrated data fetching** with TanStack Query

## 🚀 Deployment

### Environment Variables

Required for production:

```bash
JWT_SECRET=your-production-secret
API_URL=https://your-api-domain.com
NEXT_PUBLIC_API_URL=https://your-api-domain.com
```

### Docker Production

```bash
# Build production image
docker build -t onboarding-tanstack:latest .

# Run with environment variables
docker run -p 3000:3000 \
  -e JWT_SECRET=your-secret \
  -e API_URL=https://api.example.com \
  onboarding-tanstack:latest
```

## 🐛 Troubleshooting

### Common Issues

1. **JWT Token Invalid**
   - Check `JWT_SECRET` matches backend
   - Verify token hasn't expired
   - Ensure token format is correct

2. **API Connection Failed**
   - Verify `API_URL` is correct
   - Check CORS configuration
   - Ensure backend is running

3. **Build Errors**
   - Clear node_modules: `rm -rf node_modules && npm install`
   - Check TypeScript errors: `npm run type-check`
   - Verify all dependencies are installed

### Development Tips

- Use `npm run test:ui` for interactive testing
- Check browser console for client-side errors
- Use network tab to debug API calls
- Enable verbose logging in development

## 📚 Documentation

- [TanStack Start Docs](https://tanstack.com/start)
- [TanStack Router Docs](https://tanstack.com/router)
- [TanStack Query Docs](https://tanstack.com/query)
- [Tailwind CSS Docs](https://tailwindcss.com)
- [Shadcn/ui Docs](https://ui.shadcn.com)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.