import { useEffect, useState, useCallback, useRef } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useAuthenticationState } from '@/hooks/useAuthenticationState';
import { useAuth } from '@/context/AuthContext';

interface OnboardingLinkHandlerProps {
  children: React.ReactNode;
}

export function OnboardingLinkHandler({ children }: OnboardingLinkHandlerProps) {
  const [isProcessing, setIsProcessing] = useState(true);
  const { logout } = useAuth();
  const { 
    hasTokenBeenProcessed, 
    markTokenAsProcessed, 
    setProcessingError,
    clearProcessingState 
  } = useAuthenticationState();

  // Capture the initial URL hash immediately to prevent it from being cleared by other effects
  const initialHashRef = useRef<string | null>(null);
  
  // Initialize the hash ref immediately when component mounts (before any effects run)
  if (initialHashRef.current === null && typeof window !== 'undefined') {
    initialHashRef.current = window.location.hash;
    console.log('OnboardingLinkHandler: Captured initial hash:', initialHashRef.current);
  }

  // Memoize the processing function to prevent recreation
  const processOnboardingLink = useCallback(async () => {
    try {
      // Use the captured initial hash, not the current one (which might have been cleared)
      const hash = initialHashRef.current || '';
      console.log('OnboardingLinkHandler: Processing hash:', hash);
      
      const tokenMatch = hash.match(/token=([^&]*)/);
      const token = tokenMatch ? tokenMatch[1] : null;

      if (token) {
        console.log('OnboardingLinkHandler: Token found in captured hash:', token.substring(0, 20) + '...');
        
        // IMPORTANT: Check if this specific token has already been processed BEFORE clearing anything
        if (hasTokenBeenProcessed(token)) {
          console.log('OnboardingLinkHandler: Same token already processed - NO VERIFICATION NEEDED');
          
          // Clean the URL hash to prevent UI issues
          if (window.location.hash) {
            window.history.replaceState(
              null, 
              document.title, 
              window.location.pathname + window.location.search
            );
            console.log('OnboardingLinkHandler: URL hash cleaned');
          }
          
          // IMPORTANT: Clear only sessionStorage tokens to prevent verification
          // DON'T clear SecureTokenStorage (encrypted tokens) - we need those for auth persistence
          sessionStorage.removeItem('onboardingToken');
          sessionStorage.removeItem('authToken');
          console.log('OnboardingLinkHandler: Cleared sessionStorage tokens to prevent unnecessary verification');
          
          // User is already authenticated, let the existing auth state handle the redirect
          console.log('OnboardingLinkHandler: Same token - skipping verification, user already authenticated');
          setIsProcessing(false);
          return;
        }
        
        console.log('OnboardingLinkHandler: NEW TOKEN detected, clearing previous session');
        
        // FORCE LOGOUT: Clear any existing authentication state
        await logout();
        
        // Clear processing state to allow new token
        clearProcessingState();
        
        // Clear all storage to prevent conflicts
        const sessionItems = ['onboardingToken', 'authToken'];
        const localItems = ['authUser'];
        
        sessionItems.forEach(item => sessionStorage.removeItem(item));
        localItems.forEach(item => localStorage.removeItem(item));
        
        // Store new token for verification by OnboardingClient
        sessionStorage.setItem('onboardingToken', token);
        
        // Mark new token as processed using secure in-memory state
        markTokenAsProcessed(token);
        
        // Clean URL by removing hash fragment
        window.history.replaceState(
          null, 
          document.title, 
          window.location.pathname + window.location.search
        );
        
        console.log('OnboardingLinkHandler: New token stored, previous session invalidated');
      } else {
        console.log('OnboardingLinkHandler: No token found in captured hash:', hash);
        console.log('OnboardingLinkHandler: Current window.location.hash:', window.location.hash);
      }
    } catch (error) {
      console.error('OnboardingLinkHandler: Error processing onboarding link:', error);
      setProcessingError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      // Allow OnboardingClient to take over
      setIsProcessing(false);
    }
  }, [hasTokenBeenProcessed, markTokenAsProcessed, logout, clearProcessingState, setProcessingError]);

  useEffect(() => {
    // Only process on client-side and only once
    if (typeof window !== 'undefined') {
      processOnboardingLink();
    } else {
      setIsProcessing(false);
    }
  }, [processOnboardingLink]);

  // Show loading while processing token
  if (isProcessing) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-4 text-xl font-semibold text-gray-700">Processing your onboarding link...</h2>
          <p className="mt-2 text-gray-500">Please wait while we prepare your onboarding experience.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}