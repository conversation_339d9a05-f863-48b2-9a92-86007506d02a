import { Check } from 'lucide-react'

interface HeaderProps {
  progressPercentage: number; // New prop for progress percentage
}

export function Header({ progressPercentage }: HeaderProps) {
  // Ensure the percentage is between 0 and 100
  const clampedPercentage = Math.min(100, Math.max(0, progressPercentage));
  
  return (
    <header className="flex justify-between items-center mb-8">
      <img 
        src="/meet-george-logo.png"
        alt="Meet George Logo" 
        width={180} 
        height={60} 
        className="h-auto" 
        style={{
          height: 'auto'
        }}
      />
      <div className="flex items-center space-x-2">
        <div className="w-48 h-2 bg-gray-200 rounded-full flex">
          <div 
            className="h-full bg-green-500 rounded-full" 
            style={{ width: `${clampedPercentage}%` }}
          ></div>
        </div>
        <Check className="w-5 h-5 text-green-500" />
        <span className="text-sm text-gray-600 font-medium">Switched</span>
      </div>
    </header>
  )
}
