import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Deal } from '@/types/types'

const tabContentVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -10 }
};

const OfferDetails = ({ deal }: { deal: Deal }) => {
  const [lightboxFuelType, setLightboxFuelType] = useState("gas");

  return (
    <div className="h-[70vh] mt-6 relative">
        <div className="flex justify-start space-x-4 mb-4">
                <Button 
                  variant={lightboxFuelType === "gas" ? "default" : "outline"} 
                  onClick={() => setLightboxFuelType("gas")}
                >
                  Gas
                </Button>
                <Button 
                  variant={lightboxFuelType === "electricity" ? "default" : "outline"} 
                  onClick={() => setLightboxFuelType("electricity")}
                >
                  Electricity
                </Button>
              </div>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Supplier</TableCell>
                    <TableCell className="break-words font-light">{deal.supplier_name}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Name</TableCell>
                    <TableCell className="break-words font-light">{deal.tariff_name}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Type</TableCell>
                    <TableCell className="break-words font-light">{deal.tariff_type}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Payment Method</TableCell>
                    <TableCell className="break-words font-light">
                      {Array.isArray(deal.payment_methods) 
                        ? deal.payment_methods.join(', ')
                        : deal.payment_methods || 'N/A'}
                    </TableCell>
                  </TableRow>
                  {deal[lightboxFuelType] && (
                    <>
                      <TableRow>
                        <TableCell className="font-bold text-gray-600">{lightboxFuelType === "electricity" ? "Electricity" : "Gas"} Unit Rate</TableCell>
                        <TableCell className="break-words font-light">{deal[lightboxFuelType].unit_rate} p/kWh</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="font-bold text-gray-600">{lightboxFuelType === "electricity" ? "Electricity" : "Gas"} Standing Charge</TableCell>
                        <TableCell className="break-words font-light">{deal[lightboxFuelType].standing_charge} p/day</TableCell>
                      </TableRow>
                    </>
                  )}
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Tariff Ends On</TableCell>
                    <TableCell className="break-words font-light">{deal.tariff_ends}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Price Guaranteed Until</TableCell>
                    <TableCell className="break-words font-light">{deal.price_guaranteed}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>Exit Fees</TooltipTrigger>
                          <TooltipContent>
                            <p className="font-light">If you cancel this tariff before the end date</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="break-words font-light">£{deal.exit_fee}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Discounts and Additional Charges</TableCell>
                    <TableCell className="break-words font-light">{deal.discounts}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-bold text-gray-600">Additional Products or Services</TableCell>
                    <TableCell className="break-words font-light">{deal.additional_services}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
      </div>
  );
};

export default OfferDetails;
