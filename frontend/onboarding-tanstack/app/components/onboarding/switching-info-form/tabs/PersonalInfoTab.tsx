"use client"

import React from 'react';
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { HelpCircle } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { FormErrors, SwitchingFormData } from '@/lib/hooks/useFormValidation';
import { motion } from 'framer-motion';

interface PersonalInfoTabProps {
  formData: SwitchingFormData;
  errors: FormErrors;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleRadioChange: (field: string) => (value: string) => void;
  handleButtonClick: () => void;
  tabContentVariants: any;
  animationKey: string;
}

export default function PersonalInfoTab({
  formData,
  errors,
  handleInputChange,
  handleRadioChange,
  handleButtonClick,
  tabContentVariants,
  animationKey
}: PersonalInfoTabProps) {
  // Helper for generating input class names
  const inputClassName = (fieldName: keyof FormErrors) =>
    `w-full ${errors[fieldName] ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'} transition-colors`;

  return (
    <motion.div
      key={`personal-${animationKey}`}
      variants={tabContentVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <Card className="rounded-xl shadow-lg">
        <CardHeader className="pb-4">
          {/* <h2 className="text-3xl font-light text-gray-800">Personal Information</h2>*/}
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="firstName" className="text-sm font-medium text-gray-700 mb-1 block">First Name</Label>
              <Input
                id="firstName"
                placeholder="Enter your first name"
                className={inputClassName('firstName')}
                value={formData.firstName}
                onChange={handleInputChange}
              />
            </div>
            <div>
              <Label htmlFor="lastName" className="text-sm font-medium text-gray-700 mb-1 block">Surname</Label>
              <Input
                id="lastName"
                placeholder="Enter your surname"
                className={inputClassName('lastName')}
                value={formData.lastName}
                onChange={handleInputChange}
              />
            </div>
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <Label htmlFor="address" className="text-sm font-medium text-gray-700">Supply Address</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" className="h-4 w-4 p-0">
                    <HelpCircle className="h-4 w-4" />
                    <span className="sr-only">Home address info</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <p className="text-sm text-muted-foreground">
                    This is the address that the energy is supplied to. This address cannot be changed as it has been extracted from the bill you uploaded.
                  </p>
                </PopoverContent>
              </Popover>
            </div>
            <Input
              id="address"
              value={formData.address}
              className={`${inputClassName('address')} bg-gray-100`}
              readOnly
            />
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-700 mb-2 block">Have you lived at your current address for 3 or more years?</Label>
            <RadioGroup
              value={formData.livedThreeYears} 
              onValueChange={handleRadioChange('livedThreeYears')}
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="lived-yes" />
                <Label htmlFor="lived-yes" className="text-sm">Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="lived-no" />
                <Label htmlFor="lived-no" className="text-sm">No</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>
      <div className="mt-8 flex justify-end">
        <Button 
          size="lg" 
          className="px-8 py-2 text-md bg-[#fe6232] hover:bg-[#fe6232]/90 text-white transition-colors focus:ring-2 focus:ring-[#fe6232]/50 focus:outline-none"
          onClick={handleButtonClick}
          aria-label="Proceed to Security Information"
        >
          Next
        </Button>
      </div>
    </motion.div>
  );
}