import React from 'react';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight } from 'lucide-react';
import { Deal } from '@/types/types';

interface ComparisonCardsProps {
  currentSupplier: Deal;
  newTariff: Deal;
}

export default function ComparisonCards({
  currentSupplier,
  newTariff,
}: ComparisonCardsProps) {
  // Calculate estimated costs with proper error handling
  return (
    <div className="flex flex-col lg:flex-row items-stretch gap-8 mb-12">
      <Card className="w-full lg:w-[calc(50%-1rem)] rounded-xl overflow-hidden shadow-lg flex flex-col">
        <CardHeader className="bg-gradient-to-r from-orange-400 to-orange-600 text-white py-4 px-6">
          <h2 className="text-lg font-semibold">Your current energy supplier tariff</h2>
        </CardHeader>
        <CardContent className="p-6 flex-grow flex flex-col">
          <div className="flex items-center mb-6">
            {
              currentSupplier.logo && (
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mr-4 overflow-hidden flex-shrink-0">
                  <Image 
                    src={currentSupplier.logo}
                    alt={currentSupplier.supplier_name || 'Supplier logo'}
                    width={48}
                    height={48}
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      // Fallback for broken images
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        const fallback = document.createElement('div');
                        fallback.className = 'flex items-center justify-center w-full h-full bg-gray-100 text-gray-800 font-bold text-2xl';
                        fallback.textContent = currentSupplier.supplier_name?.charAt(0)?.toUpperCase() || 'S';
                        parent.appendChild(fallback);
                      }
                    }}
                  />
                </div>
              )
            }
            <div>
              <h3 className="text-xl font-semibold text-gray-800">{currentSupplier.supplier_name}</h3>
              <p className="text-md text-gray-600">{currentSupplier.tariff_type}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <p className="text-sm font-medium text-gray-500">Fuel Type</p>
              <p className="text-md font-semibold text-gray-800">{currentSupplier?.fuel_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Tariff Type</p>
              <p className="text-md font-semibold text-gray-800">{currentSupplier.tariff_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Monthly Direct Debit</p>
              <p className="text-md font-semibold text-gray-800">
                £{currentSupplier.total_estimated_costs?.total?.[1] || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Estimated Annual Cost</p>
              <p className="text-md font-semibold text-gray-800">
                £{currentSupplier.total_estimated_costs?.total?.[0] || 'N/A'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex items-center justify-center lg:w-8">
        <ArrowRight className="w-8 h-8 text-gray-400 transform lg:rotate-0 rotate-90" />
      </div>
      
      <Card className="w-full lg:w-[calc(50%-1rem)] rounded-xl overflow-hidden shadow-lg flex flex-col">
        <CardHeader className="bg-gradient-to-r from-blue-400 to-blue-600 text-white py-4 px-6">
          <h2 className="text-lg font-semibold">Switching to</h2>
        </CardHeader>
        <CardContent className="p-6 flex-grow flex flex-col">
          <div className="flex items-center mb-6">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mr-4 overflow-hidden flex-shrink-0">
              {
                newTariff.logo && (
                  <Image
                    src={newTariff.logo}
                    alt={newTariff.supplier_name || 'Supplier logo'}
                    width={48}
                    height={48}
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      // Fallback for broken images
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        const fallback = document.createElement('div');
                        fallback.className = 'flex items-center justify-center w-full h-full bg-gray-100 text-gray-800 font-bold text-2xl';
                        fallback.textContent = newTariff.supplier_name?.charAt(0)?.toUpperCase() || 'S';
                        parent.appendChild(fallback);
                      }
                    }}
                  />
                )
              }
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-800">{newTariff.supplier_name}</h3>
              <p className="text-md text-gray-600">{newTariff.tariff_name}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <p className="text-sm font-medium text-gray-500">Fuel Type</p>
              <p className="text-md font-semibold text-gray-800">{newTariff.fuel_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Tariff Type</p>
              <p className="text-md font-semibold text-gray-800">{newTariff.tariff_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Estimated Monthly Direct Debit</p>
              <p className="text-md font-semibold text-gray-800">
                £{(newTariff.fuel_types || []).reduce((total, fuelType) => total + (newTariff.estimated_cost?.[fuelType]?.monthly || 0), 0).toFixed(2)}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Estimated Annual Cost</p>
              <p className="text-md font-semibold text-gray-800">
                £{(newTariff.fuel_types || []).reduce((total, fuelType) => total + (newTariff.estimated_cost?.[fuelType]?.yearly || 0), 0).toFixed(2)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}