import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";

interface EnergyCardProps {
  deal: {
    supplier_name: string;
    tariff_name: string;
    fuel_types: string[];
    gas?: {
      unit_rate: number;
      standing_charge: number;
    };
    electricity?: {
      unit_rate: number;
      standing_charge: number;
    };
    estimated_cost: {
      yearly: number;
      monthly: number;
    };
    exit_fee: number;
    contract_length: string;
    estimated_saving: number;
  };
}

export function EnergyCard({ deal }: EnergyCardProps) {
  const [selectedFuelType, setSelectedFuelType] = useState<'gas' | 'electricity'>(
    deal.fuel_types.includes('gas') ? 'gas' : 'electricity'
  );

  const isGasAndElectricity = deal.fuel_types.includes('gas') && deal.fuel_types.includes('electricity');

  const getUnitRateLabel = () => {
    if (isGasAndElectricity) {
      return `${selectedFuelType.charAt(0).toUpperCase() + selectedFuelType.slice(1)} Unit Rate`;
    }
    return deal.fuel_types[0] === 'gas' ? 'Gas Unit Rate' : 'Electricity Unit Rate';
  };

  const getStandingChargeLabel = () => {
    if (isGasAndElectricity) {
      return `${selectedFuelType.charAt(0).toUpperCase() + selectedFuelType.slice(1)} Standing Charge`;
    }
    return deal.fuel_types[0] === 'gas' ? 'Gas Standing Charge' : 'Electricity Standing Charge';
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {deal.supplier_name}
          <span className="block text-xs text-muted-foreground">{deal.tariff_name}</span>
        </CardTitle>
        <Badge variant="secondary">{deal.contract_length}</Badge>
      </CardHeader>
      <CardContent>
        {isGasAndElectricity && (
          <div className="flex space-x-2 mb-4">
            <Button
              variant={selectedFuelType === 'gas' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFuelType('gas')}
            >
              Gas
            </Button>
            <Button
              variant={selectedFuelType === 'electricity' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFuelType('electricity')}
            >
              Electricity
            </Button>
          </div>
        )}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">{getUnitRateLabel()}</span>
            <span className="text-2xl font-bold">
              {deal[selectedFuelType]?.unit_rate.toFixed(2)} p/kWh
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">{getStandingChargeLabel()}</span>
            <span className="text-2xl font-bold">
              {deal[selectedFuelType]?.standing_charge.toFixed(2)} p/day
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">Early Exit Fee</span>
            <span className="text-2xl font-bold">£{deal.exit_fee}</span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">Estimated Monthly Cost</span>
            <span className="text-2xl font-bold">£{deal.estimated_cost.monthly.toFixed(2)}</span>
          </div>
        </div>
        <div className="mt-4 flex justify-between items-center">
          <div>
            <span className="text-sm font-medium text-muted-foreground">Estimated Yearly Cost</span>
            <span className="block text-2xl font-bold">£{deal.estimated_cost.yearly.toFixed(2)}</span>
          </div>
          <div className="text-right">
            <span className="text-sm font-medium text-muted-foreground">Estimated Saving</span>
            <span className="block text-2xl font-bold text-green-600">£{deal.estimated_saving.toFixed(2)}</span>
          </div>
        </div>
        <div className="mt-4 flex justify-between">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline">Tariff Details</Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>View full tariff details</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Button>Switch</Button>
        </div>
      </CardContent>
    </Card>
  );
}
