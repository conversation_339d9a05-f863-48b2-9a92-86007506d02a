import React from 'react';

interface ErrorMessageProps {
  title: string;
  message: string;
  showHomeLink?: boolean;
  actionText?: string;
  actionHref?: string;
}

export function ErrorMessage({ 
  title = "Something went wrong", 
  message = "We encountered an error processing your request.",
  showHomeLink = true,
  actionText = "Return to Homepage",
  actionHref = "/"
}: ErrorMessageProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] p-6 text-center">
      <div className="bg-red-50 border border-red-200 rounded-lg p-8 max-w-md mx-auto shadow-sm">
        <svg 
          className="w-16 h-16 text-red-500 mx-auto mb-4" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
          />
        </svg>
        
        <h2 className="text-xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">{message}</p>
        
        {showHomeLink && (
          <a 
            href={actionHref}
            className="inline-flex items-center justify-center px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {actionText}
          </a>
        )}
      </div>
    </div>
  );
} 