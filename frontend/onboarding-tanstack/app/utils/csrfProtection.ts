/**
 * CSRF Protection Utility
 * Generates and manages CSRF tokens for API requests
 */

class CSRFProtection {
  private static readonly CSRF_TOKEN_KEY = '__csrf_token__';
  private static readonly CSRF_EXPIRY_KEY = '__csrf_expiry__';
  private static csrfToken: string | null = null;

  /**
   * Generate a cryptographically secure random token
   */
  private static generateSecureToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Get or create a CSRF token
   */
  static getCSRFToken(): string {
    if (typeof window === 'undefined') {
      return this.generateSecureToken();
    }

    // Check if we have a cached token
    if (this.csrfToken) {
      return this.csrfToken;
    }

    // Check sessionStorage for existing token
    const storedToken = sessionStorage.getItem(this.CSRF_TOKEN_KEY);
    const storedExpiry = sessionStorage.getItem(this.CSRF_EXPIRY_KEY);

    if (storedToken && storedExpiry) {
      const expiry = parseInt(storedExpiry, 10);
      if (Date.now() < expiry) {
        this.csrfToken = storedToken;
        return storedToken;
      }
    }

    // Generate new token
    const newToken = this.generateSecureToken();
    const expiry = Date.now() + (30 * 60 * 1000); // 30 minutes

    sessionStorage.setItem(this.CSRF_TOKEN_KEY, newToken);
    sessionStorage.setItem(this.CSRF_EXPIRY_KEY, expiry.toString());
    
    this.csrfToken = newToken;
    console.log('CSRFProtection: New CSRF token generated');
    
    return newToken;
  }

  /**
   * Clear CSRF token
   */
  static clearCSRFToken(): void {
    if (typeof window === 'undefined') return;
    
    sessionStorage.removeItem(this.CSRF_TOKEN_KEY);
    sessionStorage.removeItem(this.CSRF_EXPIRY_KEY);
    this.csrfToken = null;
    
    console.log('CSRFProtection: CSRF token cleared');
  }

  /**
   * Validate CSRF token (for requests from backend)
   */
  static validateCSRFToken(token: string): boolean {
    const currentToken = this.getCSRFToken();
    return token === currentToken;
  }

  /**
   * Get headers with CSRF protection
   */
  static getSecureHeaders(additionalHeaders: Record<string, string> = {}): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'X-CSRF-Token': this.getCSRFToken(),
      'X-Requested-With': 'XMLHttpRequest', // Additional CSRF protection
      ...additionalHeaders,
    };
  }
}

export default CSRFProtection;