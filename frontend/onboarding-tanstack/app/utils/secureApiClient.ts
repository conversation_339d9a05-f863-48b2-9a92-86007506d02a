/**
 * Secure API Client
 * Combines token encryption, CSRF protection, and secure request handling
 */

import SecureTokenStorage from './secureStorage';
import CSRFProtection from './csrfProtection';

interface SecureRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: Record<string, string>;
  requireAuth?: boolean;
  skipCSRF?: boolean;
}

class SecureApiClient {
  private static readonly baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3003';

  /**
   * Make a secure API request with all protection measures
   */
  static async request<T = any>(
    endpoint: string, 
    options: SecureRequestOptions = {}
  ): Promise<T> {
    const {
      method = 'GET',
      body,
      headers = {},
      requireAuth = true,
      skipCSRF = false
    } = options;

    const url = `${this.baseURL}${endpoint}`;
    
    // Prepare secure headers
    const secureHeaders: Record<string, string> = {
      'Accept': 'application/json',
      ...headers,
    };

    // Add CSRF protection for state-changing operations
    if (!skipCSRF && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
      Object.assign(secureHeaders, CSRFProtection.getSecureHeaders());
    }

    // Add authentication if required
    if (requireAuth) {
      const token = await SecureTokenStorage.getToken();
      if (!token) {
        throw new Error('Authentication required: No valid token found');
      }
      secureHeaders['Authorization'] = `Bearer ${token}`;
    }

    // Prepare request configuration
    const requestConfig: RequestInit = {
      method,
      headers: secureHeaders,
      credentials: 'same-origin', // Important for CSRF protection
    };

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
      secureHeaders['Content-Type'] = 'application/json';
    }

    try {
      console.log(`SecureApiClient: Making ${method} request to ${endpoint}`);
      
      const response = await fetch(url, requestConfig);

      // Handle authentication errors
      if (response.status === 401) {
        console.warn('SecureApiClient: Authentication failed, clearing tokens');
        await SecureTokenStorage.clearToken();
        CSRFProtection.clearCSRFToken();
        throw new Error('Authentication failed: Invalid or expired token');
      }

      // Handle CSRF errors
      if (response.status === 403) {
        console.warn('SecureApiClient: CSRF validation failed, refreshing token');
        CSRFProtection.clearCSRFToken();
        throw new Error('CSRF validation failed: Request forbidden');
      }

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorText}`);
      }

      // Parse response
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      return await response.text() as T;
    } catch (error) {
      console.error('SecureApiClient: Request failed:', error);
      throw error;
    }
  }

  /**
   * Convenience methods for common HTTP operations
   */
  static async get<T = any>(endpoint: string, requireAuth = true): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', requireAuth, skipCSRF: true });
  }

  static async post<T = any>(endpoint: string, body: any, requireAuth = true): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body, requireAuth });
  }

  static async put<T = any>(endpoint: string, body: any, requireAuth = true): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body, requireAuth });
  }

  static async delete<T = any>(endpoint: string, requireAuth = true): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', requireAuth });
  }

  /**
   * Special method for token verification (no auth required)
   */
  static async verifyToken(token: string): Promise<any> {
    return this.request('/api/v1/onboarding/verify', {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` },
      requireAuth: false,
      skipCSRF: true,
    });
  }
}

export default SecureApiClient;