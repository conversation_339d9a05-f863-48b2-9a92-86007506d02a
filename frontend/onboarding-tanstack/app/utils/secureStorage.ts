/**
 * Secure Storage Utility
 * Provides encrypted token storage with security enhancements
 */

// Simple encryption/decryption using Web Crypto API
class SecureTokenStorage {
  private static readonly STORAGE_KEY = '__secure_auth_token__';
  private static readonly EXPIRY_KEY = '__token_expiry__';
  private static readonly IV_KEY = '__token_iv__';
  
  // Generate a key from a string (in production, this should be more sophisticated)
  private static async getEncryptionKey(): Promise<CryptoKey> {
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(import.meta.env.VITE_JWT_SECRET || 'fallback-secret-key'),
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );
    
    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: new TextEncoder().encode('onboarding-salt'),
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Securely store a token with encryption and expiration
   */
  static async setToken(token: string, expirationMinutes: number = 60): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      const key = await this.getEncryptionKey();
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      const encoded = new TextEncoder().encode(token);
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        key,
        encoded
      );

      const encryptedArray = new Uint8Array(encrypted);
      const encryptedBase64 = btoa(String.fromCharCode(...encryptedArray));
      const ivBase64 = btoa(String.fromCharCode(...iv));
      
      const expiry = Date.now() + (expirationMinutes * 60 * 1000);
      
      sessionStorage.setItem(this.STORAGE_KEY, encryptedBase64);
      sessionStorage.setItem(this.IV_KEY, ivBase64);
      sessionStorage.setItem(this.EXPIRY_KEY, expiry.toString());
      
      console.log('SecureStorage: Token stored securely with encryption');
    } catch (error) {
      console.error('SecureStorage: Failed to encrypt token:', error);
      // Fallback to regular storage (not recommended for production)
      sessionStorage.setItem(this.STORAGE_KEY, token);
    }
  }

  /**
   * Retrieve and decrypt the stored token
   */
  static async getToken(): Promise<string | null> {
    if (typeof window === 'undefined') return null;

    try {
      const encryptedToken = sessionStorage.getItem(this.STORAGE_KEY);
      const ivBase64 = sessionStorage.getItem(this.IV_KEY);
      const expiryStr = sessionStorage.getItem(this.EXPIRY_KEY);

      if (!encryptedToken || !ivBase64 || !expiryStr) {
        return null;
      }

      // Check if token has expired
      const expiry = parseInt(expiryStr, 10);
      if (Date.now() > expiry) {
        console.log('SecureStorage: Token expired, clearing storage');
        this.clearToken();
        return null;
      }

      const key = await this.getEncryptionKey();
      const iv = new Uint8Array(
        atob(ivBase64).split('').map(char => char.charCodeAt(0))
      );
      const encrypted = new Uint8Array(
        atob(encryptedToken).split('').map(char => char.charCodeAt(0))
      );

      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        key,
        encrypted
      );

      const token = new TextDecoder().decode(decrypted);
      return token;
    } catch (error) {
      console.error('SecureStorage: Failed to decrypt token:', error);
      // Try fallback to regular storage
      return sessionStorage.getItem(this.STORAGE_KEY);
    }
  }

  /**
   * Clear all stored tokens and related data
   */
  static clearToken(): void {
    if (typeof window === 'undefined') return;
    
    sessionStorage.removeItem(this.STORAGE_KEY);
    sessionStorage.removeItem(this.IV_KEY);
    sessionStorage.removeItem(this.EXPIRY_KEY);
    sessionStorage.removeItem('onboardingToken'); // Legacy cleanup
    
    console.log('SecureStorage: All tokens cleared');
  }

  /**
   * Check if a valid token exists
   */
  static async hasValidToken(): Promise<boolean> {
    const token = await this.getToken();
    return token !== null;
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiry(): number | null {
    if (typeof window === 'undefined') return null;
    
    const expiryStr = sessionStorage.getItem(this.EXPIRY_KEY);
    return expiryStr ? parseInt(expiryStr, 10) : null;
  }
}

export default SecureTokenStorage;