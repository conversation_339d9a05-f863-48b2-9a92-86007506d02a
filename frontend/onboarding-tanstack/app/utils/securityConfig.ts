/**
 * Security Configuration
 * Provides CSP and other security headers configuration
 */

export const SECURITY_CONFIG = {
  // Content Security Policy
  CSP: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for Vite in development
      "'unsafe-eval'", // Required for Vite in development
      'https://cdn.jsdelivr.net', // For any CDN scripts
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for CSS-in-JS and inline styles
      'https://fonts.googleapis.com',
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
      'data:', // For base64 encoded fonts
    ],
    'img-src': [
      "'self'",
      'data:', // For base64 images
      'https:', // Allow HTTPS images
    ],
    'connect-src': [
      "'self'",
      'http://localhost:3003', // API server
      'https://localhost:3003', // API server HTTPS
      'ws://localhost:*', // WebSocket for Vite HMR
      'wss://localhost:*', // Secure WebSocket
    ],
    'frame-ancestors': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'object-src': ["'none'"],
    'media-src': ["'self'"],
  },

  // Additional security headers
  HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  },
};

/**
 * Generate CSP header string
 */
export function generateCSPString(): string {
  const cspDirectives = Object.entries(SECURITY_CONFIG.CSP)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');
  
  return cspDirectives;
}

/**
 * Apply security headers to response (for server-side usage)
 */
export function applySecurityHeaders(response: Response): Response {
  const headers = new Headers(response.headers);
  
  // Apply CSP
  headers.set('Content-Security-Policy', generateCSPString());
  
  // Apply additional security headers
  Object.entries(SECURITY_CONFIG.HEADERS).forEach(([key, value]) => {
    headers.set(key, value);
  });
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}

/**
 * Client-side security initialization
 */
export function initializeClientSecurity(): void {
  // Add CSP via meta tag if not already present
  if (typeof document !== 'undefined' && !document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = generateCSPString();
    document.head.appendChild(meta);
    
    console.log('SecurityConfig: CSP meta tag added to document');
  }
  
  // Setup additional client-side security measures
  if (typeof window !== 'undefined') {
    // Disable eval in production
    if (import.meta.env.PROD) {
      try {
        window.eval = () => {
          throw new Error('eval() is disabled for security reasons');
        };
      } catch (e) {
        // eval might already be read-only
      }
    }
    
    // Log security events
    window.addEventListener('securitypolicyviolation', (e) => {
      console.warn('CSP Violation:', {
        directive: e.violatedDirective,
        blockedURI: e.blockedURI,
        lineNumber: e.lineNumber,
        sourceFile: e.sourceFile,
      });
    });
  }
}

export default SECURITY_CONFIG;