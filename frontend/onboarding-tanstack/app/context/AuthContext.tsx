import { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react';
import SecureTokenStorage from '@/utils/secureStorage';
import CSRFProtection from '@/utils/csrfProtection';
import { useAuthenticationState } from '@/hooks/useAuthenticationState';

interface User {
  id: string;
  energySwitchId: string;
  hasGas: boolean;
  hasElectricity: boolean;
  status: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  authLoading: boolean;
  isInitialized: boolean;
  setUser: (user: User | null) => void;
  login: (user: User, token: string) => Promise<void>;
  logout: () => void;
  getAuthToken: () => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication context provider with enhanced security
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [authLoading, setAuthLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const { clearProcessingState } = useAuthenticationState();

  // Initialize auth state from secure storage on mount
  useEffect(() => {
    const initializeAuth = async () => {
      setAuthLoading(true);
      try {
        const token = await SecureTokenStorage.getToken();
        const savedUser = typeof window !== 'undefined' ? localStorage.getItem('authUser') : null;
        
        console.log('AuthContext: Initialization check - token:', !!token, 'savedUser:', !!savedUser);
        
        if (token && savedUser) {
          try {
            const userData = JSON.parse(savedUser) as User;
            setUser(userData);
            console.log('AuthContext: Session restored from storage', userData);
          } catch (error) {
            console.error('AuthContext: Error parsing saved user data:', error);
            // Clear corrupted data
            if (typeof window !== 'undefined') {
              localStorage.removeItem('authUser');
            }
            SecureTokenStorage.clearToken();
          }
        } else {
          console.log('AuthContext: No session to restore - token:', !!token, 'savedUser:', !!savedUser);
        }
      } catch (error) {
        console.error('AuthContext: Error restoring session:', error);
      } finally {
        setIsInitialized(true);
        setAuthLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (userData: User, token: string) => {
    setAuthLoading(true);
    try {
      setUser(userData);
      
      // Store auth token securely with encryption
      await SecureTokenStorage.setToken(token, 60); // 60 minutes expiry
      
      // Store user data in localStorage for page refresh persistence
      if (typeof window !== 'undefined') {
        localStorage.setItem('authUser', JSON.stringify(userData));
      }
      
      console.log('AuthContext: User logged in securely', userData);
    } finally {
      setAuthLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    setUser(null);
    
    // Clear all auth-related tokens and CSRF tokens
    SecureTokenStorage.clearToken();
    CSRFProtection.clearCSRFToken();
    
    // Clear secure in-memory processing state
    clearProcessingState();
    
    // Clear stored user data and tokens
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authUser');
      sessionStorage.removeItem('onboardingToken');
    }
    
    console.log('AuthContext: User logged out, all tokens and secure state cleared');
  }, [clearProcessingState]);

  const getAuthToken = useCallback(async (): Promise<string | null> => {
    return await SecureTokenStorage.getToken();
  }, []);

  // Token expiration monitoring (moved inline to avoid circular dependency)
  useEffect(() => {
    if (!user) return;

    const checkTokenExpiration = async () => {
      try {
        const token = await SecureTokenStorage.getToken();
        const expiry = SecureTokenStorage.getTokenExpiry();

        if (!token || !expiry) {
          console.warn('AuthProvider: No token or expiry found, logging out');
          console.warn('AuthProvider: token:', !!token, 'expiry:', !!expiry);
          logout();
          return;
        }

        const now = Date.now();
        const timeUntilExpiry = expiry - now;
        const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60));

        // Token has expired
        if (timeUntilExpiry <= 0) {
          console.warn('AuthProvider: Token has expired, logging out');
          logout();
          return;
        }

        // Show warning if close to expiration (5 minutes)
        if (minutesUntilExpiry <= 5 && minutesUntilExpiry > 0) {
          console.warn(`AuthProvider: Token expires in ${minutesUntilExpiry} minutes`);
          
          // Dispatch custom event for UI components to handle
          window.dispatchEvent(new CustomEvent('token-expiration-warning', {
            detail: { minutesUntilExpiry, expiryTime: expiry }
          }));
        }

        console.log(`AuthProvider: Token valid for ${minutesUntilExpiry} more minutes`);
      } catch (error) {
        console.error('AuthProvider: Error checking token expiration:', error);
        logout();
      }
    };

    // Check immediately
    checkTokenExpiration();

    // Set up interval to check every minute
    const interval = setInterval(checkTokenExpiration, 60000);

    return () => clearInterval(interval);
  }, [user, logout]);

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    authLoading,
    isInitialized,
    setUser,
    login,
    logout,
    getAuthToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Hook to use the authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}