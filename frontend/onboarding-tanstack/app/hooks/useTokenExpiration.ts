/**
 * Standalone Token Expiration Utilities
 * Independent of AuthContext to avoid circular dependencies
 */

import { useEffect, useCallback } from 'react';
import SecureTokenStorage from '@/utils/secureStorage';

interface UseTokenExpirationOptions {
  warningMinutes?: number; // Minutes before expiration to show warning
  checkIntervalMs?: number; // How often to check expiration
  onExpired?: () => void; // Callback when token expires
  isEnabled?: boolean; // Whether monitoring is enabled
}

/**
 * Standalone token expiration monitoring (no AuthContext dependency)
 */
export function useTokenExpiration(options: UseTokenExpirationOptions = {}) {
  const { 
    warningMinutes = 5, 
    checkIntervalMs = 60000, 
    onExpired,
    isEnabled = true 
  } = options;

  const checkTokenExpiration = useCallback(async () => {
    if (!isEnabled) return;

    try {
      const token = await SecureTokenStorage.getToken();
      const expiry = SecureTokenStorage.getTokenExpiry();

      if (!token || !expiry) {
        console.warn('useTokenExpiration: No token or expiry found');
        onExpired?.();
        return;
      }

      const now = Date.now();
      const timeUntilExpiry = expiry - now;
      const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60));

      // Token has expired
      if (timeUntilExpiry <= 0) {
        console.warn('useTokenExpiration: Token has expired');
        onExpired?.();
        return;
      }

      // Show warning if close to expiration
      if (minutesUntilExpiry <= warningMinutes && minutesUntilExpiry > 0) {
        console.warn(`useTokenExpiration: Token expires in ${minutesUntilExpiry} minutes`);
        
        // Dispatch custom event for UI components to handle
        window.dispatchEvent(new CustomEvent('token-expiration-warning', {
          detail: { minutesUntilExpiry, expiryTime: expiry }
        }));
      }

      console.log(`useTokenExpiration: Token valid for ${minutesUntilExpiry} more minutes`);
    } catch (error) {
      console.error('useTokenExpiration: Error checking token expiration:', error);
      onExpired?.();
    }
  }, [isEnabled, onExpired, warningMinutes]);

  // Set up interval to check token expiration
  useEffect(() => {
    if (!isEnabled) return;

    // Check immediately
    checkTokenExpiration();

    // Set up interval
    const interval = setInterval(checkTokenExpiration, checkIntervalMs);

    return () => clearInterval(interval);
  }, [isEnabled, checkTokenExpiration, checkIntervalMs]);

  // Manual check function
  const checkNow = useCallback(() => {
    checkTokenExpiration();
  }, [checkTokenExpiration]);

  return {
    checkNow,
  };
}

/**
 * Hook to listen for token expiration warnings
 */
export function useTokenExpirationWarning(
  onWarning?: (minutesUntilExpiry: number) => void
) {
  useEffect(() => {
    const handleWarning = (event: CustomEvent) => {
      const { minutesUntilExpiry } = event.detail;
      onWarning?.(minutesUntilExpiry);
    };

    window.addEventListener('token-expiration-warning', handleWarning as EventListener);

    return () => {
      window.removeEventListener('token-expiration-warning', handleWarning as EventListener);
    };
  }, [onWarning]);
}

export default useTokenExpiration;