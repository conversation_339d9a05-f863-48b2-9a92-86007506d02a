/**
 * Authentication State Manager
 * Secure in-memory state management for authentication flow
 */

import { useState, useCallback, useRef } from 'react';

interface TokenProcessingState {
  processed: boolean;
  timestamp: number;
  token: string | null;
  error: string | null;
}

interface AuthenticationFlow {
  // Token processing state
  tokenProcessing: TokenProcessingState;
  
  // State management methods
  markTokenAsProcessed: (token: string) => void;
  hasTokenBeenProcessed: (currentToken?: string) => boolean;
  getProcessedToken: () => string | null;
  setProcessingError: (error: string) => void;
  clearProcessingState: () => void;
  
  // Timing utilities
  getProcessingAge: () => number;
  isProcessingStale: (maxAgeMs?: number) => boolean;
}

/**
 * Secure authentication state hook using in-memory storage
 */
export function useAuthenticationState(): AuthenticationFlow {
  // In-memory state (not accessible via browser dev tools)
  const [tokenProcessing, setTokenProcessing] = useState<TokenProcessingState>({
    processed: false,
    timestamp: 0,
    token: null,
    error: null,
  });

  // Additional security: use useRef for sensitive data that shouldn't trigger re-renders
  const secureState = useRef<{
    hashedToken: string | null;
    processedAt: number;
  }>({
    hashedToken: null,
    processedAt: 0,
  });

  /**
   * Mark token as processed with timestamp
   */
  const markTokenAsProcessed = useCallback((token: string) => {
    const now = Date.now();
    
    // Create a simple hash of the token for comparison (not for security)
    const hashedToken = btoa(token.substring(0, 10) + token.length);
    
    setTokenProcessing({
      processed: true,
      timestamp: now,
      token: null, // Don't store the actual token in state
      error: null,
    });

    // Store minimal data in secure ref
    secureState.current = {
      hashedToken,
      processedAt: now,
    };

    console.log('AuthState: Token marked as processed securely');
  }, []);

  /**
   * Check if a specific token has been processed in this session
   */
  const hasTokenBeenProcessed = useCallback((currentToken?: string): boolean => {
    const age = Date.now() - tokenProcessing.timestamp;
    const maxAge = 5 * 60 * 1000; // 5 minutes max age for processing state
    
    // If no current token provided, check general processing state
    if (!currentToken) {
      return tokenProcessing.processed && age < maxAge;
    }
    
    // If current token provided, check if it's the same token that was processed
    if (!tokenProcessing.processed || age >= maxAge) {
      return false;
    }
    
    const currentTokenHash = btoa(currentToken.substring(0, 10) + currentToken.length);
    return secureState.current.hashedToken === currentTokenHash;
  }, [tokenProcessing.processed, tokenProcessing.timestamp]);

  /**
   * Get the processed token (always returns null for security)
   */
  const getProcessedToken = useCallback((): string | null => {
    // For security, never return the actual token from state
    return null;
  }, []);

  /**
   * Set processing error
   */
  const setProcessingError = useCallback((error: string) => {
    setTokenProcessing(prev => ({
      ...prev,
      error,
      timestamp: Date.now(),
    }));
  }, []);

  /**
   * Clear all processing state
   */
  const clearProcessingState = useCallback(() => {
    setTokenProcessing({
      processed: false,
      timestamp: 0,
      token: null,
      error: null,
    });

    secureState.current = {
      hashedToken: null,
      processedAt: 0,
    };

    console.log('AuthState: Processing state cleared');
  }, []);

  /**
   * Get age of current processing state in milliseconds
   */
  const getProcessingAge = useCallback((): number => {
    return Date.now() - tokenProcessing.timestamp;
  }, [tokenProcessing.timestamp]);

  /**
   * Check if processing state is stale
   */
  const isProcessingStale = useCallback((maxAgeMs: number = 5 * 60 * 1000): boolean => {
    return getProcessingAge() > maxAgeMs;
  }, [getProcessingAge]);

  return {
    tokenProcessing,
    markTokenAsProcessed,
    hasTokenBeenProcessed,
    getProcessedToken,
    setProcessingError,
    clearProcessingState,
    getProcessingAge,
    isProcessingStale,
  };
}

export default useAuthenticationState;