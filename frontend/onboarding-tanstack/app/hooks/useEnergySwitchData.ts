import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { EnergySwitch, SwitchStatusType } from '@/types/types';
import SecureApiClient from '@/utils/secureApiClient';
import SecureTokenStorage from '@/utils/secureStorage';

/**
 * Helper function to get access token from secure storage
 */
export async function getAccessToken(): Promise<string | null> {
  return await SecureTokenStorage.getToken();
}

/**
 * Hook to fetch energy switch data with TanStack Query
 */
export function useEnergySwitch(switchId: string | null) {
  return useQuery({
    queryKey: ['energySwitch', switchId],
    queryFn: async () => {
      if (!switchId) throw new Error('Switch ID is required');
      return await SecureApiClient.get<EnergySwitch>(`/api/v1/energy_switches/${switchId}/tariff_comparison`);
    },
    enabled: !!switchId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch energy switch status with TanStack Query
 */
export function useEnergySwitchStatus(switchId: string | null) {
  return useQuery({
    queryKey: ['energySwitchStatus', switchId],
    queryFn: async () => {
      if (!switchId) throw new Error('Switch ID is required');
      return await SecureApiClient.get<SwitchStatusType>(`/api/v1/energy_switches/${switchId}/switch_status`);
    },
    enabled: !!switchId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for status updates
  });
}

/**
 * Hook for confirming an energy switch using TanStack Query
 */
export function useConfirmEnergySwitch(switchId: string | null) {
  const queryClient = useQueryClient();
  const token = getAccessToken();
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  
  return useMutation({
    mutationFn: async ({ switchTo, formData }: { switchTo: string; formData: any }) => {
      const url = `${baseUrl}/api/v1/energy_switches/${switchId}/confirm_switch`;
      
      const requestBody = {
        ...formData,
        switch_to: switchTo
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error('Failed to confirm switch');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch switch status after successful confirmation
      queryClient.invalidateQueries({ queryKey: ['energySwitchStatus', switchId] });
      queryClient.invalidateQueries({ queryKey: ['energySwitch', switchId] });
    }
  });
}

/**
 * Local storage functions for switch ID
 * 
 * Note: These functions are deprecated and should not be used.
 * The energy switch ID should be obtained from the authenticated session
 * via session.user.energySwitchId
 */
export function storeVerifiedSwitchId(id: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('verifiedSwitchId', id);
  }
}

export function getVerifiedSwitchId(): string | null {
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem('verifiedSwitchId');
  }
  return null;
}