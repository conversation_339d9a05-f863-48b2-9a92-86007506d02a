/**
 * Server-only authentication functions for TanStack Start
 */

import { getCookie, setCookie, deleteCookie } from 'vinxi/http';

export interface AuthSession {
  user: {
    id: string;
    energySwitchId: string;
    hasGas: boolean;
    hasElectricity: boolean;
    status: string;
  };
  token: string;
  expiresAt: number;
}

/**
 * Server function to set auth session cookie
 */
export async function setAuthSession(session: AuthSession) {
  'use server';
  
  const sessionData = JSON.stringify(session);
  
  setCookie('auth-session', sessionData, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60, // 1 hour
    path: '/'
  });
}

/**
 * Server function to get auth session from cookie
 */
export async function getAuthSession(): Promise<AuthSession | null> {
  'use server';
  
  try {
    const sessionCookie = getCookie('auth-session');
    if (!sessionCookie) return null;
    
    const session = JSON.parse(sessionCookie) as AuthSession;
    
    // Check if session is expired
    if (Date.now() > session.expiresAt) {
      await clearAuthSession();
      return null;
    }
    
    return session;
  } catch (error) {
    console.error('Error parsing auth session:', error);
    await clearAuthSession();
    return null;
  }
}

/**
 * Server function to clear auth session
 */
export async function clearAuthSession() {
  'use server';
  
  deleteCookie('auth-session');
}

/**
 * Server function to refresh session expiry
 */
export async function refreshAuthSession() {
  'use server';
  
  const session = await getAuthSession();
  if (!session) return null;
  
  // Extend session by 1 hour
  session.expiresAt = Date.now() + (60 * 60 * 1000);
  await setAuthSession(session);
  
  return session;
}