import { createFileRoute } from '@tanstack/react-router'
import { useEnergySwitch } from '@/hooks/useEnergySwitchData'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ErrorMessage } from '@/components/ui/error-message'
import { EnergyResults } from '@/components/onboarding/energy-results'
import { useAuth } from '@/context/AuthContext'
import { useEffect, useState } from 'react'
import { useRouter } from '@tanstack/react-router'

export const Route = createFileRoute('/onboarding/energy-switches/$id')({
  component: EnergySwitchPage,
})

function EnergySwitchPage() {
  const { id } = Route.useParams()
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()
  const [authChecked, setAuthChecked] = useState(false)
  
  console.log('EnergySwitchPage: Auth state', { 
    isAuthenticated, 
    hasUser: !!user, 
    userEnergySwitch: user?.energySwitchId,
    requestedId: id,
    authChecked 
  })
  
  // Wait for authentication state to be determined before making decisions
  useEffect(() => {
    // If we have a user, auth is definitely resolved
    if (user) {
      setAuthChecked(true)
      
      // Check if user has access to this specific energy switch
      if (user.energySwitchId !== id) {
        console.warn('EnergySwitchPage: User trying to access different energy switch:', {
          userSwitchId: user.energySwitchId,
          requestedId: id,
        })
        router.navigate({ to: `/onboarding/energy-switches/${user.energySwitchId}` })
        return
      }
    } else if (!isAuthenticated && authChecked) {
      // Only redirect if we've confirmed the user is not authenticated
      console.log('EnergySwitchPage: User not authenticated, redirecting to onboarding')
      router.navigate({ to: '/onboarding' })
    }
  }, [isAuthenticated, user, id, router, authChecked])

  // Mark auth as checked after a brief moment to let context settle
  useEffect(() => {
    const timer = setTimeout(() => setAuthChecked(true), 100)
    return () => clearTimeout(timer)
  }, [])

  const { data: energySwitch, isLoading, error } = useEnergySwitch(id)

  // Don't render anything while authentication is being checked
  if (!authChecked || (!isAuthenticated && !user)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-4 text-xl font-semibold text-gray-700">Checking authentication...</h2>
          <p className="mt-2 text-gray-500">Please wait while we verify your access.</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-4 text-xl font-semibold text-gray-700">Loading your energy switch...</h2>
          <p className="mt-2 text-gray-500">Please wait while we fetch your information.</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <ErrorMessage 
        title="Error Loading Energy Switch" 
        message="We couldn't load your energy switch information. Please try again later."
        actionText="Try Again"
        actionHref={`/onboarding/energy-switches/${id}`}
      />
    )
  }

  if (!energySwitch) {
    return (
      <ErrorMessage 
        title="Energy Switch Not Found" 
        message="We couldn't find the energy switch you're looking for."
        actionText="Return to Dashboard"
        actionHref="/onboarding"
      />
    )
  }

  return <EnergyResults energySwitch={energySwitch} />
}