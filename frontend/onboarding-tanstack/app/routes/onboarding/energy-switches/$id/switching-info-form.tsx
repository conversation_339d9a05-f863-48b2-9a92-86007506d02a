import { createFileRoute } from '@tanstack/react-router'
import { SwitchingInfoForm } from '@/components/onboarding/switching-info-form'
import { useEnergySwitch } from '@/hooks/useEnergySwitchData'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ErrorMessage } from '@/components/ui/error-message'

export const Route = createFileRoute('/onboarding/energy-switches/$id/switching-info-form')({
  component: SwitchingInfoFormPage,
  validateSearch: (search: Record<string, unknown>) => ({
    switch_to: (search.switch_to as string) || '',
  }),
})

function SwitchingInfoFormPage() {
  const { id } = Route.useParams()
  const { switch_to } = Route.useSearch()
  
  // Use TanStack Query hook to fetch energy switch data
  const { data: energySwitch, isLoading, error } = useEnergySwitch(id)
  
  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="large" />
      </div>
    )
  }
  
  // Handle error state
  if (error) {
    return (
      <ErrorMessage 
        title="Error Loading Data"
        message="We couldn't load your energy switch details. Please try again."
        actionText="Go Back"
        actionHref={`/onboarding/energy-switches/${id}`}
      />
    )
  }
  
  // Handle missing data
  if (!energySwitch || !switch_to) {
    return (
      <ErrorMessage 
        title="Missing Information"
        message="We couldn't find all the information needed to complete your switch."
        actionText="Go Back"
        actionHref={`/onboarding/energy-switches/${id}`}
      />
    )
  }

  return (
    <SwitchingInfoForm
      energySwitch={energySwitch}
      switchTo={switch_to}
    />
  )
}