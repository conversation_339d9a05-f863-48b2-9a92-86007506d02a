import { createFileRoute } from '@tanstack/react-router'
import { SwitchStatus } from '@/components/onboarding/switch-status'
import { useEnergySwitchStatus } from '@/hooks/useEnergySwitchData'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ErrorMessage } from '@/components/ui/error-message'
import { useToast } from '@/components/ui/use-toast'
import { useEffect } from 'react'

export const Route = createFileRoute('/onboarding/energy-switches/$id/switch-status')({
  component: SwitchStatusPage,
})

function SwitchStatusPage() {
  const { id } = Route.useParams()
  const { toast } = useToast()
  
  // Use TanStack Query hook to fetch switch status data
  const { data: switchStatus, isLoading, error, refetch } = useEnergySwitchStatus(id)

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Failed to load switch status information",
        variant: "destructive",
      })
    }
  }, [error, toast])
  
  // Handle data loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <LoadingSpinner size="large" />
        <span className="mt-4 text-gray-600">Loading your switch status...</span>
      </div>
    )
  }
  
  // Handle error state
  if (error) {
    return (
      <ErrorMessage 
        title="Error loading switch status"
        message="We had trouble retrieving your energy switch status. Please try again later."
        actionText="Return to Dashboard"
        actionHref="/onboarding"
      />
    )
  }
  
  // Handle missing data
  if (!switchStatus) {
    return (
      <ErrorMessage 
        title="Switch not found"
        message="We couldn't find details for this energy switch. Please check the URL or contact support."
        actionText="View all switches"
        actionHref="/onboarding"
      />
    )
  }

  return <SwitchStatus switchStatus={switchStatus} />
}