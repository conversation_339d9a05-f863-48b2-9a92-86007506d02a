import { createFileRoute } from '@tanstack/react-router'
import { SwitchCompletion } from '@/components/onboarding/switch-completion'
import { useEnergySwitch } from '@/hooks/useEnergySwitchData'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ErrorMessage } from '@/components/ui/error-message'

export const Route = createFileRoute('/onboarding/completed-switch')({
  component: CompletedSwitchPage,
  validateSearch: (search: Record<string, unknown>) => ({
    switch_id: (search.switch_id as string) || '',
    switch_to: (search.switch_to as string) || '',
  }),
})

function CompletedSwitchPage() {
  const { switch_id, switch_to } = Route.useSearch()
  
  // Use TanStack Query hook to fetch energy switch data
  const { data: energySwitch, isLoading, error } = useEnergySwitch(switch_id)
  
  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="large" />
      </div>
    )
  }
  
  // Handle error state
  if (error) {
    return (
      <ErrorMessage 
        title="Error Loading Data"
        message="We couldn't load your energy switch details. Please try again."
        actionText="Go Back"
        actionHref="/onboarding"
      />
    )
  }
  
  // Handle missing data
  if (!energySwitch) {
    return (
      <ErrorMessage 
        title="Switch not found"
        message="We couldn't find details for this energy switch."
        actionText="Return to Dashboard"
        actionHref="/onboarding"
      />
    )
  }

  return <SwitchCompletion energySwitch={energySwitch} switchTo={switch_to} />
}