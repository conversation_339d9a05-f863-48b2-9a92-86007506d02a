import { Outlet, createRootRoute } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/router-devtools'
import { useAuth } from '@/context/AuthContext'

interface RouterContext {
  auth: {
    user: any;
    isAuthenticated: boolean;
    getAuthToken: () => string | null;
  };
}

export const Route = createRootRoute({
  component: RootComponent,
})

function RootComponent() {
  const auth = useAuth();

  return (
    <>
      <Outlet context={{ auth }} />
      <TanStackRouterDevtools />
    </>
  )
}