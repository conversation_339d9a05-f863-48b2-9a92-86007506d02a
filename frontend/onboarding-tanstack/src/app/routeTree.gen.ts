/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as IndexRouteImport } from './routes/index'
import { Route as OnboardingIndexRouteImport } from './routes/onboarding/index'
import { Route as OnboardingEnergySwitchesIdRouteImport } from './routes/onboarding/energy-switches/$id'
import { Route as OnboardingEnergySwitchesIdSwitchCompletionRouteImport } from './routes/onboarding/energy-switches/$id.switch-completion'

const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const OnboardingIndexRoute = OnboardingIndexRouteImport.update({
  id: '/onboarding/',
  path: '/onboarding/',
  getParentRoute: () => rootRouteImport,
} as any)
const OnboardingEnergySwitchesIdRoute =
  OnboardingEnergySwitchesIdRouteImport.update({
    id: '/onboarding/energy-switches/$id',
    path: '/onboarding/energy-switches/$id',
    getParentRoute: () => rootRouteImport,
  } as any)
const OnboardingEnergySwitchesIdSwitchCompletionRoute =
  OnboardingEnergySwitchesIdSwitchCompletionRouteImport.update({
    id: '/switch-completion',
    path: '/switch-completion',
    getParentRoute: () => OnboardingEnergySwitchesIdRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/onboarding': typeof OnboardingIndexRoute
  '/onboarding/energy-switches/$id': typeof OnboardingEnergySwitchesIdRouteWithChildren
  '/onboarding/energy-switches/$id/switch-completion': typeof OnboardingEnergySwitchesIdSwitchCompletionRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/onboarding': typeof OnboardingIndexRoute
  '/onboarding/energy-switches/$id': typeof OnboardingEnergySwitchesIdRouteWithChildren
  '/onboarding/energy-switches/$id/switch-completion': typeof OnboardingEnergySwitchesIdSwitchCompletionRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/onboarding/': typeof OnboardingIndexRoute
  '/onboarding/energy-switches/$id': typeof OnboardingEnergySwitchesIdRouteWithChildren
  '/onboarding/energy-switches/$id/switch-completion': typeof OnboardingEnergySwitchesIdSwitchCompletionRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/onboarding'
    | '/onboarding/energy-switches/$id'
    | '/onboarding/energy-switches/$id/switch-completion'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/onboarding'
    | '/onboarding/energy-switches/$id'
    | '/onboarding/energy-switches/$id/switch-completion'
  id:
    | '__root__'
    | '/'
    | '/onboarding/'
    | '/onboarding/energy-switches/$id'
    | '/onboarding/energy-switches/$id/switch-completion'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  OnboardingIndexRoute: typeof OnboardingIndexRoute
  OnboardingEnergySwitchesIdRoute: typeof OnboardingEnergySwitchesIdRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/onboarding/': {
      id: '/onboarding/'
      path: '/onboarding'
      fullPath: '/onboarding'
      preLoaderRoute: typeof OnboardingIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/onboarding/energy-switches/$id': {
      id: '/onboarding/energy-switches/$id'
      path: '/onboarding/energy-switches/$id'
      fullPath: '/onboarding/energy-switches/$id'
      preLoaderRoute: typeof OnboardingEnergySwitchesIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/onboarding/energy-switches/$id/switch-completion': {
      id: '/onboarding/energy-switches/$id/switch-completion'
      path: '/switch-completion'
      fullPath: '/onboarding/energy-switches/$id/switch-completion'
      preLoaderRoute: typeof OnboardingEnergySwitchesIdSwitchCompletionRouteImport
      parentRoute: typeof OnboardingEnergySwitchesIdRoute
    }
  }
}

interface OnboardingEnergySwitchesIdRouteChildren {
  OnboardingEnergySwitchesIdSwitchCompletionRoute: typeof OnboardingEnergySwitchesIdSwitchCompletionRoute
}

const OnboardingEnergySwitchesIdRouteChildren: OnboardingEnergySwitchesIdRouteChildren =
  {
    OnboardingEnergySwitchesIdSwitchCompletionRoute:
      OnboardingEnergySwitchesIdSwitchCompletionRoute,
  }

const OnboardingEnergySwitchesIdRouteWithChildren =
  OnboardingEnergySwitchesIdRoute._addFileChildren(
    OnboardingEnergySwitchesIdRouteChildren,
  )

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  OnboardingIndexRoute: OnboardingIndexRoute,
  OnboardingEnergySwitchesIdRoute: OnboardingEnergySwitchesIdRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
