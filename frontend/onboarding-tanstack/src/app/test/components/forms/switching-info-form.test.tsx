import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders } from '@/test/utils'
import { SwitchingInfoForm } from '@/components/forms/switching-info-form'

// Mock the router hooks
vi.mock('@tanstack/react-router', () => ({
  useNavigate: vi.fn(() => vi.fn()),
}))

// Mock the API
vi.mock('@/lib/api', () => ({
  fetchUser: vi.fn().mockResolvedValue({
    id: 'test-user-id',
    email: '<EMAIL>',
    firstName: '',
    lastName: '',
  }),
  fetchEnergySwitch: vi.fn().mockResolvedValue({
    id: 'test-switch-id',
    userId: 'test-user-id',
    status: 'pending',
    currentSupplier: 'British Gas',
    newSupplier: 'Octopus Energy',
  }),
  updateUser: vi.fn().mockResolvedValue({}),
  updateEnergySwitch: vi.fn().mockResolvedValue({}),
}))

describe('SwitchingInfoForm', () => {
  const mockProps = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    },
    energySwitch: {
      id: 'test-switch-id',
      userId: 'test-user-id',
      status: 'pending',
      currentSupplier: 'British Gas',
      newSupplier: 'Octopus Energy',
    },
  }

  it('should render form tabs', async () => {
    renderWithProviders(<SwitchingInfoForm {...mockProps} />)

    // Wait for the form to load
    expect(await screen.findByText('Personal Info')).toBeInTheDocument()
    expect(screen.getByText('Security Info')).toBeInTheDocument()
    expect(screen.getByText('Payment Info')).toBeInTheDocument()
  })

  it('should handle form interactions without crashing', async () => {
    const user = userEvent.setup()
    renderWithProviders(<SwitchingInfoForm {...mockProps} />)

    // Wait for form to load
    await screen.findByText('Personal Info')

    // Look for any button that might be the next/submit button
    const buttons = screen.getAllByRole('button')
    const nextButton = buttons.find(button =>
      button.textContent?.toLowerCase().includes('next') ||
      button.textContent?.toLowerCase().includes('security')
    )

    expect(nextButton).toBeDefined()
    if (nextButton) {
      // Click the button and verify the form doesn't crash
      await user.click(nextButton)

      // After clicking, the form should still be rendered (check for the main container)
      expect(screen.getByText('Complete Your Information')).toBeInTheDocument()
    }
  })

  it('should navigate between tabs', async () => {
    const user = userEvent.setup()
    renderWithProviders(<SwitchingInfoForm {...mockProps} />)

    // Wait for form to load
    await screen.findByText('Personal Info')

    // Check that Security Info tab is initially disabled
    const securityTab = screen.getByText('Security Info')
    expect(securityTab).toBeDisabled()

    // The Personal Info tab should be active and show personal form fields
    expect(screen.getByText('First Name')).toBeInTheDocument()
  })
})
