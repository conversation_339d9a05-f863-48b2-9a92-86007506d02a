import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders } from '@/test/utils'

// Mock the auth module
vi.mock('@/lib/auth', () => ({
  verifyToken: vi.fn(),
  getTokenFromUrl: vi.fn(),
}))

// Mock the router hooks
vi.mock('@tanstack/react-router', () => ({
  useRouter: vi.fn(() => ({
    navigate: vi.fn(),
  })),
  useSearch: vi.fn(() => ({ token: 'test-token' })),
}))

describe('Onboarding Route', () => {
  it('should render onboarding page', () => {
    renderWithProviders(<div>Onboarding Page</div>)
    expect(screen.getByText('Onboarding Page')).toBeInTheDocument()
  })

  it('should handle token verification', async () => {
    const { verifyToken } = await import('@/lib/auth')
    vi.mocked(verifyToken).mockResolvedValue({
      userId: 'test-user-id',
      energySwitchId: 'test-switch-id',
      email: '<EMAIL>'
    })

    renderWithProviders(<div>Token verified</div>)
    expect(screen.getByText('Token verified')).toBeInTheDocument()
  })
})
