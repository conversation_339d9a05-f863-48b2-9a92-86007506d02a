import { describe, it, expect, vi, beforeEach } from 'vitest'
import { verifyToken, getTokenFromUrl } from '@/lib/auth'

// Mock jose
vi.mock('jose', () => ({
  jwtVerify: vi.fn(),
}))

describe('Auth utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getTokenFromUrl', () => {
    it('should extract token from URL search params', () => {
      const mockUrl = 'http://localhost:3000/onboarding?token=test-token'
      const token = getTokenFromUrl(mockUrl)
      expect(token).toBe('test-token')
    })

    it('should return null if no token in URL', () => {
      const mockUrl = 'http://localhost:3000/onboarding'
      const token = getTokenFromUrl(mockUrl)
      expect(token).toBeNull()
    })
  })

  describe('verifyToken', () => {
    it('should verify a valid token', async () => {
      const { jwtVerify } = await import('jose')
      const mockPayload = {
        userId: '123e4567-e89b-12d3-a456-426614174000',
        energySwitchId: '987fcdeb-51a2-43d7-8f9e-123456789abc',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        iat: Math.floor(Date.now() / 1000) // now
      }

      vi.mocked(jwtVerify).mockResolvedValue({
        payload: mockPayload,
        protectedHeader: { alg: 'HS256' },
        key: {} as any
      })

      const result = await verifyToken('valid-token')
      expect(result).toEqual(mockPayload)
    })

    it('should throw error for invalid token', async () => {
      const { jwtVerify } = await import('jose')
      vi.mocked(jwtVerify).mockRejectedValue(new Error('Invalid token'))

      await expect(verifyToken('invalid-token')).rejects.toThrow('Invalid token')
    })
  })
})
