import { jwtVerify, SignJWT } from 'jose'
import { z } from 'zod'

const JWT_SECRET = new TextEncoder().encode(
  import.meta.env.VITE_JWT_SECRET || 'your-secret-key'
)

export const OnboardingTokenSchema = z.object({
  userId: z.string().uuid(),
  energySwitchId: z.string().uuid(),
  email: z.string().email(),
  exp: z.number(),
  iat: z.number(),
})

export type OnboardingToken = z.infer<typeof OnboardingTokenSchema>

export async function verifyOnboardingToken(token: string): Promise<OnboardingToken | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return OnboardingTokenSchema.parse(payload)
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

export async function createOnboardingToken(payload: Omit<OnboardingToken, 'exp' | 'iat'>): Promise<string> {
  const now = Math.floor(Date.now() / 1000)
  const exp = now + (24 * 60 * 60) // 24 hours
  
  return await new SignJWT({ ...payload, iat: now, exp })
    .setProtectedHeader({ alg: 'HS256' })
    .sign(JWT_SECRET)
}

export interface SessionData {
  userId: string
  energySwitchId: string
  email: string
  isAuthenticated: boolean
}

export function createSession(token: OnboardingToken): SessionData {
  return {
    userId: token.userId,
    energySwitchId: token.energySwitchId,
    email: token.email,
    isAuthenticated: true,
  }
}

export function getTokenFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url)
    return urlObj.searchParams.get('token')
  } catch (error) {
    console.error('Failed to parse URL:', error)
    return null
  }
}

export async function verifyToken(token: string): Promise<OnboardingToken> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return OnboardingTokenSchema.parse(payload)
  } catch (error) {
    throw error
  }
}