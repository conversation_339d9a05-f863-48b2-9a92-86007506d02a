import { z } from 'zod'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3003'

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  if (!response.ok) {
    throw new ApiError(
      `API request failed: ${response.statusText}`,
      response.status,
      await response.json().catch(() => null)
    )
  }

  return response.json()
}

// Energy Switch API
export const EnergySwitchSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']),
  currentSupplier: z.string(),
  newSupplier: z.string(),
  estimatedSavings: z.number(),
  switchDate: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

export type EnergySwitch = z.infer<typeof EnergySwitchSchema>

export async function fetchEnergySwitch(id: string): Promise<EnergySwitch> {
  return apiRequest(`/api/energy_switches/${id}`)
}

export async function updateEnergySwitch(
  id: string,
  data: Partial<EnergySwitch>
): Promise<EnergySwitch> {
  return apiRequest(`/api/energy_switches/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(data),
  })
}

// User API
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phoneNumber: z.string().optional(),
  dateOfBirth: z.string().optional(),
})

export type User = z.infer<typeof UserSchema>

export async function fetchUser(id: string): Promise<User> {
  return apiRequest(`/api/users/${id}`)
}

export async function updateUser(id: string, data: Partial<User>): Promise<User> {
  return apiRequest(`/api/users/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(data),
  })
}