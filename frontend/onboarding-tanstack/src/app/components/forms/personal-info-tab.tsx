import { Use<PERSON>orm<PERSON><PERSON> } from '@tanstack/react-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FormField } from '@/components/ui/form-field'

interface PersonalInfoTabProps {
  form: UseFormApi<any>
}

export function PersonalInfoTab({ form }: PersonalInfoTabProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <form.Field
          name="firstName"
          children={(field) => (
            <FormField
              label="First Name"
              error={field.state.meta.errors?.[0]}
              required
            >
              <Input
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter your first name"
              />
            </FormField>
          )}
        />

        <form.Field
          name="lastName"
          children={(field) => (
            <FormField
              label="Last Name"
              error={field.state.meta.errors?.[0]}
              required
            >
              <Input
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter your last name"
              />
            </FormField>
          )}
        />
      </div>

      <form.Field
        name="email"
        children={(field) => (
          <FormField
            label="Email Address"
            error={field.state.meta.errors?.[0]}
            required
          >
            <Input
              id={field.name}
              name={field.name}
              type="email"
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Enter your email address"
            />
          </FormField>
        )}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <form.Field
          name="phoneNumber"
          children={(field) => (
            <FormField
              label="Phone Number"
              error={field.state.meta.errors?.[0]}
              required
            >
              <Input
                id={field.name}
                name={field.name}
                type="tel"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter your phone number"
              />
            </FormField>
          )}
        />

        <form.Field
          name="dateOfBirth"
          children={(field) => (
            <FormField
              label="Date of Birth"
              error={field.state.meta.errors?.[0]}
              required
            >
              <Input
                id={field.name}
                name={field.name}
                type="date"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
              />
            </FormField>
          )}
        />
      </div>
    </div>
  )
}