import { UseForm<PERSON>pi } from '@tanstack/react-form'
import { Input } from '@/components/ui/input'
import { FormField } from '@/components/ui/form-field'

interface SecurityInfoTabProps {
  form: UseFormApi<any>
}

export function SecurityInfoTab({ form }: SecurityInfoTabProps) {
  return (
    <div className="space-y-6">
      <form.Field
        name="currentAddress"
        children={(field) => (
          <FormField
            label="Current Address"
            error={field.state.meta.errors?.[0]}
            required
          >
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Enter your current address"
            />
          </FormField>
        )}
      />

      <form.Field
        name="previousAddress"
        children={(field) => (
          <FormField
            label="Previous Address (if moved in last 3 years)"
            error={field.state.meta.errors?.[0]}
          >
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Enter your previous address"
            />
          </FormField>
        )}
      />
    </div>
  )
}