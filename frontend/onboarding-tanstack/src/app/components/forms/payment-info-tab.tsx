import { <PERSON><PERSON>orm<PERSON><PERSON> } from '@tanstack/react-form'
import { Input } from '@/components/ui/input'
import { FormField } from '@/components/ui/form-field'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'

interface PaymentInfoTabProps {
  form: UseFormApi<any>
}

export function PaymentInfoTab({ form }: PaymentInfoTabProps) {
  return (
    <div className="space-y-6">
      <form.Field
        name="paymentMethod"
        children={(field) => (
          <FormField
            label="Payment Method"
            error={field.state.meta.errors?.[0]}
            required
          >
            <RadioGroup
              value={field.state.value}
              onValueChange={field.handleChange}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="direct_debit" id="direct_debit" />
                <Label htmlFor="direct_debit">Monthly Direct Debit</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="monthly_direct_debit" id="monthly_direct_debit" />
                <Label htmlFor="monthly_direct_debit">Variable Direct Debit</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="quarterly_bill" id="quarterly_bill" />
                <Label htmlFor="quarterly_bill">Quarterly Bill</Label>
              </div>
            </RadioGroup>
          </FormField>
        )}
      />

      <form.Field
        name="accountHolderName"
        children={(field) => (
          <FormField
            label="Account Holder Name"
            error={field.state.meta.errors?.[0]}
            required
          >
            <Input
              id={field.name}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Enter account holder name"
            />
          </FormField>
        )}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <form.Field
          name="sortCode"
          children={(field) => (
            <FormField
              label="Sort Code"
              error={field.state.meta.errors?.[0]}
              required
            >
              <Input
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="12-34-56"
                maxLength={6}
              />
            </FormField>
          )}
        />

        <form.Field
          name="accountNumber"
          children={(field) => (
            <FormField
              label="Account Number"
              error={field.state.meta.errors?.[0]}
              required
            >
              <Input
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="********"
                maxLength={8}
              />
            </FormField>
          )}
        />
      </div>
    </div>
  )
}