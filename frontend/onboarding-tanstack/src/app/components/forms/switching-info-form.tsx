import { useState } from 'react'
import { useForm } from '@tanstack/react-form'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from '@tanstack/react-router'
import { z } from 'zod'
import { EnergySwitch, User, updateUser, updateEnergySwitch } from '@/lib/api'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { PersonalInfoTab } from './personal-info-tab'
import { SecurityInfoTab } from './security-info-tab'
import { PaymentInfoTab } from './payment-info-tab'
import { Progress } from '@/components/ui/progress'
import { CheckCircle } from 'lucide-react'

const FormDataSchema = z.object({
  // Personal Info
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  
  // Security Info
  currentAddress: z.string().min(1, 'Current address is required'),
  previousAddress: z.string().optional(),
  
  // Payment Info
  paymentMethod: z.enum(['direct_debit', 'monthly_direct_debit', 'quarterly_bill']),
  accountHolderName: z.string().min(1, 'Account holder name is required'),
  sortCode: z.string().regex(/^\d{6}$/, 'Sort code must be 6 digits'),
  accountNumber: z.string().regex(/^\d{8}$/, 'Account number must be 8 digits'),
})

type FormData = z.infer<typeof FormDataSchema>

interface SwitchingInfoFormProps {
  energySwitch: EnergySwitch
  user: User
}

export function SwitchingInfoForm({ energySwitch, user }: SwitchingInfoFormProps) {
  const [activeTab, setActiveTab] = useState('personal')
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  const form = useForm<FormData>({
    defaultValues: {
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email,
      phoneNumber: user.phoneNumber || '',
      dateOfBirth: user.dateOfBirth || '',
      currentAddress: '',
      previousAddress: '',
      paymentMethod: 'direct_debit' as const,
      accountHolderName: '',
      sortCode: '',
      accountNumber: '',
    },
    onSubmit: async ({ value }) => {
      await submitForm(value)
    },
  })

  const submitMutation = useMutation({
    mutationFn: async (data: FormData) => {
      // Update user data
      await updateUser(user.id, {
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phoneNumber,
        dateOfBirth: data.dateOfBirth,
      })

      // Update energy switch status
      await updateEnergySwitch(energySwitch.id, {
        status: 'in_progress',
      })

      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user', user.id] })
      queryClient.invalidateQueries({ queryKey: ['energy-switch', energySwitch.id] })
      
      navigate({
        to: '/onboarding/energy-switches/$id/switch-completion',
        params: { id: energySwitch.id },
      })
    },
  })

  const submitForm = async (data: FormData) => {
    try {
      const validatedData = FormDataSchema.parse(data)
      await submitMutation.mutateAsync(validatedData)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const tabs = [
    { id: 'personal', label: 'Personal Info', completed: false },
    { id: 'security', label: 'Security Info', completed: false },
    { id: 'payment', label: 'Payment Info', completed: false },
  ]

  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab)
  const progress = ((currentTabIndex + 1) / tabs.length) * 100

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold">Complete Your Information</h2>
          <span className="text-sm text-muted-foreground">
            Step {currentTabIndex + 1} of {tabs.length}
          </span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            {tabs.map((tab, index) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="flex items-center gap-2"
                disabled={index > currentTabIndex}
              >
                {tab.completed && <CheckCircle className="h-4 w-4" />}
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="personal" className="mt-6">
            <PersonalInfoTab form={form} />
            <div className="flex justify-end mt-6">
              <Button
                type="button"
                onClick={() => setActiveTab('security')}
              >
                Next: Security Info
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="security" className="mt-6">
            <SecurityInfoTab form={form} />
            <div className="flex justify-between mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setActiveTab('personal')}
              >
                Back
              </Button>
              <Button
                type="button"
                onClick={() => setActiveTab('payment')}
              >
                Next: Payment Info
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="payment" className="mt-6">
            <PaymentInfoTab form={form} />
            <div className="flex justify-between mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setActiveTab('security')}
              >
                Back
              </Button>
              <Button
                type="submit"
                disabled={submitMutation.isPending}
              >
                {submitMutation.isPending ? 'Submitting...' : 'Complete Switch'}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  )
}