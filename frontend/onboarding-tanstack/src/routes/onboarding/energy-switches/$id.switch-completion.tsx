import { createFileRoute } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { fetchEnergySwitch } from '@/lib/api'
import { CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'

export const Route = createFileRoute('/onboarding/energy-switches/$id/switch-completion')({
  component: SwitchCompletionPage,
})

function SwitchCompletionPage() {
  const { id } = Route.useParams()
  
  const { data: energySwitch } = useQuery({
    queryKey: ['energy-switch', id],
    queryFn: () => fetchEnergySwitch(id),
  })

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm p-8 text-center">
        <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-6" />
        
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Switch Request Submitted!
        </h1>
        
        <p className="text-gray-600 mb-6">
          Your energy switch from {energySwitch?.currentSupplier} to {energySwitch?.newSupplier} has been successfully submitted.
        </p>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <p className="text-green-800 text-sm">
            You'll receive confirmation emails and updates about your switch progress.
            The switch typically takes 2-3 weeks to complete.
          </p>
        </div>
        
        <Button 
          onClick={() => window.close()}
          className="w-full"
        >
          Close
        </Button>
      </div>
    </div>
  )
}