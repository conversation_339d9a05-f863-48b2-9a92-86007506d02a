import { createFileRoute } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { fetchEnergySwitch, fetchUser } from '@/lib/api'
import { SwitchingInfoForm } from '@/components/forms/switching-info-form'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { ErrorMessage } from '@/components/ui/error-message'

export const Route = createFileRoute('/onboarding/energy-switches/$id')({
  component: EnergySwitchPage,
})

function EnergySwitchPage() {
  const { id } = Route.useParams()
  
  const {
    data: energySwitch,
    isLoading: isLoadingSwitch,
    error: switchError,
  } = useQuery({
    queryKey: ['energy-switch', id],
    queryFn: () => fetchEnergySwitch(id),
  })

  const {
    data: user,
    isLoading: isLoadingUser,
    error: userError,
  } = useQuery({
    queryKey: ['user', energySwitch?.userId],
    queryFn: () => fetchUser(energySwitch!.userId),
    enabled: !!energySwitch?.userId,
  })

  if (isLoadingSwitch || isLoadingUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner className="mx-auto mb-4" />
          <p className="text-muted-foreground">Loading your energy switch details...</p>
        </div>
      </div>
    )
  }

  if (switchError || userError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ErrorMessage 
          title="Failed to load energy switch"
          message={switchError?.message || userError?.message || 'Unknown error'}
        />
      </div>
    )
  }

  if (!energySwitch || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ErrorMessage 
          title="Energy switch not found"
          message="The requested energy switch could not be found."
        />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Complete Your Energy Switch
            </h1>
            <p className="text-gray-600">
              Switching from {energySwitch.currentSupplier} to {energySwitch.newSupplier}
            </p>
            {energySwitch.estimatedSavings > 0 && (
              <p className="text-green-600 font-medium mt-2">
                Estimated annual savings: £{energySwitch.estimatedSavings}
              </p>
            )}
          </div>
          
          <SwitchingInfoForm 
            energySwitch={energySwitch}
            user={user}
          />
        </div>
      </div>
    </div>
  )
}