import { createFileRoute, useSearch } from '@tanstack/react-router'
import { z } from 'zod'

const OnboardingSearchSchema = z.object({
  token: z.string().optional(),
})

export const Route = createFileRoute('/onboarding')({
  validateSearch: OnboardingSearchSchema,
  component: OnboardingPage,
})

function OnboardingPage() {
  const search = useSearch({ from: '/onboarding' })
  const { token } = search
  
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold mb-4">Welcome to Onboarding</h1>
        {token ? (
          <p className="text-green-600">Token received: {token.substring(0, 20)}...</p>
        ) : (
          <p className="text-red-600">No token provided</p>
        )}
        <p className="text-gray-600 mt-4">This is a test page to verify routing works.</p>
      </div>
    </div>
  )
}
