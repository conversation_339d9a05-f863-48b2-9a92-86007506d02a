# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS runner

WORKDIR /app

# Copy built application
COPY --from=builder /app/.output ./
COPY --from=builder /app/package*.json ./

# Install production dependencies
RUN npm ci --only=production

EXPOSE 3001

CMD ["npm", "start"]