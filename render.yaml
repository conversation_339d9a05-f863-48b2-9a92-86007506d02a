services:
  - type: web
    name: meet-george
    env: ruby
    region: frankfurt
    buildCommand: "./bin/render-build.sh"
    startCommand: "bundle exec puma -C config/puma.rb"
    envVars:
      - key: DATABASE_URL
        sync: false
      - key: RAILS_MASTER_KEY
        sync: false
      - key: MEET_GEORGE_DATABASE_PASSWORD
        sync: false


  - type: worker
    name: meet-george-sidekiq
    env: ruby
    region: frankfurt
    buildCommand: bundle install
    startCommand: bundle exec sidekiq
    envVars:
      - key: RAILS_MASTER_KEY
        sync: false
