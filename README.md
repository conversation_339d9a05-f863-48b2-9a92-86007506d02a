# README

1. Same user with multiple properties switching same time
2. Same user use the service to onboard but not switch, then switch themself but then come back to use the service again to switch.
   They will have same address, different bills, system need to be differentiate them
3. Same user use the service and switch, then move to new address and use the service again to switch.


1. Admin Flow:

* Admin logs in through /api/v1/admin/auth/login
* The server generates and returns an access token with admin scope
* The client stores this token in localStorage/cookies
* The client includes this token in the Authorization header for API requests
* Protected endpoints validate the token using authorize_admin

2. ChatBot Flow:

* ChatBot tokens can be generated programmatically using TokenService.generate_chatbot_token
* These tokens can access endpoints protected by authorize_chatbot

3. Onboarding Flow:

* During onboarding, tokens are generated for users with TokenService.generate_onboarding_token
* These tokens provide limited access to complete the onboarding process
