class SwitchConfirmationMailerPreview < ActionMailer::Preview
  def confirm_switch_email
    email = '<EMAIL>'
    customer_name = '<PERSON>'
    supplier_name = 'Tomato Energy'
    tariff_name = 'Tomato Prime Fixed TP July 2024-V1'
    switch_status_url = '#'

    SwitchConfirmationMailer.with(
      email: email,
      customer_name: customer_name,
      supplier_name: supplier_name,
      tariff_name: tariff_name,
      switch_status_url: switch_status_url
    ).confirm_switch_email
  end
end