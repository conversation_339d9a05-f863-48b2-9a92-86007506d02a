GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bigdecimal (3.1.8)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    builder (3.3.0)
    concurrent-ruby (1.3.3)
    connection_pool (2.4.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.0)
    date (3.3.4)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    diff-lcs (1.5.1)
    doorkeeper (5.7.1)
      railties (>= 5)
    dotenv (3.1.7)
    dotenv-rails (3.1.7)
      dotenv (= 3.1.7)
      railties (>= 6.1)
    drb (2.2.1)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.4.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.11.0)
      faraday-net_http (>= 2.0, < 3.4)
      logger
    faraday-net_http (3.3.0)
      net-http
    faraday-retry (2.2.1)
      faraday (~> 2.0)
    fugit (1.11.0)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.1.0)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    io-console (0.7.2)
    irb (1.13.2)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.12.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json-schema (4.3.0)
      addressable (>= 2.8)
    jwt (2.8.2)
      base64
    logger (1.6.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    minitest (5.24.1)
    msgpack (1.7.2)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.2.0)
    net-http (0.4.1)
      uri
    net-imap (0.4.14)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.3)
    nokogiri (1.16.6-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.16.6-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.6-x86_64-linux)
      racc (~> 1.4)
    pagy (9.3.4)
    pg (1.5.6)
    psych (5.1.2)
      stringio
    public_suffix (6.0.0)
    puma (6.4.2)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.0)
    rack (3.1.6)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redis (5.2.0)
      redis-client (>= 0.22.0)
    redis-client (0.22.2)
      connection_pool
    reline (0.5.9)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    resend (0.13.0)
      httparty (>= 0.21.0)
    rexml (3.3.2)
      strscan
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.3)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    rswag (2.13.0)
      rswag-api (= 2.13.0)
      rswag-specs (= 2.13.0)
      rswag-ui (= 2.13.0)
    rswag-api (2.13.0)
      activesupport (>= 3.1, < 7.2)
      railties (>= 3.1, < 7.2)
    rswag-specs (2.13.0)
      activesupport (>= 3.1, < 7.2)
      json-schema (>= 2.2, < 5.0)
      railties (>= 3.1, < 7.2)
      rspec-core (>= 2.14)
    rswag-ui (2.13.0)
      actionpack (>= 3.1, < 7.2)
      railties (>= 3.1, < 7.2)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.0)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-cron (1.12.0)
      fugit (~> 1.8)
      globalid (>= 1.0.1)
      sidekiq (>= 6)
    stringio (3.1.1)
    strscan (3.1.0)
    thor (1.3.1)
    timeout (0.4.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uri (0.13.1)
    vcr (6.2.0)
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.8.1)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.16)

PLATFORMS
  aarch64-linux
  arm64-darwin-22
  x86_64-linux

DEPENDENCIES
  bcrypt (~> 3.1.7)
  bootsnap
  debug
  doorkeeper
  dotenv-rails
  factory_bot_rails
  faker
  faraday
  faraday-retry
  jbuilder
  jwt
  pagy
  pg (~> 1.1)
  puma (>= 5.0)
  rack-cors
  rails (~> 7.1.3, >= *******)
  redis (>= 4.0.1)
  request_store (~> 1.7)
  resend
  rspec-rails
  rswag
  shoulda-matchers
  sidekiq
  sidekiq-cron
  tzinfo-data
  vcr
  webmock

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.4.10
