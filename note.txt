1. Initial Engagement:
All opt ins, cookie acceptance etc as we may need these for tracking, look at compare the market travel insurance flow.
We need to track the IP address from start quote to the signed agreement.
What are they looking to switch, flex tariff, green, fixed, variable etc. Start a conversation and asking them to upload bill.
Collect email address for future plus enables us to potentially auto email and that drop off prior to the actual switch.

2. Data Collection:
Bill upload
Bot Interaction: <PERSON><PERSON> asks for name, email, and other necessary or missing information; users input this data.
Data Validation: Validate the entered data in real-time to ensure accuracy (e.g., valid email format, correct postcode). Ideally linking with post office/address, we would want to link with ECOES/Xoserve eventually as this will give us supplier and consumption information. We would need to present <PERSON> to them to get access. As a broker we can’t get it, but as a comparison company it is business critical for accuracy.

3. Backend Processing:
Data Transmission: Bot sends user data to our backend.
George to provide a summary on screen and a confirmation of them confirming the details are correct.
This would be part two of the audit trail and ideally recording the IP.

4. Supplier Communication:
Data Submission: Backend sends a batch of switching user data to the energy supplier (preferably via API, fallback to CSV if necessary).
Acknowledgment Receipt: Ensure the supplier acknowledges receipt of the data, ideally via an API response.

5. Switch:
Following tariff acceptance, they then process to further information required for switch.
Full name, date of birth, email confirmation, direct debit details and agree to switching, we will need to write our terms. 
Copying a current switching platforms lead. These may need adjusting once we onboard suppliers.
Once agreed the details are stored along with the time/date stamp and IP for audit trail.
The supplier tariff reference, unit rates, and the customer details are put into a file with the audit trail information.
Supplier contract processing will either be
Customer can opt in for future auto renewal and respective details need to be stored in a database.

6. Action:
Submission: Either API or CSV submission.
Supplier to confirm acceptance and status either via api or csv.
Thank you email from George and then added to mailing list, monthly energy email, potential cross sells etc.

7. Switching Process Initiation:
·        Supplier takes over.

8. Progress Tracking:
·        API Endpoints: Supplier either exposes API endpoints for us to query and notify users of progress or notifies us of progress through some other method (e.g., email, webhooks).

9. Completion Notification:
Confirmation: Once the switch is complete, the supplier notifies our backend.
Final Confirmation to User: Send a final confirmation email to the user, confirming the switch has been completed successfully and providing any necessary follow-up information.
10. Post-Switch Support:
Customer Support: Offer dedicated support to assist with any issues or questions the user might have post-switch.
Feedback Collection: Request feedback from the user about their experience to improve the process.
Additional/Future Enhancements:
Security Measures: Ensure all data transmissions are secure and comply with GDPR regulations.
User Dashboard: Provide a user dashboard where users can track the progress of their switch in real-time.
Integration with CRM: Integrate with a CRM system to manage user interactions and follow-ups efficiently.
Analytics and Reporting: Implement analytics to monitor the switching process’s efficiency and identify any bottlenecks or issues.
Marketing Automation: Use marketing automation tools to send personalised communications and offers to users based on their profile and switching history.

There will be changes that will naturally come in and some things that wont be possible to start with. Following supplier onboarding and when looking at v2 etc, it will naturally evolve.

The important part is the compliance boxes being ticked and the onboarding experience, which we already know will be alot better than anything else around.



Upload bill => Decide profile class and GSP code => Return corresponding tariff with rates
