version: "3.7"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails s -p 3000 -b '0.0.0.0'"
    stdin_open: true
    tty: true
    volumes:
      - .:/rails
    ports:
      - "3000:3000"
    depends_on:
      - sidekiq
    env_file:
      - .env

  redis:
    image: redis:7.0
    command: redis-server
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - '6379:6379'

  sidekiq:
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - redis
    command: bundle exec sidekiq
    volumes:
      - .:/rails
    env_file:
      - .env
