require 'faraday'
require 'json'


class RebelEnergyApiClient
  BASE_URL = ENV['REBEL_ENERGY_API_URL']

  def initialize
    @client_id = ENV['REBEL_ENERGY_CLIENT_ID']
    @client_secret = ENV['REBEL_ENERGY_CLIENT_SECRET']
    @token = nil
    @token_expires_at = nil
  end

  # V2 Endpoints

  def get_tariffs(gsp)
    get('/api/v2/tariffs/current', { gsp: gsp })
  end

  def get_mpxns(address_params)
    post('/api/v2/mpxns', address_params)
  end

  def get_addresses(postcode)
    get("/api/v2/addresses/#{postcode}")
  end

  def get_quote(quote_params)
    post('/api/v2/quote', quote_params)
  end

  # Sys Endpoints

  def get_customer_by_phone(phone)
    get("/api/sys/v1/customer-by-phone/#{phone}")
  end

  def get_customer_by_custref(cust_ref)
    get("/api/sys/v1/customer-by-custref/#{cust_ref}")
  end

  def get_customer_meters(customer_ref, mpan)
    get('/api/sys/v1/customer/meters', { customerRef: customer_ref, mpan: mpan })
  end

  def get_customer_contract(customer_ref)
    get('/api/sys/v1/customer/contract', { customerRef: customer_ref })
  end

  def get_customer_status(customer_ref)
    get('/api/sys/v1/customer/status', { customerRef: customer_ref })
  end

  def get_customer_reads(params)
    get('/api/sys/v1/customer/reads', params)
  end

  def get_customer_balance(customer_ref, email)
    get('/api/sys/v1/customer/balance', { customerRef: customer_ref, email: email })
  end

  def get_customer_bill(bill_id, customer_ref)
    get('/api/sys/v1/customer/bill', { billId: bill_id, customerRef: customer_ref })
  end

  def get_customer_bills(params)
    get('/api/sys/v1/customer/bills', params)
  end

  def get_customer_transactions(params)
    get('/api/sys/v1/customer/transactions', params)
  end

  def validate_details(customer_ref, email)
    get('/api/sys/v1/details/validate', { customerRef: customer_ref, email: email })
  end

  def get_encoded_data(data)
    get("/api/sys/v1/encoded-data/#{data}")
  end

  def submit_readings(readings_params)
    post('/api/sys/v1/readings/submit', readings_params)
  end

  def get_channel_data
    get('/api/sys/v1/get-channel-data')
  end

  def update_channel_data(channel_data)
    post('/api/sys/v1/update-channel-data', channel_data)
  end

  # Customer Endpoints

  def get_customer_status(external_ref)
    get('/api/customer/status', { externalRef: external_ref })
  end

  def get_customer_reads(params)
    get('/api/customer/reads', params)
  end

  def get_customer_meters(external_ref)
    get('/api/customer/meters', { externalRef: external_ref })
  end

  def get_customer_contract(external_ref)
    get('/api/customer/contract', { externalRef: external_ref })
  end

  def get_customer_balance(params)
    get('/api/customer/balance', params)
  end

  def get_customer_bills(params)
    get('/api/customer/bills', params)
  end

  def get_customer_transactions(params)
    get('/api/customer/transactions', params)
  end

  def get_customer_bill(bill_id, external_ref)
    get('/api/customer/bill', { billId: bill_id, externalRef: external_ref })
  end

  # General Endpoints

  # def get_addresses(postcode)
  #   get("/api/addresses/#{postcode}")
  # end

  def get_halfhourly_auth_test
    get('/api/hh/hh-auth-test')
  end

  def get_halfhourly_data(params)
    get('/api/hh/data', params)
  end

  # def get_mpxns(address_params)
  #   post('/api/mpxns', address_params)
  # end

  def get_aq(mprn)
    get("/api/aq/#{mprn}")
  end

  def get_psr
    get('/api/psr')
  end

  def get_eac(mpan)
    get("/api/eac/#{mpan}")
  end


  # salesChannel
  # externalRef
  # surname
  # firstname
  # productCode
  # supplyAddressStreetAddress
  # supplyAddressPostcode
  # paymentAmountGas if fuelType is gasOnly or dualFuel
  # paymentAmountElectricity if fuelType is electricityOnly or dualFuel
  # fuelType
  # contactMobilePhone if contactSecondaryPhone is empty
  # contactSecondaryPhone if contactMobilePhone
  def register_customer(customer_params)
    validate_customer_params(customer_params)
    post('/api/register', customer_params)
  end

  # def get_quote(quote_params)
  #   post('/api/quote', quote_params)
  # end

  def get_quote_dates(submission_date = nil)
    get('/api/quote/dates', submission_date ? { submissionDate: submission_date } : {})
  end

  def auth_test
    post('/api/authtest', {})
  end

  def get_gsp(postcode)
    get("/api/gsp/#{postcode}")
  end

  def send_command_request(command_params)
    post('/api/sendCommandRequest', command_params)
  end

  private

  def get(path, params = {})
    request(:get, path, params)
  end

  def post(path, body = {})
    request(:post, path, body)
  end

  def request(method, path, data = {})
    ensure_token
    response = connection.public_send(method) do |req|
      req.url path
      req.headers['Authorization'] = "Bearer #{@token}"
      req.headers['Content-Type'] = 'application/json'
      if method == :get
        req.params = data
      else
        req.body = data.to_json
      end
    end
    handle_response(response)
  end

  def handle_response(response)
    case response.status
    when 200..299
      JSON.parse(response.body)
    when 401
      @token = nil
      raise "Authentication failed"
    when 400..499
      raise "Client error: #{response.status} - #{response.body}"
    when 500..599
      raise "Server error: #{response.status} - #{response.body}"
    else
      raise "Unknown error: #{response.status} - #{response.body}"
    end
  end

  def ensure_token
    if @token.nil? || Time.now >= @token_expires_at
      authenticate
    end
  end

  def authenticate
    response = connection.post('/oauth/token') do |req|
      req.params['grant_type'] = 'client_credentials'
      req.params['client_id'] = @client_id
      req.params['client_secret'] = @client_secret
    end
    data = JSON.parse(response.body)
    @token = data['access_token']
    @token_expires_at = Time.now + data['expires_in']
  end

  def connection
    @connection ||= Faraday.new(url: BASE_URL) do |faraday|
      faraday.request :url_encoded
      faraday.adapter Faraday.default_adapter
    end
  end

  def validate_customer_params(params)
    required_fields = %w[salesChannel externalRef surname firstname productCode supplyAddressStreetAddress supplyAddressPostcode fuelType]
    missing_fields = required_fields - params.keys.map(&:to_s)
    
    if missing_fields.any?
      raise ArgumentError, "Missing required fields: #{missing_fields.join(', ')}"
    end

    # Validate fuel type specific fields
    case params[:fuelType]
    when 'gasOnly', 'dualFuel'
      raise ArgumentError, "Missing paymentAmountGas for gasOnly or dualFuel" unless params[:paymentAmountGas]
    when 'electricityOnly', 'dualFuel'
      raise ArgumentError, "Missing paymentAmountElectricity for electricityOnly or dualFuel" unless params[:paymentAmountElectricity]
    else
      raise ArgumentError, "Invalid fuelType: #{params[:fuelType]}"
    end

    # Validate contact phone
    unless params[:contactMobilePhone] || params[:contactSecondaryPhone]
      raise ArgumentError, "Either contactMobilePhone or contactSecondaryPhone must be provided"
    end
  end
end
