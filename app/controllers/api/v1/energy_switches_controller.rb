module Api
  module V1
    class EnergySwitchesController < Api::V1::ApiController
      before_action :set_energy_switch, only: [
        :tariff_comparison,
        :confirm_switch,
        :switch_status
      ]
      before_action :authorize_onboarding_for_switch, only: [
        :tariff_comparison,
        :confirm_switch,
        :switch_status
      ]

      def tariff_comparison
        # Generate cache key before updating viewed_at to avoid cache invalidation
        cache_key = generate_tariff_comparison_cache_key
        
        # Cache the expensive tariff calculations
        Rails.cache.fetch(cache_key, expires_in: 30.minutes) do
          @energy_switch.available_tariffs
        end
        
        # Update viewed_at after caching to avoid invalidating cache on every request
        @energy_switch.update_column(:viewed_at, Time.zone.now)

        render :tariff_comparison
      end

      def confirm_switch
        permitted_params = confirm_switch_params
        # Convert to JSON and back to ensure native JSON format
        json_params = permitted_params.to_json
        hash_params = JSON.parse(json_params)
        ConfirmSwitchJob.perform_async(@energy_switch.id, hash_params)
        json_response({ message: "Switch confirmed", status: :confirmed }, :ok)
      end

      def switch_status
        set_profile_class_and_gsp_code_for_electricity_tariff(@energy_switch)

        electricity_tariff = @energy_switch.electricity_energy_tariff

        if electricity_tariff.present? && (electricity_tariff.gsp_code.blank? || electricity_tariff.profile_class.blank?)
          Rails.logger.error("Unexpected error: Electricity tariff is present but gsp_code or profile_class is blank")
          render json: { error: 'Unexpected error' }, status: :unprocessable_entity
        else
          switch_date = @energy_switch.switch_date

          render json: {
            currentStage: @energy_switch.current_stage,
            referenceNumber: @energy_switch.reference_number,
            supplier: @energy_switch.switching_to_tariff.try(:supplier).try(:name),
            switchDate: switch_date.present? ? switch_date.strftime("#{switch_date.day.ordinalize} %B %Y") : nil,
            isRejected: @energy_switch.rejected_by_supplier?,
            stages: @energy_switch.stages
          }
        end
      end

      private

      def set_energy_switch
        # Use a single optimized query with all joins
        @energy_switch = EnergySwitch
          .joins("LEFT JOIN switch_users ON switch_users.id = energy_switches.switch_user_id")
          .joins("LEFT JOIN addresses ON addresses.id = energy_switches.address_id")
          .joins("LEFT JOIN user_tariffs AS electricity_tariff ON electricity_tariff.id = energy_switches.electricity_energy_tariff_id")
          .joins("LEFT JOIN user_tariffs AS gas_tariff ON gas_tariff.id = energy_switches.gas_energy_tariff_id")
          .joins("LEFT JOIN energy_tariffs AS switching_tariff ON switching_tariff.id = energy_switches.switching_to_tariff_id")
          .joins("LEFT JOIN suppliers ON suppliers.id = switching_tariff.supplier_id")
          .select(
            "energy_switches.*",
            "switch_users.id as switch_user_id, switch_users.first_name, switch_users.last_name, switch_users.email, switch_users.phone_number",
            "addresses.id as address_id, addresses.full_address, addresses.postcode, addresses.posttown",
            "electricity_tariff.id as elec_tariff_id, electricity_tariff.electricity_est_annual_usage, electricity_tariff.unit_rate as elec_unit_rate, electricity_tariff.standing_charge as elec_standing_charge, electricity_tariff.profile_class, electricity_tariff.gsp_code, electricity_tariff.supplier_name as elec_supplier_name, electricity_tariff.tariff_name as elec_tariff_name, electricity_tariff.tariff_type as elec_tariff_type, electricity_tariff.exit_fees as elec_exit_fees, electricity_tariff.payment_method as elec_payment_method",
            "gas_tariff.id as gas_tariff_id, gas_tariff.gas_estimated_annual_usage, gas_tariff.unit_rate as gas_unit_rate, gas_tariff.standing_charge as gas_standing_charge, gas_tariff.supplier_name as gas_supplier_name, gas_tariff.tariff_name as gas_tariff_name, gas_tariff.tariff_type as gas_tariff_type, gas_tariff.exit_fees as gas_exit_fees, gas_tariff.payment_method as gas_payment_method"
          )
          .find(params[:id])
      end

      def authorize_onboarding_for_switch
        authorize_onboarding(@energy_switch.switch_user_id)
      end

      def set_profile_class_and_gsp_code_for_electricity_tariff(energy_switch)
        electricity_tariff = energy_switch.electricity_energy_tariff

        if electricity_tariff.present?
          Rails.cache.fetch("energy_switch_#{energy_switch.id}_tariff_details", expires_in: 10.minutes) do
            if electricity_tariff.gsp_code.blank?
              electricity_xoserve_api = XoserveElectricityApiService.new
              result = electricity_xoserve_api.search_utility_address(electricity_tariff.meter_serial_number)

              electricity_tariff.gsp_code = result["gsp_code"]

              if electricity_tariff.profile_class.blank?
                technical_details = electricity_xoserve_api.get_technical_details_by_mpan(result["mpan"])
                electricity_tariff.profile_class = technical_details["profile_class"]
              end

              electricity_tariff.save!
            end
            
            true # Return value for cache
          end
        end
      end

      def generate_tariff_comparison_cache_key
        # Create cache key based on energy switch attributes and tariff IDs
        # Exclude updated_at since viewed_at updates change it but don't affect tariff calculations
        key_components = [
          "tariff_comparison_v3",
          @energy_switch.id,
          @energy_switch.gas_energy_tariff_id,
          @energy_switch.electricity_energy_tariff_id,
          @energy_switch.status
        ].compact
        
        key_components.join("_")
      end

      def confirm_switch_params
        params.permit(
          :firstName, :lastName, :address, :livedThreeYears, :email, 
          :phoneNumber, :dateOfBirth, :requiresSupport, :accountHolder, 
          :sortCode, :accountNumber, :isBillingAddressSame, :billingAddress, 
          :paymentMethodPreference, :switchPreference, :switch_to, :agreeToTerms
        )
      end
    end
  end
end
