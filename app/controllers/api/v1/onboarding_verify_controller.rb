module Api
  module V1
    class OnboardingVerifyController < Api::V1::ApiController
      # Handle token verification and return JSON response for the NextJS app
      def verify
        # Extract token from Authorization header
        auth_header = request.headers['Authorization']
        
        if auth_header.blank? || !auth_header.start_with?('Bearer ')
          json_response({ error: "Invalid authorization" }, :unprocessable_entity)
          return
        end
        
        # Extract token from "Bearer <token>"
        token = auth_header.gsub('Bearer ', '')
        
        if token.blank?
          json_response({ error: "Invalid access link" }, :unprocessable_entity)
          return
        end
        
        # Rate limiting by IP to prevent brute force attempts
        rate_limit_key = "onboarding_verify_attempts:#{request.remote_ip}"
        attempts = Rails.cache.fetch(rate_limit_key, expires_in: 1.hour) { 0 }
        
        if attempts >= 10 && Rails.env.production?
          # Log suspicious activity
          SecurityMonitoringService.record_rate_limit_triggered(
            "unknown", 
            request.remote_ip, 
            "onboarding_token_verify"
          )
          
          json_response({ error: "Too many attempts. Please try again later" }, :too_many_requests)
          return
        end
        
        # Increment attempts counter
        Rails.cache.write(rate_limit_key, attempts + 1, expires_in: 1.hour)
        
        # Verify the token and get the energy switch
        energy_switch_id = TokenService.verify_onboarding_jwt(token)
        
        if energy_switch_id.nil?
          json_response({ error: "Your access link has expired or is invalid" }, :unauthorized)
          return
        end
        
        # Reset attempts counter on successful verification
        Rails.cache.delete(rate_limit_key)

        energy_switch = EnergySwitch.find(energy_switch_id)
        
        # Return the energy switch data that the NextJS app needs
        json_response({
          success: true,
          energy_switch_id: energy_switch.id,
          user_id: energy_switch.switch_user_id,
          status: energy_switch.status,
          has_gas: energy_switch.gas_energy_tariff.present?,
          has_electricity: energy_switch.electricity_energy_tariff.present?
        }, :ok)
      end
    end
  end
end 