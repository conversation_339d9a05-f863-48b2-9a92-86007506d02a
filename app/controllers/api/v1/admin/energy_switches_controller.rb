module Api
  module V1
    module Admin
      class EnergySwitchesController < Api::V1::ApiController
        before_action :authorize_admin
        
        def index
          @switches = EnergySwitch.includes(:switch_user, :gas_energy_tariff, :electricity_energy_tariff, :switching_to_tariff)
                               .order(created_at: :desc)
          
          # Apply search if provided
          if params[:search].present?
            search_term = "%#{params[:search]}%"
            @switches = @switches.joins(:switch_user)
                                .where("energy_switches.reference_number ILIKE ? OR switch_users.email ILIKE ?", 
                                       search_term, search_term)
          end
          
          # Apply filters if provided
          if params[:status].present?
            case params[:status]
            when 'pending'
              @switches = @switches.where(status: [:confirmed, :submitted_to_supplier, :supplier_processing])
            when 'completed'
              @switches = @switches.where(status: :switched)
            when 'cancelled'
              # Add cancelled status if you have one
              @switches = @switches.where(status: []) # Empty array since there's no cancelled state
            when 'failed'
              @switches = @switches.where(status: :rejected_by_supplier)
            end
          end
          
          if params[:user_id].present?
            @switches = @switches.where(switch_user_id: params[:user_id])
          end
          
          # Filter for suppliers - this needs to be adjusted based on your data model
          if params[:from_supplier].present? || params[:to_supplier].present?
            # These filters will need to be implemented based on how suppliers are related
            # to your energy tariffs and switches
          end
          
          if params[:from_date].present?
            start_date = Date.parse(params[:from_date]) rescue nil
            @switches = @switches.where('energy_switches.created_at >= ?', start_date.beginning_of_day) if start_date
          end
          
          if params[:to_date].present?
            end_date = Date.parse(params[:to_date]) rescue nil
            @switches = @switches.where('energy_switches.created_at <= ?', end_date.end_of_day) if end_date
          end
          
          # Use pagy for pagination
          limit = [(params[:limit].presence || 25).to_i, 100].min
          page = (params[:page] || 1).to_i
          @pagy, @paginated_switches = pagy(@switches, limit: limit, page: page)

          # Format the switches data for the response
          switches_data = @paginated_switches.map do |switch|
            {
              id: switch.id,
              userId: switch.switch_user_id,
              userEmail: switch.switch_user&.email,
              fromSupplier: determine_from_supplier(switch),
              referenceNumber: switch.reference_number,
              toSupplier: determine_to_supplier(switch),
              status: map_status_to_frontend(switch.status),
              createdAt: switch.created_at.iso8601,
              completedAt: switch.status == 'switched' ? switch.updated_at.iso8601 : nil,
              failureReason: switch.status == 'rejected_by_supplier' ? "Switch rejected by supplier" : nil
            }
          end
          
          # Format the response to match original format
          render json: {
            switches: switches_data,
            total: @pagy.count,
            page: @pagy.page,
            limit: limit,
            total_pages: @pagy.pages
          }
        end
        
        def show
          @switch = EnergySwitch.includes(:switch_user, :gas_energy_tariff, :electricity_energy_tariff, :switching_to_tariff, :address, :payment_method, :supplier_submissions)
                                .find(params[:id])
          
          # Generate pre-signed URL for bill if it exists
          bill_presigned_url = nil
          if @switch.bill_url.present?
            storage_service = Services::SupabaseStorageService.new
            bill_presigned_url = storage_service.presigned_url(@switch.bill_url)
          end
          
          switch_data = {
            id: @switch.id,
            reference_number: @switch.reference_number,
            bill_url: bill_presigned_url,
            bill_json: @switch.raw_bill_json,
            supplier_reference: @switch.supplier_reference,
            user: {
              id: @switch.switch_user.id,
              email: @switch.switch_user.email,
              firstName: @switch.switch_user.first_name,
              lastName: @switch.switch_user.last_name,
              phoneNumber: @switch.switch_user.phone_number,
              requiresPsrSupport: @switch.switch_user.respond_to?(:requires_psr_support) ? @switch.switch_user.requires_psr_support : false
            },
            address: {
              id: @switch.address.id,
              fullAddress: @switch.address.full_address,
              postcode: @switch.address.postcode
            },
            fromSupplier: determine_from_supplier(@switch),
            toSupplier: determine_to_supplier(@switch),
            status: @switch.status,
            displayStatus: map_status_to_frontend(@switch.status),
            createdAt: @switch.created_at.iso8601,
            updatedAt: @switch.updated_at.iso8601,
            completedAt: @switch.status == 'switched' ? @switch.updated_at.iso8601 : nil,
            failureReason: @switch.status == 'rejected_by_supplier' ? "Switch rejected by supplier" : nil,
            paymentMethod: @switch.payment_method ? {
              id: @switch.payment_method.id,
              accountHolder: @switch.payment_method.account_holder_name,
              accountNumber: @switch.payment_method.account_number&.last(4),
              sortCode: @switch.payment_method.sort_code&.last(4),
              paymentType: @switch.payment_method.payment_type
            } : nil,
            energyTariffs: {
              gas: @switch.gas_energy_tariff ? {
                id: @switch.gas_energy_tariff.id,
                supplierName: @switch.gas_energy_tariff.supplier_name,
                tariffName: @switch.gas_energy_tariff.tariff_name,
                tariffType: @switch.gas_energy_tariff.tariff_type,
                standingCharge: @switch.gas_energy_tariff.standing_charge,
                unitRate: @switch.gas_energy_tariff.unit_rate,
                paymentMethod: @switch.gas_energy_tariff.payment_method,
                meterSerialNumber: @switch.gas_energy_tariff.meter_serial_number,
                meterPointReferenceNumber: @switch.gas_energy_tariff.meter_point_reference_number,
                annualConsumption: @switch.gas_energy_tariff.gas_estimated_annual_usage.to_f,
                estimatedYearlyCost: @switch.gas_energy_tariff ? @switch.gas_energy_tariff.calculate_costs('gas').first : 0,
                estimatedMonthlyCost: @switch.gas_energy_tariff ? @switch.gas_energy_tariff.calculate_costs('gas').last : 0
              } : nil,
              electricity: @switch.electricity_energy_tariff ? {
                id: @switch.electricity_energy_tariff.id,
                supplierName: @switch.electricity_energy_tariff.supplier_name,
                tariffName: @switch.electricity_energy_tariff.tariff_name,
                tariffType: @switch.electricity_energy_tariff.tariff_type,
                standingCharge: @switch.electricity_energy_tariff.standing_charge,
                unitRate: @switch.electricity_energy_tariff.unit_rate,
                paymentMethod: @switch.electricity_energy_tariff.payment_method,
                meterSerialNumber: @switch.electricity_energy_tariff.meter_serial_number,
                meterPointAdministrationNumber: @switch.electricity_energy_tariff.meter_point_administration_number,
                annualConsumption: @switch.electricity_energy_tariff.electricity_est_annual_usage.to_f,
                estimatedYearlyCost: @switch.electricity_energy_tariff ? @switch.electricity_energy_tariff.calculate_costs('electricity').first : 0,
                estimatedMonthlyCost: @switch.electricity_energy_tariff ? @switch.electricity_energy_tariff.calculate_costs('electricity').last : 0
              } : nil
            },
            switchingToTariff: @switch.switching_to_tariff ? {
              id: @switch.switching_to_tariff.id,
              supplierName: @switch.switching_to_tariff.try(:supplier_name),
              tariffName: @switch.switching_to_tariff.try(:tariff_name),
              tariffType: @switch.switching_to_tariff.try(:tariff_type),
              fuelType: @switch.switching_to_tariff.try(:energy_type),
              estimatedSavings: @switch.switching_to_tariff.calculate_estimated_saving(
                @switch.gas_energy_tariff,
                @switch.electricity_energy_tariff
              )[:total]&.dig(:amount) || 0,
              paymentMethods: @switch.switching_to_tariff.payment_methods
            } : nil,
            supplierSubmissions: @switch.supplier_submissions.order(created_at: :desc).map do |submission|
              {
                id: submission.id,
                supplierName: submission.supplier.name,
                submissionType: submission.submission_type,
                status: submission.status,
                statusSummary: submission.status_summary,
                submittedAt: submission.submitted_at&.iso8601,
                processedAt: submission.processed_at&.iso8601,
                attemptNumber: submission.attempt_number,
                supplierReference: submission.supplier_reference,
                errorMessage: submission.error_message,
                rejectionReason: submission.rejection_reason
              }
            end
          }
          
          render json: switch_data
        end
        
        # Support OPTIONS request for CORS
        def options
          head :ok
        end
        
        private
        
        def map_status_to_frontend(status)
          case status.to_s
          when 'draft'
            'pending'
          when 'confirmed'
            'confirmed'
          when 'submitted_to_supplier'
            'submitted_to_supplier'
          when 'supplier_processing'
            'supplier_processing'
          when 'switched'
            'completed'
          when 'rejected_by_supplier'
            'failed'
          else
            'pending'
          end
        end
        
        def determine_from_supplier(switch)
          # Get supplier info from the energy tariffs
          suppliers = []
          if switch.gas_energy_tariff.present?
            if switch.gas_energy_tariff.respond_to?(:supplier_name)
              suppliers << switch.gas_energy_tariff.supplier_name
            elsif switch.gas_energy_tariff.respond_to?(:supplier)
              suppliers << switch.gas_energy_tariff.supplier&.name
            end
          end
          
          if switch.electricity_energy_tariff.present?
            if switch.electricity_energy_tariff.respond_to?(:supplier_name)
              suppliers << switch.electricity_energy_tariff.supplier_name
            elsif switch.electricity_energy_tariff.respond_to?(:supplier)
              suppliers << switch.electricity_energy_tariff.supplier&.name
            end
          end
          
          suppliers.compact.first || "Unknown Supplier"
        end
        
        def determine_to_supplier(switch)
          # Get the supplier they're switching to
          if switch.switching_to_tariff.present?
            if switch.switching_to_tariff.respond_to?(:supplier_name)
              return switch.switching_to_tariff.supplier_name
            elsif switch.switching_to_tariff.respond_to?(:supplier)
              return switch.switching_to_tariff.supplier&.name
            end
          end
          
          "Unknown Supplier"
        end
      end
    end
  end
end 