module Api
  module V1
    module Admin
      class SwitchUsersController < Api::V1::ApiController
        before_action :authorize_admin

        def index
          # Start with base query
          @users = SwitchUser.order(created_at: :desc)
          
          # Apply search if provided
          if params[:search].present?
            search_term = "%#{params[:search]}%"
            @users = @users.where("email ILIKE ? OR first_name ILIKE ? OR last_name <PERSON><PERSON><PERSON> ? OR phone_number ILIKE ?", 
                                 search_term, search_term, search_term, search_term)
          end
          
          # Apply filters if provided
          if params[:email].present?
            @users = @users.where("email LIKE ?", "%#{params[:email]}%")
          end
          
          if params[:name].present?
            @users = @users.where("name LIKE ?", "%#{params[:name]}%")
          end
          
          # Apply PSR support filter
          if params[:psr_support].present?
            if params[:psr_support] == 'required'
              @users = @users.where(requires_psr_support: true)
            elsif params[:psr_support] == 'not-required'
              @users = @users.where(requires_psr_support: false)
            end
          end
          
          # Apply status filter
          if params[:status].present?
            status = params[:status].downcase
            case status
            when 'pending'
              # Find users with pending switches
              @users = @users.joins(:energy_switches).where(energy_switches: { status: ['draft', 'confirmed', 'submitted_to_supplier', 'supplier_processing'] }).distinct
            when 'completed'
              # Find users with completed switches
              @users = @users.joins(:energy_switches).where(energy_switches: { status: 'switched' }).distinct
            when 'cancelled'
              # Find users with cancelled switches
              @users = @users.joins(:energy_switches).where(energy_switches: { status: 'cancelled' }).distinct
            when 'failed'
              # Find users with failed switches
              @users = @users.joins(:energy_switches).where(energy_switches: { status: 'rejected_by_supplier' }).distinct
            end
          end
          
          # Use pagy for pagination
          limit = [(params[:limit].presence || 25).to_i, 100].min
          page = (params[:page] || 1).to_i
          @pagy, @users = pagy(@users, limit: limit, page: page)
          
          # Format the response to match original format
          render json: {
            users: @users,
            total: @pagy.count,
            page: @pagy.page,
            limit: limit,
            total_pages: @pagy.pages
          }
        end

        def show
          @user = SwitchUser.includes(:addresses, :energy_switches, :payment_methods)
                           .find(params[:id])
          
          # Build comprehensive user data
          user_data = {
            id: @user.id,
            email: @user.email,
            firstName: @user.first_name,
            lastName: @user.last_name,
            fullName: @user.respond_to?(:full_name) ? @user.full_name : "#{@user.first_name} #{@user.last_name}",
            phoneNumber: @user.phone_number,
            requiresPsrSupport: @user.respond_to?(:requires_psr_support) ? @user.requires_psr_support : false,
            createdAt: @user.created_at.iso8601,
            updatedAt: @user.updated_at.iso8601,
            addresses: @user.addresses.map do |address|
              {
                id: address.id,
                fullAddress: address.full_address,
                postcode: address.postcode,
                createdAt: address.created_at.iso8601
              }
            end,
            payment_methods: @user.payment_methods.map do |payment|
              {
                id: payment.id,
                accountHolder: payment.account_holder_name,
                accountNumber: payment.account_number&.last(4),
                sortCode: payment.sort_code&.last(4),
                paymentType: payment.payment_type,
                createdAt: payment.created_at.iso8601
              }
            end,
            energy_switches: @user.energy_switches.order(created_at: :desc).map do |switch|
              {
                id: switch.id,
                reference_number: switch.reference_number,
                status: switch.status,
                displayStatus: map_status_to_frontend(switch.status),
                createdAt: switch.created_at.iso8601,
                completedAt: switch.status == 'switched' ? switch.updated_at.iso8601 : nil,
                fromSupplier: determine_from_supplier(switch),
                toSupplier: determine_to_supplier(switch)
              }
            end
          }
          
          render json: user_data
        end
        
        private
        
        def map_status_to_frontend(status)
          case status.to_s
          when 'draft'
            'pending'
          when 'confirmed', 'submitted_to_supplier', 'supplier_processing'
            'pending'
          when 'switched'
            'completed'
          when 'rejected_by_supplier'
            'failed'
          else
            'pending'
          end
        end
        
        def determine_from_supplier(switch)
          # Get supplier info from the energy tariffs
          suppliers = []
          if switch.gas_energy_tariff.present?
            if switch.gas_energy_tariff.respond_to?(:supplier_name)
              suppliers << switch.gas_energy_tariff.supplier_name
            elsif switch.gas_energy_tariff.respond_to?(:supplier)
              suppliers << switch.gas_energy_tariff.supplier&.name
            end
          end
          
          if switch.electricity_energy_tariff.present?
            if switch.electricity_energy_tariff.respond_to?(:supplier_name)
              suppliers << switch.electricity_energy_tariff.supplier_name
            elsif switch.electricity_energy_tariff.respond_to?(:supplier)
              suppliers << switch.electricity_energy_tariff.supplier&.name
            end
          end
          
          suppliers.compact.first || "Unknown Supplier"
        end
        
        def determine_to_supplier(switch)
          # Get the supplier they're switching to
          if switch.switching_to_tariff.present?
            if switch.switching_to_tariff.respond_to?(:supplier_name)
              return switch.switching_to_tariff.supplier_name
            elsif switch.switching_to_tariff.respond_to?(:supplier)
              return switch.switching_to_tariff.supplier&.name
            end
          end
          
          "Unknown Supplier"
        end
      end
    end
  end
end
