module Api
  module V1
    module Admin
      class SecurityEventsController < Api::V1::ApiController
        before_action :authorize_admin
        
        def index
          # Build query with filters
          @events = SecurityEvent.order(created_at: :desc)
          
          # Apply search if provided
          if params[:search].present?
            search_term = "%#{params[:search]}%"
            @events = @events.where("email ILIKE ? OR event_type ILIKE ? OR ip_address ILIKE ?", 
                                  search_term, search_term, search_term)
          end
          
          # Apply filters if provided
          if params[:event_type].present? && params[:event_type] != "all"
            @events = @events.by_type(params[:event_type])
          end
          
          if params[:email].present?
            @events = @events.by_email(params[:email])
          end
          
          if params[:ip_address].present?
            @events = @events.by_ip(params[:ip_address])
          end
          
          if params[:start_date].present?
            start_date = Date.parse(params[:start_date]) rescue nil
            @events = @events.where('created_at >= ?', start_date.beginning_of_day) if start_date
          end
          
          if params[:end_date].present?
            end_date = Date.parse(params[:end_date]) rescue nil
            @events = @events.where('created_at <= ?', end_date.end_of_day) if end_date
          end
          
          # Use pagy for pagination
          limit = [(params[:limit].presence || 25).to_i, 100].min
          page = (params[:page] || 1).to_i
          @pagy, @events = pagy(@events, limit: limit, page: page)
          
          # Format the response to match original format
          render json: {
            events: @events,
            total: @pagy.count,
            page: @pagy.page,
            limit: limit,
            total_pages: @pagy.pages
          }
        end
        
        def show
          @event = SecurityEvent.find(params[:id])
          render json: @event
        end
        
        # Support OPTIONS request for CORS
        def options
          head :ok
        end
      end
    end
  end
end 