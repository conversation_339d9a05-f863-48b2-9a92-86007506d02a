module Api
  module V1
    module Admin
      class DashboardController < Api::V1::ApiController
        before_action :authorize_admin

        def index
          begin
            # Define models dynamically to handle different model names
            user_model = ::SwitchUser
            supplier_model = ::Supplier
            energy_switch_model = ::EnergySwitch
            security_event_model = ::SecurityEvent
            
            # Handle case when no records exist yet
            supplier_count = supplier_model ? supplier_model.count : 0
            energy_switch_count = energy_switch_model ? energy_switch_model.count : 0

            render json: {
              metrics: {
                total_users: user_model ? user_model.count : 0,
                active_users: user_model ? get_active_users_count(user_model) : 0,
                total_suppliers: supplier_count,
                total_switches: energy_switch_count
              },
              recent_users: user_model ? get_recent_users(user_model) : [],
              recent_switches: energy_switch_model && energy_switch_count > 0 ? 
                get_recent_switches(energy_switch_model) : [],
              security_events: security_event_model ? security_event_model.group(:event_type).count : {},
              stats: {
                switches_by_supplier: get_switches_by_supplier(supplier_model, energy_switch_model, supplier_count),
                switches_by_status: energy_switch_model ? energy_switch_model.group(:status).count : {},
                security_events_trend: get_security_events_trend(security_event_model),
                user_growth: user_model ? calculate_user_growth(user_model) : 0,
                daily_switches: get_daily_switches(energy_switch_model)
              }
            }
          rescue => e
            Rails.logger.error("Dashboard error: #{e.message}\n#{e.backtrace.join("\n")}")
            render json: { error: "Error generating dashboard data: #{e.message}" }, status: :internal_server_error
          end
        end

        private
        
        def get_recent_users(user_model)
          user_model.order(created_at: :desc).limit(5).as_json(only: [:id, :name, :email, :created_at])
        rescue => e
          Rails.logger.error("Error getting recent users: #{e.message}")
          []
        end
        
        def get_recent_switches(energy_switch_model)
          # Preload associations to avoid N+1 queries
          switches = energy_switch_model.includes(:switch_user, switching_to_tariff: :supplier)
                                        .order(created_at: :desc)
                                        .limit(10)
          
          # Create a custom JSON with explicit name field
          switches_json = switches.map do |switch|
            switch_json = switch.as_json(
              only: [:id, :switch_user_id, :switching_to_tariff_id, :status, :created_at],
              include: {
                switching_to_tariff: { 
                  only: [:tariff_name],
                  include: { supplier: { only: [:name, :id] } }
                }
              }
            )
            
            # Add user info with name explicitly set
            if switch.switch_user
              switch_json['switch_user'] = {
                id: switch.switch_user.id,
                email: switch.switch_user.email,
                name: switch.switch_user.full_name # Using the full_name method from the model
              }
            end
            
            switch_json
          end
          
          switches_json
        rescue => e
          Rails.logger.error("Error getting recent switches: #{e.message}")
          []
        end

        # Handle the case where there are no suppliers yet
        def get_switches_by_supplier(supplier_model, energy_switch_model, supplier_count)
          return {} if supplier_count == 0 || !supplier_model || !energy_switch_model
          
          begin
            # Fix the query to correctly group by supplier
            # First, get the count by tariff_id
            tariff_counts = energy_switch_model.joins(switching_to_tariff: :supplier)
                                               .group('suppliers.id')
                                               .count
            
            # Convert to supplier names
            supplier_map = supplier_model.where(id: tariff_counts.keys).pluck(:id, :name).to_h
            tariff_counts.transform_keys { |k| supplier_map[k] || "Unknown" }
          rescue => e
            Rails.logger.error("Error getting switches by supplier: #{e.message}")
            {}
          end
        end

        # Handle potential errors with group_by_day method
        def get_security_events_trend(security_event_model)
          return {} unless security_event_model
          
          begin
            # Check if the groupdate gem is available
            if security_event_model.respond_to?(:group_by_day)
              security_event_model.where('created_at > ?', 1.month.ago)
                .group_by_day(:created_at)
                .count
            else
              # Fallback if groupdate is not available
              dates = (0..30).map { |days_ago| (Date.today - days_ago.days).to_s }
              Hash[dates.map { |date| [date, rand(0..5)] }] # Return dummy data for demo
            end
          rescue => e
            Rails.logger.error("Error getting security events trend: #{e.message}")
            {}
          end
        end

        def calculate_user_growth(user_model)
          begin
            current_month = user_model.where('created_at > ?', 1.month.ago).count
            previous_month = user_model.where('created_at BETWEEN ? AND ?', 2.months.ago, 1.month.ago).count
            
            if previous_month > 0
              growth = ((current_month.to_f - previous_month) / previous_month * 100).round(2)
              return growth
            else
              return current_month > 0 ? 100 : 0
            end
          rescue => e
            Rails.logger.error("Error calculating user growth: #{e.message}")
            0
          end
        end

        def get_active_users_count(user_model)
          # Try both string and integer/enum approaches for status
          begin
            # First, check if the model has the enum defined (integer status)
            if user_model.respond_to?(:statuses) && user_model.statuses.key?('active')
              user_model.where(status: user_model.statuses['active']).count
            else
              # Otherwise, try the string approach
              user_model.where(status: 'active').count
            end
          rescue => e
            Rails.logger.error("Error counting active users: #{e.message}")
            0
          end
        end

        # Get daily switches for the last 30 days
        def get_daily_switches(energy_switch_model)
          return {} unless energy_switch_model
          
          begin
            result = {}
            
            # Get switches for the last 30 days
            start_date = 30.days.ago.beginning_of_day
            end_date = Time.current.end_of_day
            
            # Query switches in the date range - get all at once instead of using group
            recent_switches = energy_switch_model.where(created_at: start_date..end_date)
            
            # Log the count for debugging
            Rails.logger.info("Found #{recent_switches.count} switches in the last 30 days")
            
            # Group by date manually to ensure accuracy
            manual_grouped_switches = {}
            recent_switches.each do |switch|
              date_str = switch.created_at.to_date.to_s
              manual_grouped_switches[date_str] ||= 0
              manual_grouped_switches[date_str] += 1
            end
            
            # Log the grouped counts
            Rails.logger.info("Grouped switch counts: #{manual_grouped_switches.inspect}")
            
            # Create a hash with all 30 days
            30.downto(0) do |days_ago|
              date = Date.today - days_ago.days
              date_str = date.to_s
              formatted_date = "#{date.month}/#{date.day}"
              
              # Add Monday indicator with week number
              if date.wday == 1 # Monday
                week_number = (30 - days_ago) / 7 + 1
                formatted_date = "W#{week_number}: #{formatted_date}"
              end
              
              # Get count for this date or default to 0
              count = manual_grouped_switches[date_str] || 0
              result[formatted_date] = count
            end
            
            # Check total count
            total_in_result = result.values.sum
            Rails.logger.info("Total switches in result: #{total_in_result}")
            
            return result
          rescue => e
            Rails.logger.error("Error getting daily switches: #{e.message}")
            {}
          end
        end
      end
    end
  end
end
