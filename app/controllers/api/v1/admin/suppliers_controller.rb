module Api
  module V1
    module Admin
      class SuppliersController < Api::V1::ApiController
        # skip_before_action :authorize_request
        before_action :authorize_admin

        def index
          # Apply pagination
          limit = [(params[:limit].presence || 25).to_i, 100].min
          page = (params[:page] || 1).to_i
          
          @suppliers = Supplier.order(name: :asc)
          
          # Apply search filters if provided
          if params[:search].present?
            @suppliers = @suppliers.where("name ILIKE ?", "%#{params[:search]}%")
          elsif params[:name].present?
            @suppliers = @suppliers.where("name ILIKE ?", "%#{params[:name]}%")
          end
          
          # Use pagy for pagination
          @pagy, @paginated_suppliers = pagy(@suppliers, limit: limit, page: page)
          
          # Format response
          suppliers_data = @paginated_suppliers.map do |supplier|
            {
              id: supplier.id,
              name: supplier.name,
              logo_url: '', #supplier.logo,
              description: supplier.about,
              trustpilot_rating: supplier.trustpilot_rating,
              website_url: '#', #supplier.website_url,
              tariffsCount: supplier.energy_tariffs.count,
              createdAt: supplier.created_at.iso8601,
              updatedAt: supplier.updated_at.iso8601
            }
          end
          
          render json: {
            suppliers: suppliers_data,
            total: @pagy.count,
            page: @pagy.page,
            limit: limit,
            total_pages: @pagy.pages
          }
        end

        def show
          @supplier = Supplier.includes(energy_tariffs: :energy_tariff_rates).find(params[:id])
          
          # Format the supplier data with detailed energy tariff information
          supplier_data = {
            id: @supplier.id,
            name: @supplier.name,
            logo_url: @supplier.logo,
            description: @supplier.about,
            trustpilot_rating: @supplier.trustpilot_rating,
            website_url: '', #@supplier.website_url,
            createdAt: @supplier.created_at.iso8601,
            updatedAt: @supplier.updated_at.iso8601,
            energyTariffs: @supplier.energy_tariffs.map do |tariff|
              {
                id: tariff.id,
                tariffName: tariff.tariff_name,
                energyType: tariff.energy_type,
                tariffType: tariff.tariff_type,
                readableTariffType: tariff.readable_tariff_type,
                productCode: tariff.product_code,
                availableFrom: tariff.available_from&.iso8601,
                availableTo: tariff.available_to&.iso8601,
                exitFees: tariff.exit_fees,
                paymentMethods: tariff.payment_methods,
                createdAt: tariff.created_at.iso8601,
                updatedAt: tariff.updated_at.iso8601,
                tariffRates: tariff.energy_tariff_rates.map do |rate|
                  {
                    id: rate.id,
                    fuelType: rate.fuel_type,
                    gspCode: rate.gsp_code,
                    profileClass: rate.profile_class,
                    standingChargeIncVat: rate.standing_charge_inc_vat,
                    unitRateIncVat: rate.unit_rate_inc_vat,
                    dayUnitRateIncVat: rate.day_unit_rate_inc_vat,
                    nightUnitRateIncVat: rate.night_unit_rate_inc_vat,
                    createdAt: rate.created_at.iso8601,
                    updatedAt: rate.updated_at.iso8601
                  }
                end
              }
            end
          }
          
          render json: supplier_data
        end
      end
    end
  end
end
 