module Api
  module V1
    class OnboardingController < Api::V1::ApiController
      before_action :authorize_trusted_request

      def create
        ActiveRecord::Base.transaction do
          switch_user = process_switch_user
          address = process_address(switch_user)

          # Check for existing draft energy switch
          existing_switch = EnergySwitch.with_matching_meters(
            switch_user: switch_user,
            gas_msn: gas_params&.dig("meter_serial_number"),
            electricity_msn: electricity_params&.dig("meter_serial_number")
          ).first

          if existing_switch
            existing_switch.bill_url = bill_params["bill_url"]
            existing_switch.raw_bill_json = onboarding_params
            existing_switch.save!

            json_response({ onboarding_url: generate_onboarding_url(existing_switch) }, :ok)
            return
          end

          energy_switch = switch_user.energy_switches.build(
            status: :draft,
            address_id: address.id
          )

          energy_switch.bill_url = bill_params["bill_url"]
          energy_switch.raw_bill_json = onboarding_params

          energy_switch = process_user_tariffs(energy_switch)

          json_response(
            { onboarding_url: generate_onboarding_url(energy_switch) },
            :created
          )
        end
      rescue ActiveRecord::RecordInvalid => e
        # Log the specific error for debugging but send a generic message to the client
        Rails.logger.error("Onboarding validation error: #{e.message}")
        json_response({ error: "Validation failed. Please check your information and try again." }, :unprocessable_entity)
      rescue StandardError => e
        # Log the full error details but only send generic message to client
        Rails.logger.error("Onboarding error: #{e.message}\n#{e.backtrace.join("\n")}")
        json_response({ error: "An error occurred during processing" }, :unprocessable_entity)
      end

      # Protected by bot access token
      def send_verification_code
        email = onboarding_params[:email]&.downcase

        if email.blank?
          json_response({ error: "Email is required" }, :unprocessable_entity)
          return
        end

        unless EmailValidator.valid?(email)
          json_response({ error: "Invalid email format" }, :unprocessable_entity)
          return
        end

        # Rate limiting check - prevent sending too many codes
        send_attempts_key = "verification_send_attempts:#{email}"
        send_attempts = Rails.cache.fetch(send_attempts_key, expires_in: 24.hours) { 0 }

        if send_attempts >= 10
          # Log rate limit trigger in monitoring system
          SecurityMonitoringService.record_rate_limit_triggered(email, request.remote_ip, "verification_code_send")
          
          Rails.logger.warn("Too many verification code requests for email: #{email}")
          json_response({ error: "Too many code requests. Please try again later" }, :too_many_requests)
          return
        end

        # Cooldown period check - prevent sending codes too frequently
        cooldown_key = "verification_cooldown:#{email}"
        last_sent_at = Rails.cache.read(cooldown_key)
        
        if last_sent_at && (Time.now.utc - last_sent_at) < 10 # 10 seconds cooldown
          # Log cooldown trigger in monitoring system
          SecurityMonitoringService.record_rate_limit_triggered(email, request.remote_ip, "verification_cooldown")
          
          json_response({ error: "Please wait before requesting another code" }, :too_many_requests)
          return
        end

        # Use SecureRandom for cryptographically secure random numbers
        code = SecureRandom.random_number(900_000) + 100_000 # 6-digit number

        # Store the code securely with an expiration time
        # Add additional entropy by including a random salt in the cache key
        salt = SecureRandom.hex(8)
        verification_key = "verification_code:#{email}:#{salt}"
        
        # Store both the code and the salt
        Rails.cache.write(verification_key, { code: code, salt: salt }, expires_in: 10.minutes)
        
        # Also store a reference to the latest verification key for this email
        Rails.cache.write("latest_verification_key:#{email}", verification_key, expires_in: 10.minutes)
        
        # Update rate limiting and cooldown trackers
        Rails.cache.write(send_attempts_key, send_attempts + 1, expires_in: 24.hours)
        Rails.cache.write(cooldown_key, Time.now.utc, expires_in: 10.minutes)
        
        # Record this code send in the monitoring system
        SecurityMonitoringService.record_code_send(email, request.remote_ip)
        
        # Log for audit purposes (but don't log the actual code)
        Rails.logger.info("Verification code sent to email: #{email}")
        
        SendOnboardingVerificationCodeJob.perform_async(email, code)

        # Return success without exposing the code
        json_response({ message: "Verification code sent successfully" }, :ok)
      end

      # Protected by bot access token
      def verify_code
        email = verification_params[:email]&.downcase
        submitted_code = verification_params[:code]

        if email.blank? || submitted_code.blank?
          json_response({ error: "Email and verification code are required" }, :unprocessable_entity)
          return
        end

        unless EmailValidator.valid?(email)
          json_response({ error: "Invalid email format" }, :unprocessable_entity)
          return
        end

        # Rate limiting check
        attempts_key = "verification_attempts:#{email}"
        attempts = Rails.cache.fetch(attempts_key, expires_in: 1.hour) { 0 }

        if attempts >= 5
          # Log rate limit trigger in monitoring system
          SecurityMonitoringService.record_rate_limit_triggered(email, request.remote_ip, "verification_attempt")
          
          Rails.logger.warn("Too many verification attempts for email: #{email}")
          json_response({ error: "Too many attempts. Please try again later" }, :too_many_requests)
          return
        end

        # Increment attempts counter
        Rails.cache.write(attempts_key, attempts + 1, expires_in: 1.hour)

        # Retrieve the latest verification key for this email
        latest_verification_key = Rails.cache.read("latest_verification_key:#{email}")
        
        if latest_verification_key.nil?
          # Record failed verification in monitoring
          SecurityMonitoringService.record_verification_attempt(email, request.remote_ip, false)
          
          # Use a consistent error message to prevent email enumeration
          json_response({ error: "Invalid or expired verification code" }, :unprocessable_entity)
          return
        end
        
        # Retrieve stored verification data using the key
        verification_data = Rails.cache.read(latest_verification_key)

        if verification_data.nil?
          # Record failed verification in monitoring
          SecurityMonitoringService.record_verification_attempt(email, request.remote_ip, false)
          
          # Use a consistent error message to prevent email enumeration
          json_response({ error: "Invalid or expired verification code" }, :unprocessable_entity)
          return
        end
        
        stored_code = verification_data[:code]

        # Use constant-time comparison to prevent timing attacks
        if ActiveSupport::SecurityUtils.secure_compare(stored_code.to_s, submitted_code.to_s)
          # Clear the verification data and attempts counter after successful verification
          Rails.cache.delete(latest_verification_key)
          Rails.cache.delete("latest_verification_key:#{email}")
          Rails.cache.delete(attempts_key)
          
          # Record successful verification in monitoring
          SecurityMonitoringService.record_verification_attempt(email, request.remote_ip, true)
          
          json_response({ verified: true }, :ok)
        else
          # Record failed verification in monitoring
          SecurityMonitoringService.record_verification_attempt(email, request.remote_ip, false)
          
          json_response({ error: "Invalid or expired verification code" }, :unprocessable_entity)
        end
      end

      private

      def generate_onboarding_url(energy_switch)
        # Validate server host setting
        server_host = ENV['SERVER_HOST']
        
        unless server_host.present?
          Rails.logger.error("SERVER_HOST environment variable not configured")
          raise StandardError, "Server configuration error"
        end
        
        # Create a JWT token with the energy switch ID embedded
        token = TokenService.generate_onboarding_jwt(energy_switch)

        if token.nil?
          raise StandardError, "Failed to generate onboarding token"
        end
        
        # Return URL that navigates to the NextJS app with token in the hash fragment
        # This keeps the token out of server logs and most browser history logs
        "#{server_host}/onboarding#token=#{token}"
      end

      def process_switch_user
        email = personal_params["email"]&.downcase
        
        # Validate email format
        unless email.present? && EmailValidator.valid?(email)
          raise ActiveRecord::RecordInvalid.new(SwitchUser.new.tap { |u| u.errors.add(:email, "is invalid") })
        end
        
        switch_user = SwitchUser.find_or_initialize_by(email: email)
        
        switch_user.assign_attributes(
          first_name: sanitize_input(personal_params["first_name"]) || switch_user.first_name,
          last_name: sanitize_input(personal_params["last_name"]) || switch_user.last_name,
          phone_number: sanitize_input(personal_params["phone_number"]) || switch_user.phone_number
        )

        switch_user.save!
        switch_user
      end

      def process_address(switch_user)
        # Validate postcode format
        unless valid_postcode?(address_params["postcode"])
          raise ActiveRecord::RecordInvalid.new(Address.new.tap { |a| a.errors.add(:postcode, "is invalid") })
        end

        address = switch_user.addresses.find_or_initialize_by(
          postcode: address_params["postcode"],
          full_address: sanitize_input(address_params["full_address"]&.downcase)
        )

        address.assign_attributes(
          posttown: sanitize_input(address_params["posttown"]),
          street: sanitize_input(address_params["street"]),
          number: sanitize_input(address_params["number"]),
          flat: sanitize_input(address_params["flat"])
        )

        address.save!
        address
      end

      def process_user_tariffs(energy_switch)
        fuel_type = onboarding_params["fuel_type"].to_s.downcase

        # Define a mapping of fuel types to creation methods
        fuel_type_methods = {
          'gas' => -> { energy_switch.gas_energy_tariff = create_gas_tariff(energy_switch.switch_user) },
          'electricity' => -> { energy_switch.electricity_energy_tariff = create_electricity_tariff(energy_switch.switch_user) },
          'both' => -> {
            energy_switch.gas_energy_tariff = create_gas_tariff(energy_switch.switch_user)
            energy_switch.electricity_energy_tariff = create_electricity_tariff(energy_switch.switch_user)
          }
        }

        # Validate fuel type is valid
        unless fuel_type.in?(fuel_type_methods.keys)
          Rails.logger.warn("Invalid fuel type provided: #{fuel_type}")
          fuel_type = 'both' # Default to both if invalid
        end

        # Execute the appropriate method(s) based on fuel type
        fuel_type_methods[fuel_type].call

        energy_switch.save!
        energy_switch
      end

      def create_gas_tariff(switch_user)
        return nil unless gas_params.present?
        
        gas_msn = gas_params["meter_serial_number"]
        
        unless UserTariff.valid_meter_serial_number?(gas_msn)
          raise ActiveRecord::RecordInvalid.new(UserTariff.new.tap { |t| 
            t.errors.add(:meter_serial_number, "must be 6 to 21 alphanumeric characters")
          })
        end
        
        switch_user.user_tariffs.create!(
          energy_type: :gas,
          meter_point_reference_number: gas_params["meter_point_reference_number"],
          meter_serial_number: gas_msn,
          unit_rate: gas_params["unit_rate"],
          standing_charge: gas_params["standing_charge"],
          supplier_name: supplier_params["name"],
          tariff_name: supplier_params["tariff_name"],
          payment_method: gas_params["payment_method"],
          tariff_type: gas_params["product_type"].try(:titleize),
          gas_estimated_annual_usage: gas_params["estimated_annual_usage"],
          gas_estimated_annual_cost: gas_params["estimated_annual_cost"],
          gas_monthly_usage: gas_params["monthly_usage"],
          exit_fees: gas_params["early_exit_fee"]
        )
      end

      def create_electricity_tariff(switch_user)
        return nil unless electricity_params.present?
        
        electricity_msn = electricity_params["meter_serial_number"]
        
        unless UserTariff.valid_meter_serial_number?(electricity_msn)
          raise ActiveRecord::RecordInvalid.new(UserTariff.new.tap { |t| 
            t.errors.add(:meter_serial_number, "must be 6 to 21 alphanumeric characters")
          })
        end

        mpan = '-'
        gsp_code = nil
        profile_class = nil

        begin
          electricity_xoserve_api = XoserveElectricityApiService.new
          result = electricity_xoserve_api.search_utility_address(electricity_msn)

          mpan = result[:mpan]
          gsp_code = result[:gsp_code]

          technical_details = electricity_xoserve_api.get_technical_details_by_mpan(mpan)
          profile_class = technical_details[:profile_class]
        rescue StandardError => e
          Rails.logger.error("Error fetching electricity details: #{e.message}")
        end

        # Create the tariff first
        tariff = switch_user.user_tariffs.create!(
          energy_type: :electricity,
          gsp_code: gsp_code,
          profile_class: profile_class,
          meter_point_administration_number: mpan,
          meter_serial_number: electricity_msn,
          unit_rate: electricity_params["unit_rate"],
          standing_charge: electricity_params["standing_charge"],
          supplier_name: supplier_params["name"],
          tariff_name: supplier_params["tariff_name"],
          payment_method: electricity_params["payment_method"],
          tariff_type: electricity_params["product_type"].try(:titleize),
          electricity_est_annual_usage: electricity_params["estimated_annual_usage"],
          electricity_est_annual_cost: electricity_params["estimated_annual_cost"],
          electricity_monthly_usage: electricity_params["monthly_usage"],
          exit_fees: electricity_params["early_exit_fee"]
        )

        # Schedule the job after tariff creation if there was an error
        if gsp_code.nil? || profile_class.nil?
          FetchElectricityDetailsJob.perform_async(
            switch_user.id,
            tariff.meter_serial_number,
            tariff.id
          )
        end

        tariff
      end

      def verification_params
        params.require(:verification).permit(:email, :code)
      end

      def onboarding_params
        params.require(:onboarding).permit(
          :fuel_type,
          :email,
          personal: [:email, :first_name, :last_name, :phone_number],
          address: [:full_address, :postcode, :posttown, :street, :number, :flat],
          bill: [:account_number, :bill_date, :bill_reference, :billing_period, 
                 :amount_payable, :percentage_of_vat_applied, :bill_url],
          supplier: [:name, :product_type, :tariff_name],
          electricity: [:meter_serial_number, :unit_rate, :standing_charge, 
                       :estimated_annual_usage, :estimated_annual_cost,
                       :monthly_usage, :payment_method, 
                       :product_type, :early_exit_fee],
          gas: [:meter_serial_number, :meter_point_reference_number, :unit_rate, 
               :standing_charge, :estimated_annual_usage, :estimated_annual_cost,
               :monthly_usage, :payment_method, :product_type, :early_exit_fee]
        ).tap do |whitelisted|
          # Allow gas parameter to be nil
          whitelisted[:gas] = nil if params[:onboarding][:gas].nil?
          # Allow electricity parameter to be nil
          whitelisted[:electricity] = nil if params[:onboarding][:electricity].nil?
        end
      end

      # Helper methods to access nested params
      def personal_params
        onboarding_params["personal"] || {}
      end

      def address_params
        onboarding_params["address"] || {}
      end

      def bill_params
        onboarding_params["bill"] || {}
      end

      def supplier_params
        onboarding_params["supplier"] || {}
      end

      def electricity_params
        onboarding_params["electricity"]
      end

      def gas_params
        onboarding_params["gas"]
      end

      # Additional security methods
      def sanitize_input(input)
        return nil if input.nil?
        input.to_s.strip
      end

      def valid_postcode?(postcode)
        return false if postcode.blank?
        # Add UK postcode validation regex or use a gem
        postcode.present? && postcode.match?(/^[A-Z]{1,2}\d[A-Z\d]? ?\d[A-Z]{2}$/i)
      end
    end
  end
end
