module Api
  module V1
    class ApiController < ApplicationController
      include ExceptionHandler
      include Pagy::Backend

      # Request from trusted applications e.g. <PERSON><PERSON><PERSON>
      def authorize_trusted_request
        token = extract_token_from_header
        return true if TokenService.verify_token_scope(token, 'chatbot', 'ChatBot')
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
      
      # Authorize admin access (for admin dashboard and administrative endpoints)
      def authorize_admin
        token = extract_token_from_header
        
        # Check for admin token
        return true if TokenService.verify_token_scope(token, 'admin', 'Admin')
        
        render json: { error: 'Unauthorized: Admin access required' }, status: :unauthorized
      end

      # Authorize onboarding access (for onboarding flow)
      def authorize_onboarding(resource_owner_id = nil)
        token = extract_token_from_header
        return true if TokenService.verify_onboarding_jwt(token)
        render json: { error: 'Unauthorized: Onboarding access required' }, status: :unauthorized
      end
      
      # Authorize chatbot access (for chatbot-specific endpoints)
      def authorize_chatbot
        token = extract_token_from_header
        return true if TokenService.verify_token_scope(token, 'chatbot', 'ChatBot')
        render json: { error: 'Unauthorized: ChatBot access required' }, status: :unauthorized
      end
    
      def json_response(object, status = :ok)
        return head(status == :ok ? :no_content : status) if object.nil?
    
        render(json: object, status: status)
      end
    
      def unprocessable_entity(errors = [])
        json_response(
          {
            errors: errors.is_a?(Hash) ? [errors] : errors,
          },
          :unprocessable_entity
        )
      end
    
      def handle_unauthorized
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end
    
      private
    
      def extract_token_from_header
        # First check Authorization header
        auth_header = request.headers['Authorization']
        
        if auth_header.present?
          # Try Bearer token format
          if auth_header.start_with?('Bearer ')
            return auth_header.split(' ').last
          end
          
          # If it doesn't start with 'Bearer ', maybe the token is sent directly
          if auth_header !~ /\s/ # No whitespace means it's probably a raw token
            return auth_header
          end
        end
        
        # Check token header (some clients use this instead of Authorization)
        token_header = request.headers['Token'] || request.headers['Access-Token'] || request.headers['AccessToken']
        if token_header.present?
          return token_header
        end
        
        # Then check URL parameter
        params['access_token'] || params['token']
      end
    end
  end
end
