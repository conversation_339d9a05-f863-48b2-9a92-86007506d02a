module ExceptionHandler
  extend ActiveSupport::Concern

  class AuthenticationError < StandardError; end

  included do
    rescue_from ExceptionHandler::AuthenticationError, with: :unauthorized_request
    rescue_from ActionController::ParameterMissing, with: :bad_request
    rescue_from ActiveRecord::RecordNotFound do |e|
      render(json: { message: e.message }, status: :not_found)
    end
  end

  private

  def bad_request(error)
    json_response({ message: error.message }, :bad_request)
  end

  def unauthorized_request(error)
    json_response({ message: error.message }, :unauthorized)
  end
end
