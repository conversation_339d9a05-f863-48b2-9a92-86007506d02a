class FetchElectricityDetailsJob
  include Sidekiq::Job

  sidekiq_options retry: 2

  queue_as :priority

  def perform(switch_user_id, meter_serial_number, tariff_id)
    switch_user = SwitchUser.find(switch_user_id)
    electricity_tariff = switch_user.user_tariffs.find(tariff_id)

    electricity_service = XoserveElectricityApiService.new
    address_result = electricity_service.search_utility_address(meter_serial_number)

    mpan = address_result[:mpan]
    gsp_code = address_result[:gsp_code]

    technical_details = electricity_service.get_technical_details_by_mpan(mpan)
    profile_class = technical_details[:profile_class]

    electricity_tariff.update!(
      gsp_code: gsp_code,
      profile_class: profile_class,
      meter_point_administration_number: mpan
    )
  end
end