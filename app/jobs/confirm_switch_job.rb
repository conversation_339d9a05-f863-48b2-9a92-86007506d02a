class ConfirmSwitchJob
  include Sidekiq::Job

  queue_as :default

  def perform(energy_switch_id, params = {})
    energy_switch = EnergySwitch.find(energy_switch_id)

    Rails.logger.info "Confirming switch #{energy_switch.reference_number}"

    case energy_switch.status
    when 'draft'
      result = ProcessSwitchService.new(energy_switch, params).run
      unless result.success?
        Rails.logger.error "Switch confirmation failed: #{result.data[:message]}"
        Rails.logger.error "Details: #{result.data[:error]}"
        raise StandardError, result.data[:message]
      else
        Rails.logger.info "Switch #{energy_switch.reference_number} confirmed"
      end
    when 'confirmed'
      Rails.logger.info "Switch #{energy_switch.reference_number} already confirmed, skipping!"
    else
      Rails.logger.error "Switch #{energy_switch.reference_number} cannot be confirmed in status: #{energy_switch.status}, skipping!"
    end
  end
end
