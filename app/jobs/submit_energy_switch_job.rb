class SubmitEnergySwitchJob
  include Sidekiq::Job

  sidekiq_options retry: 5, queue: :priority
  
  sidekiq_retry_in do |count|
    calculate_retry_delay(count)
  end

  def calculate_retry_delay(count)
    # Exponential backoff: 5min, 25min, 2h 5min, 10h 25min, 52h 5min
    (count ** 2) * 5 * 60
  end

  def perform(energy_switch_id)
    energy_switch = EnergySwitch.find(energy_switch_id)

    supplier = energy_switch.switching_to_tariff&.supplier
    if supplier.nil?
      error_message = "Energy switch #{energy_switch_id} has no associated supplier"
      Rails.logger.error(error_message)
      return
    end

    # Check for a valid submission that hasn't been processed yet
    valid_submission = energy_switch.supplier_submissions.pending_or_submitted_or_successful.first
    if valid_submission
      Rails.logger.info("Energy switch #{energy_switch_id} already has a valid submission (ID: #{valid_submission.id}, status: #{valid_submission.status}). No need for a new submission.")
      return
    end
    
    case supplier.name
    when "Tulo Energy"
      TuloSwitchSubmissionService.new(energy_switch).submit
    else
      error_message = "Unsupported supplier: #{supplier.name}"
      Rails.logger.error(error_message)
      
      # Create a failed submission record
      energy_switch.supplier_submissions.create!(
        supplier: supplier,
        submission_type: 'switch_request',
        status: :failed,
        error_message: error_message
      )
      
      raise error_message
    end
  end
end 