class ResubmitPendingSwitchesJob
  include Sidekiq::Job

  sidekiq_options retry: 0

  def perform
    resubmit_confirmed_without_submissions
    resubmit_failed_or_rejected
  end

  private

  def resubmit_confirmed_without_submissions
    # Find confirmed energy switches without any supplier submissions
    energy_switches = EnergySwitch.where(status: :confirmed)
                                 .where.not(id: SupplierSubmission.select(:energy_switch_id))
                                 
    return if energy_switches.empty?

    Rails.logger.info("Found #{energy_switches.count} confirmed energy switches without supplier submissions")

    # Submit each switch to supplier
    energy_switches.each do |energy_switch|
      Rails.logger.info("Resubmitting energy switch #{energy_switch.id} to supplier")
      SubmitEnergySwitchJob.perform_async(energy_switch.id)
    end
  end

  def resubmit_failed_or_rejected
    # Find energy switches with failed or rejected submissions and no pending submissions
    # Only consider switches that are still in submitted_to_supplier or rejected_by_supplier status
    eligible_statuses = [:submitted_to_supplier, :rejected_by_supplier]
    
    # First, get IDs of switches that have failed/rejected submissions but no pending/submitted/successful ones
    switches_with_failures = SupplierSubmission.where(status: [:failed, :rejected]).pluck(:energy_switch_id).uniq
    switches_with_pending = SupplierSubmission.pending_or_submitted_or_successful.pluck(:energy_switch_id).uniq
    
    # Find eligible switch IDs (those with failures but no pending submissions)
    eligible_switch_ids = switches_with_failures - switches_with_pending
    
    # Query eligible switches with the correct status
    energy_switches = EnergySwitch.where(id: eligible_switch_ids, status: eligible_statuses)
    
    return if energy_switches.empty?

    Rails.logger.info("Found #{energy_switches.count} energy switches with failed or rejected submissions")

    # Submit each switch to supplier again
    energy_switches.each do |energy_switch|
      # Get the latest submission to check how many retry attempts
      latest_submission = energy_switch.supplier_submissions.order(created_at: :desc).first
      
      # Skip if we've already tried too many times (5 is the default retry limit for SubmitEnergySwitchJob)
      if latest_submission && latest_submission.attempt_number.to_i >= 5
        Rails.logger.info("Skipping energy switch #{energy_switch.id} as it has reached the maximum retry attempts")
        next
      end
      
      Rails.logger.info("Resubmitting failed/rejected energy switch #{energy_switch.id} to supplier")
      SubmitEnergySwitchJob.perform_async(energy_switch.id)
    end
  end
end
