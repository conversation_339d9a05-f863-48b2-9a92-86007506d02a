class SendSecurityAlertJob
  include Sidekiq::Job

  queue_as :priority

  def perform(message)
    Rails.logger.error("SECURITY ALERT: #{message}")
      
    # Slack notification (requires slack-notifier gem)
    if defined?(Slack) && Slack.is_a?(<PERSON>dule) && ENV['SLACK_SECURITY_WEBHOOK_URL'].present?
      begin
        notifier = Slack::Notifier.new(ENV['SLACK_SECURITY_WEBHOOK_URL'])
        notifier.ping("🚨 SECURITY ALERT: #{message}")
      rescue => e
        Rails.logger.error("Failed to send Slack notification: #{e.message}")
      end
    end

    # Send email alert to security team
    if ENV['SECURITY_TEAM_EMAIL'].present?
      begin
        SecurityMailer.alert(message).deliver_now
      rescue => e
        Rails.logger.error("Failed to send security email: #{e.message}")
      end
    end
    
    # Log to external service like Sentry or Bugsnag
    # if defined?(Sentry) && Sentry.is_a?(<PERSON><PERSON><PERSON>)
    #   Sentry.capture_message(message, level: 'fatal')
    # end
  end
end 