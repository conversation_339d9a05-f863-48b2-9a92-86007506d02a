# app/jobs/send_security_report_job.rb
class SendSecurityReportJob
  include Sidekiq::Worker
  
  sidekiq_options queue: 'security', retry: 3
  
  def perform(period = 'daily')
    unless %w[hourly daily weekly].include?(period)
      raise ArgumentError, "Invalid period: #{period}. Must be one of: hourly, daily, weekly"
    end
    
    # Check if there are any security events to report
    time_range = case period
                 when 'hourly'
                   1.hour.ago..Time.now.utc
                 when 'daily'
                   24.hours.ago..Time.now.utc
                 when 'weekly'
                   1.week.ago..Time.now.utc
                 end
    
    count = SecurityEvent.where(created_at: time_range).count
    
    # Only send report if there are events to report
    if count > 0
      SecurityMailer.suspicious_activity_report(period).deliver_now
      
      # Log that report was sent
      Rails.logger.info("Security report (#{period}) sent with #{count} events")
    else
      Rails.logger.info("No security events found for #{period} report - skipping")
    end
  end
end 