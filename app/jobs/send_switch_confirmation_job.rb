class SendSwitchConfirmationJob
  include Sidekiq::Job

  queue_as :priority

  def perform(email, customer_name, supplier_name, tariff_name, switch_status_url)
    SwitchConfirmationMailer.with(
      email: email,
      customer_name: customer_name,
      supplier_name: supplier_name,
      tariff_name: tariff_name,
      switch_status_url: switch_status_url
    ).confirm_switch_email.deliver_now!
  end
end
