# app/models/security_event.rb
class SecurityEvent < ApplicationRecord
  # Event types
  VERIFICATION_SUCCESS = 'verification_success'
  VERIFICATION_FAILURE = 'verification_failure'
  VERIFICATION_CODE_SENT = 'verification_code_sent'
  RATE_LIMIT_TRIGGERED = 'rate_limit_triggered'
  
  # Validations
  validates :event_type, presence: true
  validates :email, presence: true, unless: -> { ip_address.present? }
  validates :ip_address, presence: true, unless: -> { email.present? }
  
  # Scopes
  scope :recent, -> { order(created_at: :desc).limit(100) }
  scope :by_email, ->(email) { where(email: email) }
  scope :by_ip, ->(ip) { where(ip_address: ip) }
  scope :by_type, ->(type) { where(event_type: type) }
  
  # Create time-based scopes
  scope :last_day, -> { where('created_at > ?', 24.hours.ago) }
  scope :last_hour, -> { where('created_at > ?', 1.hour.ago) }
  
  # Store details as JSON
  serialize :details, coder: JSO<PERSON>
  
  # Helper methods to identify patterns
  
  # Check if this IP has had suspicious activity recently
  def self.suspicious_ip?(ip_address)
    return false if ip_address.blank?
    
    count = by_ip(ip_address).last_day.count
    rate_limits = by_ip(ip_address).by_type(RATE_LIMIT_TRIGGERED).last_day.count
    failures = by_ip(ip_address).by_type(VERIFICATION_FAILURE).last_day.count
    
    # Determine if suspicious based on thresholds
    count > 50 || rate_limits > 3 || failures > 10
  end
  
  # Check if this email has had suspicious activity recently
  def self.suspicious_email?(email)
    return false if email.blank?
    
    count = by_email(email).last_day.count
    rate_limits = by_email(email).by_type(RATE_LIMIT_TRIGGERED).last_day.count
    failures = by_email(email).by_type(VERIFICATION_FAILURE).last_day.count
    
    # Determine if suspicious based on thresholds
    count > 20 || rate_limits > 3 || failures > 5
  end
  
  # Find potentially related events (same IP or email)
  def related_events
    return SecurityEvent.none if ip_address.blank? && email.blank?
    
    conditions = []
    values = {}
    
    if ip_address.present?
      conditions << "ip_address = :ip"
      values[:ip] = ip_address
    end
    
    if email.present?
      conditions << "email = :email"
      values[:email] = email
    end
    
    SecurityEvent.where(conditions.join(' OR '), values)
      .where.not(id: id)
      .order(created_at: :desc)
      .limit(50)
  end
end 