class EnergyTariff < ApplicationRecord
  enum energy_type: [ :gas, :electricity, :both ]
  enum tariff_type: [ :fixed, :variable]

  validates :energy_type, :tariff_name, :tariff_type, :exit_fees,
    :payment_methods,  presence: true
  validates :product_code, uniqueness: true, allow_blank: true

  belongs_to :supplier
  has_many :energy_tariff_rates, dependent: :destroy

  def supplier_name
    supplier&.name
  end

  def readable_tariff_type
    if fixed?
      'Fixed'
    elsif variable?
      'Standard Variable'
    end
  end

  def available_from
    Date.today
  end

  def available_to
    Date.today
  end

  # Calculate estimated cost for this tariff based on user's energy usage
  def calculate_estimated_cost(gas_energy_tariff, electricity_energy_tariff)
    estimated_cost = {}
    
    # Calculate gas costs if applicable
    if gas_energy_tariff.present?
      gas_cost = calculate_gas_cost(gas_energy_tariff, electricity_energy_tariff)
      estimated_cost[:gas] = gas_cost if gas_cost.present?
    end

    # Calculate electricity costs if applicable
    if electricity_energy_tariff.present?
      electricity_cost = calculate_electricity_cost(
        electricity_energy_tariff
      )
      estimated_cost[:electricity] = electricity_cost if electricity_cost.present?
    end

    # Calculate total costs
    if estimated_cost[:gas].present? && estimated_cost[:electricity].present?
      yearly_total = estimated_cost[:gas][:yearly] + estimated_cost[:electricity][:yearly]
      estimated_cost[:total] = {
        monthly: (yearly_total / 12).round(2),
        yearly: yearly_total.round(2)
      }
    end

    estimated_cost
  end

  # Calculate electricity cost considering meter type and gsp_code
  def calculate_electricity_cost(electricity_energy_tariff)
    # Get appropriate electricity rates based on region
    gsp_code = electricity_energy_tariff.gsp_code
    profile_class = electricity_energy_tariff.profile_class.to_i

    rates = energy_tariff_rates.where(
      fuel_type: 'electricity',
      gsp_code: gsp_code,
      profile_class: profile_class
    )
    
    return nil unless rates.exists?
    rate = rates.first # Using first matching rate for simplicity
    
    annual_usage = get_annual_electricity_usage(electricity_energy_tariff)
    
    # Economy 7 meter calculation
    if profile_class == 2 &&
       rate.day_unit_rate_inc_vat.present? && 
       rate.night_unit_rate_inc_vat.present?
      
      # Default Economy 7 split (could be customized)
      day_percent = 0.58 # 58% daytime usage
      night_percent = 0.42 # 42% nighttime usage
      
      day_unit_rate = rate.day_unit_rate_inc_vat.to_f / 100
      night_unit_rate = rate.night_unit_rate_inc_vat.to_f / 100
      standing_charge = rate.standing_charge_inc_vat.to_f / 100
      
      day_usage = annual_usage * day_percent
      night_usage = annual_usage * night_percent
      
      yearly_cost = (day_unit_rate * day_usage) + 
                    (night_unit_rate * night_usage) + 
                    (standing_charge * 365)
    else
      # Standard meter calculation
      unit_rate = rate.unit_rate_inc_vat.to_f / 100
      standing_charge = rate.standing_charge_inc_vat.to_f / 100
      
      yearly_cost = (unit_rate * annual_usage) + (standing_charge * 365)
    end
    
    {
      monthly: (yearly_cost / 12).round(2),
      yearly: yearly_cost.round(2)
    }
  end

  # Calculate gas cost
  def calculate_gas_cost(gas_energy_tariff, electricity_energy_tariff)
    rates = energy_tariff_rates.where(fuel_type: 'gas')

    gsp_code = electricity_energy_tariff.try(:gsp_code)
    
    if gsp_code.present?
      rates = rates.where(gsp_code: gsp_code)
    end
    
    return nil unless rates.exists?

    rate = rates.first
    
    annual_usage = get_annual_gas_usage(gas_energy_tariff)
    
    unit_rate = rate.unit_rate_inc_vat.to_f / 100
    standing_charge = rate.standing_charge_inc_vat.to_f / 100
    
    yearly_cost = (unit_rate * annual_usage) + (standing_charge * 365)
    
    {
      monthly: (yearly_cost / 12).round(2),
      yearly: yearly_cost.round(2)
    }
  end

  # Helper to get annual electricity usage
  def get_annual_electricity_usage(electricity_energy_tariff)
    if electricity_energy_tariff.respond_to?(:electricity_est_annual_usage)
      electricity_energy_tariff.electricity_est_annual_usage.to_f
    elsif electricity_energy_tariff.respond_to?(:electricity_monthly_usage)
      electricity_energy_tariff.electricity_monthly_usage.to_f * 12
    else
      0
    end
  end

  # Helper to get annual gas usage
  def get_annual_gas_usage(gas_energy_tariff)
    if gas_energy_tariff.respond_to?(:gas_estimated_annual_usage)
      gas_energy_tariff.gas_estimated_annual_usage.to_f
    elsif gas_energy_tariff.respond_to?(:gas_monthly_usage)
      gas_energy_tariff.gas_monthly_usage.to_f * 12
    else
      0
    end
  end

  # Calculate estimated savings by comparing with current tariffs
  def calculate_estimated_saving(gas_energy_tariff, electricity_energy_tariff)
    estimated_saving = {}
    
    # Calculate costs with the new tariff
    new_costs = calculate_estimated_cost(gas_energy_tariff, electricity_energy_tariff)
    
    # Calculate current costs using current tariff rates
    current_costs = calculate_current_costs(gas_energy_tariff, electricity_energy_tariff)
    
    # Calculate savings for gas
    if current_costs[:gas].present? && new_costs[:gas].present?
      gas_saving_amount = current_costs[:gas][:yearly] - new_costs[:gas][:yearly]
      gas_saving_percentage = current_costs[:gas][:yearly] > 0 ? 
                             ((gas_saving_amount / current_costs[:gas][:yearly]) * 100) : 0
      
      estimated_saving[:gas] = {
        amount: gas_saving_amount.round(1),
        percentage: gas_saving_percentage.round(1)
      }
    end

    # Calculate savings for electricity
    if current_costs[:electricity].present? && new_costs[:electricity].present?
      elec_saving_amount = current_costs[:electricity][:yearly] - new_costs[:electricity][:yearly]
      elec_saving_percentage = current_costs[:electricity][:yearly] > 0 ? 
                              ((elec_saving_amount / current_costs[:electricity][:yearly]) * 100) : 0
      
      estimated_saving[:electricity] = {
        amount: elec_saving_amount.round(1),
        percentage: elec_saving_percentage.round(1)
      }
    end
    
    # Calculate total savings if both present
    if estimated_saving[:gas].present?
      gas_saving_amount = estimated_saving[:gas][:amount]
    else
      gas_saving_amount = 0
    end

    if estimated_saving[:electricity].present?
      elec_saving_amount = estimated_saving[:electricity][:amount]
    else
      elec_saving_amount = 0
    end

    total_saving_amount = gas_saving_amount + elec_saving_amount
    total_current_cost = current_costs[:total][:yearly]
    total_saving_percentage = total_current_cost > 0 ?
                              ((total_saving_amount / total_current_cost) * 100) : 0
    
    estimated_saving[:total] = {
      amount: [total_saving_amount, 0].max.round(1),
      percentage: [total_saving_percentage, 0].max.round(1)
    }

    estimated_saving
  end

  # Helper method to calculate current costs based on user's existing tariffs
  def calculate_current_costs(gas_energy_tariff, electricity_energy_tariff)
    current_costs = {}
    
    if gas_energy_tariff.present?
      gas_costs = gas_energy_tariff.calculate_costs('gas')
      
      current_costs[:gas] = {
        monthly: gas_costs.last,
        yearly: gas_costs.first
      }
    end
    
    if electricity_energy_tariff.present?
      electricity_costs = electricity_energy_tariff.calculate_costs('electricity')   
     
      current_costs[:electricity] = {
        monthly: electricity_costs.last,
        yearly: electricity_costs.first
      }
    end
    
    # Add total if both gas and electricity costs are present
    if current_costs[:gas].present? && current_costs[:electricity].present?
      
    end

    if current_costs[:gas].present?
      gas_current_yearly_cost = current_costs[:gas][:yearly]
    else
      gas_current_yearly_cost = 0
    end

    if current_costs[:electricity].present?
      electricity_current_yearly_cost = current_costs[:electricity][:yearly]
    else
      electricity_current_yearly_cost = 0
    end

    yearly_total = gas_current_yearly_cost + electricity_current_yearly_cost
    current_costs[:total] = {
      monthly: (yearly_total / 12).round(2),
      yearly: yearly_total.round(2)
    }
    
    current_costs
  end
end
