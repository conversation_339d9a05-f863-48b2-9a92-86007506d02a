class Admin < ApplicationRecord
  # Use UUID as primary key
  self.primary_key = 'uuid'
  
  has_secure_password

  validates :email, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, presence: true, length: { minimum: 8 }, if: :password_digest_changed?
  validates :first_name, :last_name, presence: true

  def self.verify_email(email)
    admin = find_by(email: email.downcase)
    return nil unless admin
    admin
  end

  def full_name
    "#{first_name} #{last_name}"
  end

  def generate_password_reset_token
    self.reset_token = SecureRandom.urlsafe_base64
    self.reset_token_expiry = 1.hour.from_now
    save
    reset_token
  end

  def valid_reset_token?(token)
    reset_token == token && reset_token_expiry > Time.now
  end

  def clear_reset_token
    self.reset_token = nil
    self.reset_token_expiry = nil
    save
  end
end
