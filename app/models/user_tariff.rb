class UserTariff < ApplicationRecord
  enum energy_type: [ :gas, :electricity ]

  VALID_METER_SERIAL_NUMBER_PATTERN = /\A[a-zA-Z0-9]{6,21}\z/

  validates :energy_type, :meter_serial_number, :unit_rate, :standing_charge, presence: true
  validates :meter_serial_number, format: { with: VALID_METER_SERIAL_NUMBER_PATTERN, message: "must be 6 to 21 alphanumeric characters" }
  validates :meter_point_reference_number, presence: true, if: :gas_energy_type?
  validates :meter_point_administration_number, presence: true, if: :electricity_energy_type?
  
  # Class method to validate meter serial numbers
  def self.valid_meter_serial_number?(msn)
    msn.present? && msn.match?(VALID_METER_SERIAL_NUMBER_PATTERN)
  end

  belongs_to :switch_user

  def gas_energy_type?
    energy_type.to_s.downcase == 'gas'
  end

  def electricity_energy_type?
    energy_type.to_s.downcase == 'electricity'
  end
  
  # Calculate yearly and monthly costs for this tariff
  def calculate_costs(fuel_type = nil)
    # Default to energy_type if fuel_type not specified
    fuel_type ||= energy_type.to_s
    return [0, 0] unless fuel_type.present?

    if fuel_type == 'electricity'
      # Try pre-calculated cost first, but fall back to calculation if it's 0 or missing
      pre_calculated_cost = electricity_est_annual_cost.to_f

      if pre_calculated_cost > 0
        yearly_cost = pre_calculated_cost.round(2)
        monthly_cost = (yearly_cost / 12).round(2)
        return [yearly_cost, monthly_cost]
      else
        # Fall back to calculating from rates and usage
        return calculate_electricity_costs_from_rates
      end

    elsif fuel_type == 'gas'
      # Try pre-calculated cost first, but fall back to calculation if it's 0 or missing
      pre_calculated_cost = gas_estimated_annual_cost.to_f

      if pre_calculated_cost > 0
        yearly_cost = pre_calculated_cost.round(2)
        monthly_cost = (yearly_cost / 12).round(2)
        return [yearly_cost, monthly_cost]
      else
        # Fall back to calculating from rates and usage
        return calculate_gas_costs_from_rates
      end
    end

    # Legacy fallback for other fuel types
    return calculate_costs_from_unit_rate(fuel_type)
    
    # Get annual consumption for specified fuel type
    annual_consumption = if fuel_type == 'electricity'
                          respond_to?(:electricity_est_annual_usage) ? electricity_est_annual_usage.to_f : 0
                        elsif fuel_type == 'gas'
                          respond_to?(:gas_estimated_annual_usage) ? gas_estimated_annual_usage.to_f : 0
                        else
                          0
                        end
    
    return [0, 0] unless annual_consumption && annual_consumption > 0
    
    # Calculate yearly cost
    yearly_standing_charge = standing_charge.to_f / 100 * 365
    
    # Calculate yearly energy cost
    unit_rate_value = unit_rate.to_f / 100
    
    yearly_energy_cost = annual_consumption * unit_rate_value
    
    yearly_cost = yearly_standing_charge + yearly_energy_cost
    monthly_cost = yearly_cost / 12
    
    [yearly_cost.round(2), monthly_cost.round(2)]
  end

  def formatted_estimated_cost(fuel_type)
    costs = calculate_costs(fuel_type)

    "£#{costs[1].round(1)}/mo (£#{costs[0].round(1)}/yr)"
  end

  private

  # Calculate electricity costs from unit rates and usage
  def calculate_electricity_costs_from_rates
    return [0, 0] unless electricity_est_annual_usage.present? &&
                         unit_rate.present? &&
                         standing_charge.present?

    annual_usage = electricity_est_annual_usage.to_f
    unit_rate_value = unit_rate.to_f / 100  # Convert pence to pounds
    standing_charge_value = standing_charge.to_f / 100  # Convert pence to pounds

    # Calculate yearly cost
    yearly_energy_cost = annual_usage * unit_rate_value
    yearly_standing_charge = standing_charge_value * 365
    yearly_cost = yearly_energy_cost + yearly_standing_charge

    monthly_cost = yearly_cost / 12

    [yearly_cost.round(2), monthly_cost.round(2)]
  end

  # Calculate gas costs from unit rates and usage
  def calculate_gas_costs_from_rates
    return [0, 0] unless gas_estimated_annual_usage.present? &&
                         unit_rate.present? &&
                         standing_charge.present?

    annual_usage = gas_estimated_annual_usage.to_f
    unit_rate_value = unit_rate.to_f / 100  # Convert pence to pounds
    standing_charge_value = standing_charge.to_f / 100  # Convert pence to pounds

    # Calculate yearly cost
    yearly_energy_cost = annual_usage * unit_rate_value
    yearly_standing_charge = standing_charge_value * 365
    yearly_cost = yearly_energy_cost + yearly_standing_charge

    monthly_cost = yearly_cost / 12

    [yearly_cost.round(2), monthly_cost.round(2)]
  end

  # Legacy method for calculating from unit rate (fallback)
  def calculate_costs_from_unit_rate(fuel_type)
    # Ensure we have the required fields
    unless respond_to?(:unit_rate) && respond_to?(:standing_charge)
      return [0, 0]
    end

    # Get annual consumption for specified fuel type
    annual_consumption = if fuel_type == 'electricity'
                          respond_to?(:electricity_est_annual_usage) ? electricity_est_annual_usage.to_f : 0
                        elsif fuel_type == 'gas'
                          respond_to?(:gas_estimated_annual_usage) ? gas_estimated_annual_usage.to_f : 0
                        else
                          0
                        end

    return [0, 0] unless annual_consumption && annual_consumption > 0

    # Calculate yearly cost
    yearly_standing_charge = standing_charge.to_f / 100 * 365

    # Calculate yearly energy cost
    unit_rate_value = unit_rate.to_f / 100

    yearly_energy_cost = annual_consumption * unit_rate_value

    yearly_cost = yearly_standing_charge + yearly_energy_cost
    monthly_cost = yearly_cost / 12

    [yearly_cost.round(2), monthly_cost.round(2)]
  end
end
