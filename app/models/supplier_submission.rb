class SupplierSubmission < ApplicationRecord
  enum status: {
    pending: 0,
    submitted: 1,
    successful: 2,
    failed: 3,
    rejected: 4,
    retry_scheduled: 5
  }

  belongs_to :energy_switch
  belongs_to :supplier

  validates :submission_type, presence: true
  
  before_create :set_submitted_at

  scope :latest_attempt, -> { order(attempt_number: :desc).first }
  scope :successful, -> { where(status: :successful) }
  scope :pending_or_submitted, -> { where(status: [:pending, :submitted]) }
  scope :pending_or_submitted_or_successful, -> { where(status: [:pending, :submitted, :successful]) }
  scope :failed_or_rejected, -> { where(status: [:failed, :rejected]) }
  
  # Returns a human-readable description of the submission status
  def status_summary
    case status
    when 'pending'
      'Waiting to be submitted'
    when 'submitted'
      "Submitted to #{supplier.name}, awaiting response"
    when 'successful'
      "Successfully processed by #{supplier.name}"
    when 'failed'
      "Failed: #{error_message}"
    when 'rejected'
      "Rejected by #{supplier.name}: #{rejection_reason}"
    when 'retry_scheduled'
      "Failed, scheduled for retry"
    end
  end

  private

  def set_submitted_at
    self.submitted_at ||= Time.current if status == 'submitted'
  end
end 