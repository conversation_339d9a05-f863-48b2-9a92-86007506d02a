class EnergyTariffRate < ApplicationRecord
  belongs_to :energy_tariff

  validates :fuel_type, presence: true

  with_options if: -> { fuel_type == 'electricity' } do
    validates :gsp_code, :profile_class, presence: true
    validates :gsp_code, format: { with: /\A_[A-Z]\z/, message: "must be in the format '_X' where X is a capital letter" }
  end

  with_options if: -> { profile_class == 1 && fuel_type == 'electricity' } do
    validates :standing_charge_inc_vat, :unit_rate_inc_vat,
              presence: true, numericality: { greater_than_or_equal_to: 0 }
  end

  with_options if: -> { profile_class == 2 && fuel_type == 'electricity' } do
    validates :standing_charge_inc_vat, :day_unit_rate_inc_vat, :night_unit_rate_inc_vat,
              presence: true, numericality: { greater_than_or_equal_to: 0 }
  end
end
