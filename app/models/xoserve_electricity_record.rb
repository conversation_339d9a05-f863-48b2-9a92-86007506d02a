# frozen_string_literal: true

class XoserveElectricityRecord < ApplicationRecord
  validates :meter_serial_number, presence: true
  validates :meter_serial_number, uniqueness: true
  validates :mpan_core, presence: true
  validates :mpan_core, uniqueness: true

  def self.find_by_meter_serial_number(meter_serial_number)
    record = where(meter_serial_number: meter_serial_number).first
    
    # Return nil if record doesn't exist or is older than 48 hours
    return nil if record.nil? || record.updated_at < 48.hours.ago
    
    record&.search_utility_address_json
  end

  def self.find_by_mpan_core(mpan_core)
    record = where(mpan_core: mpan_core).first
    
    # Return nil if record doesn't exist or is older than 48 hours
    return nil if record.nil? || record.updated_at < 48.hours.ago
    
    record&.get_technical_details_by_mpan_json
  end

  def self.store_utility_address_json(meter_serial_number, mpan_core, utility_address_match)
    record = where(meter_serial_number: meter_serial_number).first
    
    if record
      record.update!(
        mpan_core: mpan_core,
        search_utility_address_json: utility_address_match
      )
    else
      create!(
        meter_serial_number: meter_serial_number,
        mpan_core: mpan_core,
        search_utility_address_json: utility_address_match
      )
    end
  end

  def self.store_technical_details_json(mpan_core, utility_match)
    record = where(mpan_core: mpan_core).first
    return if record.nil?

    # Update existing record
    record.update!(
      get_technical_details_by_mpan_json: utility_match
    )
  end

  def self.extract_meter_serial_number(utility_match)
    return nil unless utility_match && utility_match['Meters'] && utility_match['Meters'].any?
    
    meter_details = utility_match['Meters'][0]['MeterDetails']
    meter_serial = meter_details.find { |detail| detail['Key'] == 'meter_serial_number' }
    
    meter_serial ? meter_serial['Value'] : nil
  end
end 