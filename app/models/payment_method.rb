class PaymentMethod < ApplicationRecord
  MONTHLY_FIXED_DIRECT_DEBIT ||= "Monthly Fixed Direct Direct"
  MONTHLY_VARIABLE_DIRECT_DEBIT ||= "Monthly Variable Direct Direct"
  MONTHLY_VARIABLE_CASH_CHEQUE ||= "Monthly Variable Cash/Cheque"

  belongs_to :switch_user

  validates :payment_type, presence: true
  # validates :estimated_monthly_payment, presence: true, numericality: { greater_than: 0 }

  # Validations for direct debit specific fields
  with_options if: :direct_debit? do |dd|
    dd.validates :account_holder_name, presence: true
    dd.validates :sort_code, presence: true, format: { with: /\A\d{6}\z/, message: "should be 6 digits" }
    dd.validates :account_number, presence: true, format: { with: /\A\d{8}\z/, message: "should be 8 digits" }
  end

  # Validations for billing address
  with_options unless: :billing_address_same_as_supply? do |ba|
    ba.validates :billing_address, presence: true
  end

  before_save :format_sort_code, if: :direct_debit?

  private

  def direct_debit?
    payment_type == MONTHLY_FIXED_DIRECT_DEBIT || payment_type == MONTHLY_VARIABLE_DIRECT_DEBIT
  end

  def format_sort_code
    self.sort_code = sort_code.gsub(/\D/, '') if sort_code.present?
  end
end
