class SwitchConfirmationMailer < ApplicationMailer
  default from: 'Meet <PERSON> <<EMAIL>>' # this domain must be verified with <PERSON>send
  def confirm_switch_email
    @email = params[:email]
    @customer_name = params[:customer_name]
    @supplier_name = params[:supplier_name]
    @tariff_name = params[:tariff_name]
    @switch_status_url = params[:switch_status_url]

    mail(to: @email, subject: 'Meet George - Switch Confirmation')
  end
end