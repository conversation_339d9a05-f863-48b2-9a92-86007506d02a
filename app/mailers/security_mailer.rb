# app/mailers/security_mailer.rb
class SecurityMailer < ApplicationMailer
  # Override the default from address
  default from: ENV['SECURITY_ALERT_FROM_EMAIL'] || '<EMAIL>'
  
  def alert(subject_or_message, message = nil, events: nil)
    # Handle both old and new method signatures
    if message.nil?
      @message = subject_or_message
      subject_text = "🚨 SECURITY ALERT: #{subject_or_message.truncate(50)}"
    else
      @message = message
      subject_text = subject_or_message
    end
    
    @timestamp = Time.now.utc
    @environment = Rails.env
    @server = Socket.gethostname rescue 'unknown'
    @events = events
    
    # Add request details if available
    if defined?(RequestStore) && RequestStore.store[:request_id].present?
      @request_id = RequestStore.store[:request_id]
    end
    
    mail(
      to: ENV['SECURITY_TEAM_EMAIL'] || '<EMAIL>',
      from: ENV['SECURITY_ALERT_FROM_EMAIL'] || '<EMAIL>',
      subject: subject_text
    )
  end
  
  def suspicious_activity_report(period = 'daily', events = nil)
    @period = period
    @timestamp = Time.now.utc
    
    if events.present?
      @events = events
    else
      time_range = case period
                  when 'hourly'
                    1.hour.ago..Time.now.utc
                  when 'daily'
                    24.hours.ago..Time.now.utc
                  when 'weekly'
                    1.week.ago..Time.now.utc
                  else
                    24.hours.ago..Time.now.utc
                  end
      
      @events = SecurityEvent.where(created_at: time_range)
    end
    
    # Group events by type
    @events_by_type = @events.group_by(&:event_type)
    
    # Calculate totals
    @total_count = @events.size
    
    subject = case period
              when 'hourly'
                "Hourly Security Activity Report"
              when 'daily'
                "Daily Security Activity Report"
              when 'weekly'
                "Weekly Security Activity Report"
              else
                "Security Activity Report"
              end
    
    mail(
      to: ENV['SECURITY_TEAM_EMAIL'] || '<EMAIL>',
      from: ENV['SECURITY_ALERT_FROM_EMAIL'] || '<EMAIL>',
      subject: subject
    )
  end
end