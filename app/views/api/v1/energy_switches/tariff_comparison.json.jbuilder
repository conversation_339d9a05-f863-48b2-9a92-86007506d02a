json.extract! @energy_switch, :id, :status, :reference_number, :switching_to_tariff_id

json.available_tariffs @energy_switch.available_tariffs

json.switch_user do
  # Use joined data to avoid N+1 queries
  if @energy_switch.switch_user_id.present?
    json.id @energy_switch.switch_user_id
    json.first_name @energy_switch.first_name
    json.last_name @energy_switch.last_name
    json.email @energy_switch.email
    json.phone_number @energy_switch.phone_number
  else
    json.null!
  end
end

json.address do
  # Use joined data to avoid N+1 queries
  if @energy_switch.address_id.present?
    json.postcode @energy_switch.postcode
    json.posttown @energy_switch.posttown
    json.full_address @energy_switch.full_address&.titleize
  else
    json.null!
  end
end

json.current_supplier do
  # Use joined data to avoid loading current_tariff
  has_gas = @energy_switch.gas_energy_tariff_id.present?
  has_electricity = @energy_switch.electricity_energy_tariff_id.present?
  
  if has_gas || has_electricity
    # Use joined data to avoid loading associations
    if has_electricity
      supplier_name = @energy_switch.elec_supplier_name
      tariff_name = @energy_switch.elec_tariff_name
      tariff_type = @energy_switch.elec_tariff_type&.titleize
      exit_fee = @energy_switch.elec_exit_fees || 0
      payment_method = @energy_switch.elec_payment_method
      profile_class = @energy_switch.profile_class
    else
      supplier_name = @energy_switch.gas_supplier_name
      tariff_name = @energy_switch.gas_tariff_name
      tariff_type = @energy_switch.gas_tariff_type&.titleize
      exit_fee = @energy_switch.gas_exit_fees || 0
      payment_method = @energy_switch.gas_payment_method
      profile_class = nil
    end

    json.supplier_name supplier_name
    json.tariff_name tariff_name
    json.tariff_type tariff_type
    json.exit_fee exit_fee
    json.payment_method payment_method
    json.profile_class profile_class
    json.tariff_ends '-'
    json.price_guaranteed '-'
    json.discounts 'None'
    json.additional_charges 'None'
    json.additional_services 'None'
    json.total_estimated_costs @energy_switch.total_estimated_costs

    json.fuel_type(
      if has_gas && has_electricity
        'Electricity & Gas'
      elsif has_gas
        'Gas'
      elsif has_electricity
        'Electricity'
      end
    )
  else
    json.null!
  end

  json.gas do
    if @energy_switch.gas_energy_tariff_id.present?
      tariff = @energy_switch.gas_energy_tariff
      if tariff
        json.extract! tariff, :meter_point_reference_number, :meter_point_administration_number, :meter_serial_number,
        :supplier_name, :payment_method
        json.unit_rate tariff.unit_rate.to_f.round(3)
        json.standing_charge tariff.standing_charge.to_f.round(3)
        json.estimated_annual_usage tariff.gas_estimated_annual_usage.to_f.round(2)
        json.monthly_usage tariff.gas_monthly_usage.to_f.round(2)
        json.tariff_type tariff.tariff_type.try(:titleize)
        json.formatted_estimated_cost tariff.formatted_estimated_cost('gas')
        json.estimated_costs tariff.calculate_costs('gas')
        json.mpan tariff.meter_point_administration_number
        json.meter_serial_number tariff.meter_serial_number
      else
        json.null!
      end
    else
      json.null!
    end
  end

  json.electricity do
    if @energy_switch.electricity_energy_tariff_id.present?
      tariff = @energy_switch.electricity_energy_tariff
      if tariff
        json.extract! tariff, :meter_point_reference_number, :meter_point_administration_number, :meter_serial_number,
        :supplier_name, :payment_method
        json.unit_rate tariff.unit_rate.to_f.round(3)
        json.standing_charge tariff.standing_charge.to_f.round(3)
        json.estimated_annual_usage tariff.electricity_est_annual_usage.to_f.round(2)
        json.monthly_usage tariff.electricity_monthly_usage.to_f.round(2)
        json.tariff_type tariff.tariff_type.try(:titleize)
        json.formatted_estimated_cost tariff.formatted_estimated_cost('electricity')
        json.estimated_costs tariff.calculate_costs('electricity')
        json.mpan tariff.meter_point_administration_number
        json.meter_serial_number tariff.meter_serial_number
      else
        json.null!
      end
    else
      json.null!
    end
  end

  json.fuel_types(
    if @energy_switch.gas_energy_tariff_id.present? && @energy_switch.electricity_energy_tariff_id.present?
      ['electricity', 'gas']
    elsif @energy_switch.gas_energy_tariff_id.present?
      ['gas']
    elsif @energy_switch.electricity_energy_tariff_id.present?
      ['electricity']
    else
      []
    end
  )
end
