<!DOCTYPE html>
<html>
<head>
  <meta content='text/html; charset=UTF-8' http-equiv='Content-Type' />
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
    }
    .header {
      background-color: #2196F3;
      color: white;
      padding: 15px;
      margin-bottom: 20px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    .event {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f1f1f1;
      border-left: 3px solid #2196F3;
    }
    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #ddd;
      padding-top: 10px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1><%= @period.capitalize %> Security Report</h1>
    <p>Report generated: <%= @timestamp.strftime("%Y-%m-%d %H:%M:%S UTC") %></p>
  </div>
  
  <div class="content">
    <h2>Summary</h2>
    <p>This report covers security events for the <%= @period %> period.</p>
    <p><strong>Total Events:</strong> <%= @total_count %></p>
    
    <% if @events.present? %>
      <h2>Event Details</h2>
      <% @events.each do |event| %>
        <div class="event">
          <p><strong>Event Type:</strong> <%= event.event_type %></p>
          <% if event.email.present? %>
            <p><strong>Email:</strong> <%= event.email %></p>
          <% end %>
          <% if event.ip_address.present? %>
            <p><strong>IP Address:</strong> <%= event.ip_address %></p>
          <% end %>
          <p><strong>Time:</strong> <%= event.created_at.strftime("%Y-%m-%d %H:%M:%S UTC") %></p>
          <% if event.details.present? %>
            <p><strong>Details:</strong> <%= event.details.inspect %></p>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>
  
  <div class="footer">
    <p>This is an automated security report. Please analyze the data and take appropriate actions if needed.</p>
    <p>If you have any questions, please contact the security team.</p>
  </div>
</body>
</html> 