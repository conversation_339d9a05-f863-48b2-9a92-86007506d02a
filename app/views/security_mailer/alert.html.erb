<!DOCTYPE html>
<html>
<head>
  <meta content='text/html; charset=UTF-8' http-equiv='Content-Type' />
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.5;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
    }
    .header {
      background-color: #f44336;
      color: white;
      padding: 15px;
      margin-bottom: 20px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }
    .event {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f1f1f1;
      border-left: 3px solid #f44336;
    }
    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #777;
      border-top: 1px solid #ddd;
      padding-top: 10px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Security Alert</h1>
  </div>
  
  <div class="content">
    <h2>Alert Details</h2>
    <p><strong>Message:</strong> <%= @message %></p>
    <p><strong>Timestamp:</strong> <%= @timestamp.strftime("%Y-%m-%d %H:%M:%S UTC") %></p>
    <p><strong>Environment:</strong> <%= @environment %></p>
    <p><strong>Server:</strong> <%= @server %></p>
    <% if @request_id.present? %>
    <p><strong>Request ID:</strong> <%= @request_id %></p>
    <% end %>
    
    <% if @events.present? %>
    <h2>Event Details</h2>
      <% @events.each do |event| %>
        <div class="event">
          <p><strong>Event Type:</strong> <%= event.event_type %></p>
          <% if event.email.present? %>
            <p><strong>Email:</strong> <%= event.email %></p>
          <% end %>
          <% if event.ip_address.present? %>
            <p><strong>IP Address:</strong> <%= event.ip_address %></p>
          <% end %>
          <p><strong>Time:</strong> <%= event.created_at.strftime("%Y-%m-%d %H:%M:%S UTC") %></p>
          <% if event.details.present? %>
            <p><strong>Details:</strong> <%= event.details.inspect %></p>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>
  
  <div class="footer">
    <p>This is an automated security alert. Please respond according to security protocols.</p>
    <p>If this alert is invalid or you have questions, please contact the security team.</p>
  </div>
</body>
</html> 