class TokenService
  # Generate token for admin users
  def self.generate_admin_token(admin)
    admin_app = Doorkeeper::Application.find_by(name: 'Admin')
    
    unless admin_app
      Rails.logger.error "Admin application not found in Doorkeeper configuration"
      return nil
    end
    
    # Admin UUID is now the primary key
    Rails.logger.info "Generating admin token for admin UUID: #{admin.id}"
    
    Doorkeeper::AccessToken.create!(
      application_id: admin_app.id,
      resource_owner_id: admin.id, # Store the UUID directly
      scopes: 'admin',
      expires_in: 8.hours.to_i  # Standard admin session length
    )
  end
  
  # Generate token for chatbot interactions
  def self.generate_chatbot_token
    chatbot_app = Doorkeeper::Application.find_by(name: 'ChatBot')
    
    unless chatbot_app
      Rails.logger.error "ChatBot application not found in Doorkeeper configuration"
      return nil
    end
    
    # ChatBot tokens typically don't have a specific resource owner
    Doorkeeper::AccessToken.create!(
      application_id: chatbot_app.id,
      scopes: 'chatbot',
      expires_in: nil  # Never expires (forever)
    )
  end
  
  # Generate token for onboarding process
  def self.generate_onboarding_token(switch_user)
    onboarding_app = Doorkeeper::Application.find_by(name: 'Onboarding')
    
    unless onboarding_app
      Rails.logger.error "Onboarding application not found in Doorkeeper configuration"
      return nil
    end
    
    # User ID might be a UUID depending on the implementation
    Rails.logger.info "Generating onboarding token for user ID: #{switch_user.id}"
    
    Doorkeeper::AccessToken.create!(
      application_id: onboarding_app.id,
      resource_owner_id: switch_user.id, # Store the ID directly
      scopes: 'onboarding',
      expires_in: ENV['ONBOARDING_ACCESS_TOKEN_EXPIRATION_HOURS'].to_i * 3600,
      use_refresh_token: false
    )
  end
  
  # Generate secure JWT token for onboarding with encrypted energy switch details
  def self.generate_onboarding_jwt(energy_switch)
    return nil unless energy_switch.present?

    # Validate environment variables
    jwt_secret = ENV['JWT_SECRET_KEY']
    expiration_hours = ENV['ONBOARDING_ACCESS_TOKEN_EXPIRATION_HOURS'].to_i

    unless jwt_secret.present? && expiration_hours.positive?
      Rails.logger.error "Missing or invalid JWT configuration: JWT_SECRET_KEY or ONBOARDING_ACCESS_TOKEN_EXPIRATION_HOURS"
      return nil
    end

    # Generate a unique token ID to enable revocation
    token_id = SecureRandom.uuid

    # Create payload with minimal necessary information
    payload = {
      switch_id: energy_switch.id,
      user_id: energy_switch.switch_user_id,
      jti: token_id,           # JWT ID for revocation
      iat: Time.now.to_i,      # Issued at
      exp: Time.now.to_i + (expiration_hours * 3600), # Expiration time
      one_time: true           # Flag for one-time use
    }

    # Store token ID in Redis/cache for revocation capability with expiration
    Rails.cache.write("onboarding_token:#{token_id}", true, expires_in: expiration_hours.hours)
    # Sign and encode the JWT
    JWT.encode(payload, jwt_secret, 'HS512')
  end

  # Verify and process onboarding JWT token
  def self.verify_onboarding_jwt(token)
    return nil if token.blank?

    jwt_secret = ENV['JWT_SECRET_KEY']
    unless jwt_secret.present?
      Rails.logger.error "Missing JWT configuration: JWT_SECRET_KEY"
      return nil
    end

    begin
      # Decode and verify the token
      decoded_token = JWT.decode(token, jwt_secret, true, { algorithm: 'HS512' })
      payload = decoded_token[0]
      
      # Check if token is already used (one-time use check)
      token_id = payload['jti']
      if token_id.present?
        token_status = Rails.cache.read("onboarding_token:#{token_id}")
        
        if token_status.nil?
          # Token doesn't exist or has expired
          return nil
        end

        # Disable one-time use check for now
        # if payload['one_time'] && token_status != true
        #   # Token was already used (marked as used)
        #   return nil
        # end
        
        # # Mark as used if it's a one-time token
        # if payload['one_time']
        #   Rails.cache.write("onboarding_token:#{token_id}", 'used', expires_in: 24.hours)
        # end
      end
      
      # Return the energy switch ID
      payload['switch_id']
    rescue JWT::ExpiredSignature
      Rails.logger.info "Expired onboarding token"
      nil
    rescue JWT::DecodeError, ActiveRecord::RecordNotFound => e
      Rails.logger.error "Error decoding onboarding token: #{e.message}"
      nil
    end
  end
  
  # Verify a token is valid and has the required scope
  def self.verify_token_scope(token, required_scope, application_name = nil, resource_owner_id = nil)
    return false if token.blank?
    
    access_token = Doorkeeper::AccessToken.find_by(token: token)
    
    # Check if token exists and is not expired
    return false if access_token.nil? || !access_token.accessible?
    
    # Check if token has the required scope
    return false unless access_token.scopes.include?(required_scope)
    
    # Check application name if specified
    if application_name.present?
      return false unless access_token.application.name == application_name
    end
    
    # Check resource owner if specified
    if resource_owner_id.present?
      # For UUIDs, ensure direct comparison works correctly
      return false unless access_token.resource_owner_id == resource_owner_id
    end

    true
  end
end 