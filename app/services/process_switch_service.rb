class ProcessSwitchService < BaseService
  def initialize(energy_switch, params = {})
    @energy_switch = energy_switch
    @params = params
  end

  def run
    switch_user = @energy_switch.switch_user

    begin
      ActiveRecord::Base.transaction do
        # Update SwitchUser information
        switch_user.update!(
          first_name: @params["firstName"],
          last_name: @params["lastName"],
          phone_number: @params["phoneNumber"],
          date_of_birth: @params["dateOfBirth"],
          requires_psr_support: @params["requiresSupport"] == 'yes'
        )

        # Update payment information
        payment_info = switch_user.payment_methods.create!(
          payment_type: PaymentMethod::MONTHLY_VARIABLE_DIRECT_DEBIT,
          account_holder_name: @params["accountHolder"],
          sort_code: @params["sortCode"],
          account_number: @params["accountNumber"],
          billing_address_same_as_supply: @params["isBillingAddressSame"] == 'yes',
          billing_address: @params["isBillingAddressSame"] == 'yes' ? @energy_switch.address.full_address : @params["billingAddress"],
          switch_within_5_days: @params["agreeToTerms"] == 'yes',
          understand_cooling_off_period: @params["agreeToTerms"] == 'yes'
        )

        switching_to_tariff = EnergyTariff.find(@params["switch_to"])

        # Update energy_switch status
        @energy_switch.update!(
          lived_three_years: @params["livedThreeYears"] == 'yes',
          payment_method_id: payment_info.id,
          switching_to_tariff: switching_to_tariff,
          switch_date: Time.zone.now.to_date,
          status: :confirmed
        )

        switch_status_url = "#{ENV['SERVER_HOST']}/onboarding/energy-switches/#{@energy_switch.id}/switch-status"

        SendSwitchConfirmationJob.perform_async(
          switch_user.email,
          switch_user.full_name,
          switching_to_tariff.supplier.name,
          switching_to_tariff.tariff_name,
          switch_status_url
        )

        # Submit the switch to the supplier
        SubmitEnergySwitchJob.perform_async(@energy_switch.id)
      end
      
      success(energy_switch: @energy_switch)
    rescue StandardError => e
      Rails.logger.error "Failed to process switch: #{e.message}"
      error(message: "Failed to process energy switch", error: e.message)
    end
  end
end