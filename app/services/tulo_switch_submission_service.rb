class TuloSwitchSubmissionService < BaseService
  def initialize(energy_switch)
    @energy_switch = energy_switch
    @switch_user = energy_switch.switch_user
    @payment_method = PaymentMethod.find(energy_switch.payment_method_id)
    @address = energy_switch.address
    @supplier = energy_switch.switching_to_tariff.supplier
    @tulo_client = TuloApiClient.new
    
    # Get the latest submission attempt number if any
    latest_submission = @energy_switch.supplier_submissions
                                     .where(supplier: @supplier)
                                     .order(attempt_number: :desc)
                                     .first
    @attempt_number = latest_submission ? latest_submission.attempt_number + 1 : 1
  end

  def submit
    # Build the request parameters
    sale_params = build_sale_params
    
    # Create a pending submission record
    submission = @energy_switch.supplier_submissions.create!(
      supplier: @supplier,
      submission_type: 'switch_request',
      status: :pending,
      attempt_number: @attempt_number,
      raw_request: sale_params.to_json
    )

    begin
      # Update submission to submitted status
      submission.update!(status: :submitted, submitted_at: Time.current)
      
      # Make the API call
      response = @tulo_client.sale_made(sale_params)
      
      # Check if the response indicates success
      if response['success'] == false
        error_message = response['errorMessage'] || "Supplier returned unsuccessful response"
        Rails.logger.error "Failed to submit switch to #{@supplier.name}: #{error_message}"
        
        # Determine if it's a permanent failure or something that should be retried
        status = :failed
        rejection_reason = nil
        
        # Check for rejection conditions 
        if error_message.include?('Invalid MPAN') || error_message.include?('Invalid MPRN') || error_message.include?('Bad Request')
          status = :rejected
          rejection_reason = parse_rejection_reason(error_message)
          
          # Update energy switch status as rejected by supplier
          @energy_switch.update!(status: :rejected_by_supplier)
        end
        
        # Update the submission record with failure details
        submission.update!(
          status: status,
          error_message: error_message,
          rejection_reason: rejection_reason,
          processed_at: Time.current
        )

        # Update energy_switch with supplier reference
        @energy_switch.update!(
          supplier_reference: response['CustomerQuoteId'] || response['customerQuoteId'],
          status: :rejected_by_supplier
        )
        
        # Return failure result
        return OpenStruct.new(
          success?: false,
          error: error_message
        )
      end
      
      # Update the submission with success info
      submission.update!(
        status: :successful,
        supplier_reference: response['CustomerQuoteId'] || response['customerQuoteId'],
        raw_response: response.to_json,
        processed_at: Time.current
      )
      
      # Update energy_switch with supplier reference
      @energy_switch.update!(
        supplier_reference: response['CustomerQuoteId'] || response['customerQuoteId'],
        status: :submitted_to_supplier
      )
      
      # Using OpenStruct to return the exact attributes the tests expect
      return OpenStruct.new(
        success?: true,
        energy_switch: @energy_switch,
        supplier_reference: response['CustomerQuoteId'] || response['customerQuoteId']
      )
    rescue StandardError => e
      error_message = "Failed to submit switch to #{@supplier.name}: #{e.message}"
      Rails.logger.error error_message
      
      # Determine if it's a permanent failure or something that should be retried
      status = :failed
      rejection_reason = nil
      
      # Check for rejection conditions
      if e.message.include?('Invalid MPAN') || e.message.include?('Invalid MPRN') || e.message.include?('Bad Request')
        status = :rejected
        rejection_reason = parse_rejection_reason(e.message)
        
        # Update energy switch status as rejected by supplier
        @energy_switch.update!(status: :rejected_by_supplier)
      end
      
      # Update the submission record with failure details
      submission.update!(
        status: status,
        error_message: e.message,
        rejection_reason: rejection_reason,
        processed_at: Time.current
      )
      
      # Using OpenStruct to return the exact attributes the tests expect
      return OpenStruct.new(
        success?: false,
        error: e.message
      )
    end
  end

  private

  def build_sale_params
    {
      "SaleMadeBy" => "MeetGeorge",
      "SaleMadeOn" => Time.zone.now.iso8601,
      "SaleValidatedBy" => "MeetGeorge",
      "SaleValidatedOn" => Time.zone.now.iso8601,
      "SaleValidationStatus" => "V",
      "SaleChannel" => "MeetGeorgeApp",
      "SaleType" => "CoS", # Change of Supplier
      "CustomerReference" => @energy_switch.reference_number,
      "SaleCustomerType" => 1, # Assuming residential customer
      "CustomerTitle" => get_title_from_name(@switch_user.first_name),
      "CustomerFirstName" => @switch_user.first_name,
      "CustomerLastName" => @switch_user.last_name,
      "CustomerAddressLine1" => @address.full_address,
      "CustomerTownCity" => @address.posttown,
      "CustomerPostcode" => @address.postcode,
      "CustomerEmail" => @switch_user.email,
      "CustomerPhoneNumber" => @switch_user.phone_number,
      "CustomerDateOfBirth" => @switch_user.date_of_birth&.iso8601,
      "PaymentMethod" => "Direct Debit",
      "ElectricityTariffName" => @energy_switch.switching_to_tariff.tariff_name,
      "DDElectricMonthlyAmount" => @energy_switch.electricity_energy_tariff ? @energy_switch.electricity_energy_tariff.calculate_costs('electricity').last : nil,
      "DDGasMonthlyAmount" => @energy_switch.gas_energy_tariff ? @energy_switch.gas_energy_tariff.calculate_costs('gas').last : nil,
      "MPANs" => build_mpans,
      "MPRNs" => build_mprns,
      "BankAccountName" => @payment_method.account_holder_name,
      "BankAccountNumber" => @payment_method.account_number,
      "BankSortCode" => @payment_method.sort_code.gsub('-', '')
    }.compact
  end

  def build_mpans
    return [] unless @energy_switch.electricity_energy_tariff.present?
    
    [{
      "MPAN" => @energy_switch.electricity_energy_tariff.meter_point_administration_number,
      "UseIndustryDataLookup" => true
    }]
  end

  def build_mprns
    return [] unless @energy_switch.gas_energy_tariff.present?
    
    [{
      "MPRN" => @energy_switch.gas_energy_tariff.meter_point_reference_number,
      "UseIndustryDataLookup" => true
    }]
  end

  def get_title_from_name(first_name)
    # This is a simplistic approach - in a real implementation, 
    # you would have the title stored or have a more robust way to determine it
    "Mr" # Default title when we don't have specific information
  end
  
  def parse_rejection_reason(error_message)
    # Extract a more user-friendly rejection reason from the error message
    # This could be enhanced with pattern matching or more sophisticated parsing
    if error_message.include?('already exists')
      'Customer already has a pending switch with this supplier'
    elsif error_message.include?('Invalid MPAN')
      'Invalid electricity meter point details'
    elsif error_message.include?('Invalid MPRN')
      'Invalid gas meter point details'
    elsif error_message.include?('address')
      'Address validation failed'
    elsif error_message.include?('payment') || error_message.include?('bank')
      'Payment details validation failed'
    else
      # Generic fallback
      'Supplier rejected the switch request - see error details'
    end
  end
end 