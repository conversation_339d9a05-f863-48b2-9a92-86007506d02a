# app/services/security_monitoring_service.rb
class SecurityMonitoringService
  # Maximum allowed verification attempts before triggering alert
  MAX_VERIFICATION_ATTEMPTS_THRESHOLD = 10
  
  # Maximum allowed verification code sends before triggering alert
  MAX_CODE_SENDS_THRESHOLD = 20
  
  # Time window for monitoring in hours
  MONITORING_WINDOW = 24
  
  class << self
    # Record a verification attempt for monitoring
    def record_verification_attempt(email, ip_address, success)
      increment_counter("verification_attempts")
      
      # Record the attempt in the database if we're using the SecurityEvent model
      if security_event_defined?
        SecurityEvent.create(
          event_type: success ? 'verification_success' : 'verification_failure',
          email: email,
          ip_address: ip_address,
          details: { success: success, timestamp: Time.now.utc }
        )
      end
      
      # Check for suspicious activity
      check_verification_attempts(email, ip_address)
    end
    
    # Record a verification code send for monitoring
    def record_code_send(email, ip_address)
      increment_counter("code_sends")
      
      # Record the code send in the database if we're using the SecurityEvent model
      if security_event_defined?
        SecurityEvent.create(
          event_type: 'verification_code_sent',
          email: email,
          ip_address: ip_address,
          details: { timestamp: Time.now.utc }
        )
      end
      
      # Check for suspicious activity
      check_code_sends(email, ip_address)
    end
    
    # Record rate limiting being triggered
    def record_rate_limit_triggered(email, ip_address, limit_type)
      if security_event_defined?
        SecurityEvent.create(
          event_type: 'rate_limit_triggered',
          email: email,
          ip_address: ip_address,
          details: { limit_type: limit_type, timestamp: Time.now.utc }
        )
      end
      
      # Always log rate limit triggers as warnings
      Rails.logger.warn("Rate limit triggered - Type: #{limit_type}, Email: #{email}, IP: #{ip_address}")
      
      # Alert if needed
      # trigger_alert("Rate limit triggered for #{email} from #{ip_address} (#{limit_type})")
    end
    
    private
    
    def security_event_defined?
      defined?(SecurityEvent) && SecurityEvent.is_a?(Class)
    end
    
    def slack_defined?
      defined?(Slack) && Slack.is_a?(Module) && ENV['SLACK_SECURITY_WEBHOOK_URL'].present?
    end
    
    def sentry_defined?
      defined?(Sentry) && Sentry.is_a?(Module)
    end
    
    # Increment a monitoring counter in cache
    def increment_counter(counter_name)
      current_count = Rails.cache.fetch("security_monitoring:#{counter_name}", expires_in: MONITORING_WINDOW.hours) { 0 }
      Rails.cache.write("security_monitoring:#{counter_name}", current_count + 1, expires_in: MONITORING_WINDOW.hours)
    end
    
    # Check for suspicious verification attempts
    def check_verification_attempts(email, ip_address)
      # Check email-specific attempts
      email_key = "verification_attempts:#{email}"
      email_attempts = Rails.cache.fetch(email_key, expires_in: MONITORING_WINDOW.hours) { 0 }
      Rails.cache.write(email_key, email_attempts + 1, expires_in: MONITORING_WINDOW.hours)
      
      # Check IP-specific attempts
      ip_key = "verification_attempts:ip:#{ip_address}"
      ip_attempts = Rails.cache.fetch(ip_key, expires_in: MONITORING_WINDOW.hours) { 0 }
      Rails.cache.write(ip_key, ip_attempts + 1, expires_in: MONITORING_WINDOW.hours)
      
      # Trigger alerts if thresholds are exceeded
      if email_attempts >= MAX_VERIFICATION_ATTEMPTS_THRESHOLD
        trigger_alert("Excessive verification attempts for email: #{email}")
      end
      
      if ip_attempts >= MAX_VERIFICATION_ATTEMPTS_THRESHOLD
        trigger_alert("Excessive verification attempts from IP: #{ip_address}")
      end
    end
    
    # Check for suspicious code sends
    def check_code_sends(email, ip_address)
      # Check email-specific code sends
      email_key = "code_sends:#{email}"
      email_sends = Rails.cache.fetch(email_key, expires_in: MONITORING_WINDOW.hours) { 0 }
      Rails.cache.write(email_key, email_sends + 1, expires_in: MONITORING_WINDOW.hours)
      
      # Check IP-specific code sends
      ip_key = "code_sends:ip:#{ip_address}"
      ip_sends = Rails.cache.fetch(ip_key, expires_in: MONITORING_WINDOW.hours) { 0 }
      Rails.cache.write(ip_key, ip_sends + 1, expires_in: MONITORING_WINDOW.hours)
      
      # Trigger alerts if thresholds are exceeded
      if email_sends >= MAX_CODE_SENDS_THRESHOLD
        trigger_alert("Excessive verification code sends for email: #{email}")
      end
      
      if ip_sends >= MAX_CODE_SENDS_THRESHOLD
        trigger_alert("Excessive verification code sends from IP: #{ip_address}")
      end
    end
    
    # Trigger a security alert
    def trigger_alert(message)
      Rails.logger.error("SECURITY ALERT: #{message}")
      
      # Send notifications asynchronously via job
      SendSecurityAlertJob.perform_async(message)
    end
  end
end 