# frozen_string_literal: true

require 'net/http'
require 'active_support/core_ext/hash'
require 'json'
require 'uri'

class XoserveElectricityApiService
  class EcoesApiError < StandardError; end

  # HTTP response codes
  HTTP_OK = 200
  HTTP_BAD_REQUEST = 400
  HTTP_UNAUTHORIZED = 401
  HTTP_FORBIDDEN = 403
  HTTP_NOT_FOUND = 404
  HTTP_INTERNAL_ERROR = 500

  # Constants for configuration
  REQUIRED_ENV_VARS = %w[
    ECOES_BASE_URL
    ECOES_SUBSCRIPTION_KEY
    ECOES_APP_NAME
  ].freeze

  DEFAULT_TIMEOUT = 10      # seconds
  DEFAULT_OPEN_TIMEOUT = 5  # seconds

  attr_reader :client

  def initialize
    load_configuration
    validate_configuration!
    initialize_client
  end

  # Example response
  #   [
  #     {
  #       "UtilityDetails": [
  #           {
  #               "Key": "mpan_core",
  #               "Value": "2000006535006"
  #           },
  #           {
  #               "Key": "address_line_1",
  #               "Value": null
  #           },
  #           {
  #               "Key": "address_line_2",
  #               "Value": null
  #           },
  #           {
  #               "Key": "address_line_3",
  #               "Value": "28"
  #           },
  #           {
  #               "Key": "address_line_4",
  #               "Value": null
  #           },
  #           {
  #               "Key": "address_line_5",
  #               "Value": "PRIMROSE DRIVE"
  #           },
  #           {
  #               "Key": "address_line_6",
  #               "Value": null
  #           },
  #           {
  #               "Key": "address_line_7",
  #               "Value": null
  #           },
  #           {
  #               "Key": "address_line_8",
  #               "Value": "MELKSHAM"
  #           },
  #           {
  #               "Key": "address_line_9",
  #               "Value": "WILTSHIRE"
  #           },
  #           {
  #               "Key": "postcode",
  #               "Value": "SN12 6GB"
  #           },
  #           {
  #               "Key": "trading_status_efd",
  #               "Value": "19980925"
  #           },
  #           {
  #               "Key": "profile_class",
  #               "Value": "01"
  #           },
  #           {
  #               "Key": "profile_class_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "meter_timeswitch_class",
  #               "Value": "801"
  #           },
  #           {
  #               "Key": "meter_timeswitch_class_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "line_loss_factor",
  #               "Value": "100"
  #           },
  #           {
  #               "Key": "line_loss_factor_efd",
  #               "Value": "19980925"
  #           },
  #           {
  #               "Key": "standard_settlement_configuration",
  #               "Value": "0393"
  #           },
  #           {
  #               "Key": "standard_settlement_configuration_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "energisation_status",
  #               "Value": "E"
  #           },
  #           {
  #               "Key": "trading_status",
  #               "Value": "T"
  #           },
  #           {
  #               "Key": "gsp_group_id",
  #               "Value": "_H"
  #           },
  #           {
  #               "Key": "gsp_group_efd",
  #               "Value": "19960401"
  #           },
  #           {
  #               "Key": "data_aggregator_mpid",
  #               "Value": "SSIL"
  #           },
  #           {
  #               "Key": "data_aggregator_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "data_collector_mpid",
  #               "Value": "SSIL"
  #           },
  #           {
  #               "Key": "data_collector_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "supplier_mpid",
  #               "Value": "MARI"
  #           },
  #           {
  #               "Key": "supplier_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "energisation_status_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "meter_operator_mpid",
  #               "Value": "SWEB"
  #           },
  #           {
  #               "Key": "meter_operator_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "measurement_class",
  #               "Value": "A"
  #           },
  #           {
  #               "Key": "measurement_class_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "green_deal_in_effect",
  #               "Value": "0"
  #           },
  #           {
  #               "Key": "smso_mpid",
  #               "Value": "LCMG"
  #           },
  #           {
  #               "Key": "smso_efd",
  #               "Value": "20210714"
  #           },
  #           {
  #               "Key": "dcc_service_flag",
  #               "Value": "A"
  #           },
  #           {
  #               "Key": "dcc_service_flag_efd",
  #               "Value": "20210714"
  #           },
  #           {
  #               "Key": "ihd_status",
  #               "Value": "I"
  #           },
  #           {
  #               "Key": "ihd_status_efd",
  #               "Value": "20210714"
  #           },
  #           {
  #               "Key": "smets_version",
  #               "Value": "SMETS2"
  #           },
  #           {
  #               "Key": "distributor_mpid",
  #               "Value": "SOUT"
  #           },
  #           {
  #               "Key": "metered_indicator",
  #               "Value": "T"
  #           },
  #           {
  #               "Key": "metered_indicator_efd",
  #               "Value": "20010301"
  #           },
  #           {
  #               "Key": "metered_indicator_etd",
  #               "Value": null
  #           },
  #           {
  #               "Key": "consumer_type",
  #               "Value": "Domestic"
  #           },
  #           {
  #               "Key": "domestic_consumer_indicator",
  #               "Value": "T"
  #           },
  #           {
  #               "Key": "relationship_status_indicator",
  #               "Value": "None"
  #           },
  #           {
  #               "Key": "rmp_state",
  #               "Value": "O"
  #           },
  #           {
  #               "Key": "rmp_efd",
  #               "Value": "19980925"
  #           },
  #           {
  #               "Key": "energy_direction",
  #               "Value": "I"
  #           },
  #           {
  #               "Key": "energy_direction_efd",
  #               "Value": "19960401"
  #           },
  #           {
  #               "Key": "energy_direction_etd",
  #               "Value": null
  #           },
  #           {
  #               "Key": "connection_type",
  #               "Value": "W"
  #           },
  #           {
  #               "Key": "connection_type_efd",
  #               "Value": "20240217"
  #           },
  #           {
  #               "Key": "connection_type_etd",
  #               "Value": null
  #           },
  #           {
  #               "Key": "css_supplier_mpid",
  #               "Value": "MARI"
  #           },
  #           {
  #               "Key": "css_supply_start_date",
  #               "Value": "2024-02-17 00:00:00.000"
  #           }
  #       ],
  #       "UtilityKey": "2000006535006",
  #       "UtilityType": "electricity",
  #       "Meters": [
  #           {
  #               "MeterDetails": [
  #                   {
  #                       "Key": "mpancore",
  #                       "Value": "2000006535006"
  #                   },
  #                   {
  #                       "Key": "meter_serial_number",
  #                       "Value": "21L3737050"
  #                   },
  #                   {
  #                       "Key": "meter_install_date",
  #                       "Value": "20210714"
  #                   },
  #                   {
  #                       "Key": "meter_type",
  #                       "Value": "S2AD"
  #                   },
  #                   {
  #                       "Key": "map_mpid",
  #                       "Value": "SGNS"
  #                   },
  #                   {
  #                       "Key": "map_mpid_efd",
  #                       "Value": "20210714"
  #                   },
  #                   {
  #                       "Key": "installing_supplier_mpid",
  #                       "Value": "SOUT"
  #                   },
  #                   {
  #                       "Key": "meter_location",
  #                       "Value": "F"
  #                   },
  #                   {
  #                       "Key": "register_digits",
  #                       "Value": "5"
  #                   },
  #                   {
  #                       "Key": "esme_id",
  #                       "Value": "30-EB-5A-FF-FF-93-1C-C1"
  #                   }
  #               ]
  #           }
  #       ]
  #   }
  # ]
  def get_technical_details_by_mpan(mpan)
    # Check if the data already exists in the database
    cached_data = XoserveElectricityRecord.find_by_mpan_core(mpan)
    
    if cached_data.present? && cached_data['UtilityDetails'].present?
      utility_details = cached_data['UtilityDetails']

      profile_class = utility_details.find { |detail| detail['Key'] == 'profile_class' }['Value']
      supplier_mpid = utility_details.find { |detail| detail['Key'] == 'supplier_mpid' }['Value']
      energisation_status_efd = utility_details.find { |detail| detail['Key'] == 'energisation_status_efd' }['Value']

      return {
        profile_class: profile_class.to_i,
        supplier_mpid: supplier_mpid,
        energisation_status_efd: energisation_status_efd,
        raw_data: cached_data
      }
    end

    full_request = {
      Authentication: { Key: @subscription_key },
      ParameterSets: [{
        Parameters: [
          { Key: 'mpan', Value: mpan }
        ]
      }]
    }

    result = make_request(
      http_method: :post,
      endpoint: '/WebServices/Service/ECOESAPI.svc/RESTful/JSON/GetTechnicalDetailsByMpan',
      body: full_request
    )

    # Add validation to check if response has the expected structure
    if result.nil? || !result['Results'] || result['Results'].empty? || 
       !result['Results'][0]['UtilityMatches'] || result['Results'][0]['UtilityMatches'].empty?
      raise EcoesApiError, "Invalid MPAN or no data returned for MPAN: #{mpan}"
    end

    XoserveElectricityRecord.store_technical_details_json(mpan, result['Results'][0]['UtilityMatches'][0])

    utility_details = result['Results'][0]['UtilityMatches'][0]['UtilityDetails']
    profile_class = utility_details.find { |detail| detail['Key'] == 'profile_class' }['Value']
    supplier_mpid = utility_details.find { |detail| detail['Key'] == 'supplier_mpid' }['Value']
    energisation_status_efd = utility_details.find { |detail| detail['Key'] == 'energisation_status_efd' }['Value']

    {
      profile_class: profile_class.to_i,
      supplier_mpid: supplier_mpid,
      energisation_status_efd: energisation_status_efd,
      raw_data: result['Results'][0]
    }
  end

  # Example response
  # [
  #   {"AddressDetails"=>
  #     [{"Key"=>"mpan_core", "Value"=>"2000006535006"},
  #       {"Key"=>"address_line_1", "Value"=>nil},
  #       {"Key"=>"address_line_2", "Value"=>nil},
  #       {"Key"=>"address_line_3", "Value"=>"28"},
  #       {"Key"=>"address_line_4", "Value"=>nil},
  #       {"Key"=>"address_line_5", "Value"=>"PRIMROSE DRIVE"},
  #       {"Key"=>"address_line_6", "Value"=>nil},
  #       {"Key"=>"address_line_7", "Value"=>nil},
  #       {"Key"=>"address_line_8", "Value"=>"MELKSHAM"},
  #       {"Key"=>"address_line_9", "Value"=>"WILTSHIRE"},
  #       {"Key"=>"postcode", "Value"=>"SN12 6GB"},
  #       {"Key"=>"gsp_group_id", "Value"=>"_H"},
  #       {"Key"=>"trading_status", "Value"=>"T"},
  #       {"Key"=>"distributor_mpid", "Value"=>"SOUT"}
  #   ]
  # }
  def search_utility_address(meter_serial_number)
    # Check if the data already exists in the database
    cached_data = XoserveElectricityRecord.find_by_meter_serial_number(meter_serial_number)
    
    if cached_data.present?
      # Return the data from cache
      address_details = cached_data['AddressDetails']
      mpan_core = address_details.find { |detail| detail['Key'] == 'mpan_core' }['Value']
      gsp_code = address_details.find { |detail| detail['Key'] == 'gsp_group_id' }['Value']
      postcode = address_details.find { |detail| detail['Key'] == 'postcode' }['Value']
      trading_status = address_details.find { |detail| detail['Key'] == 'trading_status' }['Value']
      distributor_mpid = address_details.find { |detail| detail['Key'] == 'distributor_mpid' }['Value']
      
      return {
        mpan: mpan_core,
        gsp_code: gsp_code,
        postcode: postcode,
        trading_status: trading_status,
        distributor_mpid: distributor_mpid,
        raw_data: cached_data
      }
    end
    
    # If not in the database, proceed with the API call
    full_request = {
      Authentication: { Key: @subscription_key },
      ParameterSets: [{
        Parameters: [
          { Key: 'MeterSerialNumber', Value: meter_serial_number }
        ]
      }]
    }

    result = make_request(
      http_method: :post,
      endpoint: '/WebServices/Service/ECOESAPI.svc/RESTful/JSON/SearchUtilityAddress',
      body: full_request
    )

    # Add validation to check if response has the expected structure
    if result.nil? || !result['Results'] || result['Results'].empty? || 
       !result['Results'][0]['UtilityAddressMatches'] || result['Results'][0]['UtilityAddressMatches'].empty?
      raise EcoesApiError, "Invalid meter serial number or no data returned for: #{meter_serial_number}"
    end

    address_details = result['Results'][0]['UtilityAddressMatches'][0]['AddressDetails']
    mpan_core = address_details.find { |detail| detail['Key'] == 'mpan_core' }['Value']

    utility_address_match = result['Results'][0]['UtilityAddressMatches'][0]
    XoserveElectricityRecord.store_utility_address_json(meter_serial_number, mpan_core, utility_address_match)

    gsp_code = address_details.find { |detail| detail['Key'] == 'gsp_group_id' }['Value']
    postcode = address_details.find { |detail| detail['Key'] == 'postcode' }['Value']
    trading_status = address_details.find { |detail| detail['Key'] == 'trading_status' }['Value']
    distributor_mpid = address_details.find { |detail| detail['Key'] == 'distributor_mpid' }['Value']

    {
      mpan: mpan_core,
      gsp_code: gsp_code,
      postcode: postcode,
      trading_status: trading_status,
      distributor_mpid: distributor_mpid,
      raw_data: result['Results'][0]
    }
  end

  private

  def load_configuration
    @base_url = ENV['ECOES_BASE_URL']
    @subscription_key = ENV['ECOES_SUBSCRIPTION_KEY']
    @app_name = ENV['ECOES_APP_NAME']
  end

  def validate_configuration!
    missing_vars = REQUIRED_ENV_VARS.select { |var| ENV[var].nil? || ENV[var].empty? }
    raise EcoesApiError, "Missing required environment variables: #{missing_vars.join(', ')}" if missing_vars.any?
    
    validate_urls!
  end

  def validate_urls!
    uri = URI.parse(@base_url)
    raise EcoesApiError, "Invalid URL format: #{@base_url}" unless uri.is_a?(URI::HTTP) || uri.is_a?(URI::HTTPS)
  rescue URI::InvalidURIError
    raise EcoesApiError, "Invalid URL format"
  end

  def initialize_client
    uri = URI.parse(@base_url)
    @http = Net::HTTP.new(uri.host, uri.port)
    @http.use_ssl = uri.scheme == 'https'
    @http.verify_mode = OpenSSL::SSL::VERIFY_NONE
  end

  def make_request(http_method:, endpoint:, params: {}, body: nil)
    url = URI.join(@base_url, endpoint)
    request = case http_method
              when :post
                Net::HTTP::Post.new(url)
              when :get
                Net::HTTP::Get.new(url)
              else
                raise EcoesApiError, "Unsupported HTTP method: #{http_method}"
              end

    request["Content-Type"] = "application/json"
    request.body = JSON.dump(body) if body

    response = @http.request(request)
    handle_response(response)
  rescue StandardError => e
    raise EcoesApiError, "Request failed: #{e.message}"
  end

  def handle_response(response)
    case response.code.to_i
    when HTTP_OK
      JSON.parse(response.body)
    when HTTP_BAD_REQUEST
      raise EcoesApiError, "Bad request: #{parse_error_message(response)}"
    when HTTP_UNAUTHORIZED
      raise EcoesApiError, "Unauthorized: #{parse_error_message(response)}"
    when HTTP_FORBIDDEN
      raise EcoesApiError, "Forbidden: #{parse_error_message(response)}"
    when HTTP_NOT_FOUND
      raise EcoesApiError, "Not found: #{parse_error_message(response)}"
    when HTTP_INTERNAL_ERROR
      raise EcoesApiError, "Internal server error: #{parse_error_message(response)}"
    else
      raise EcoesApiError, "Unexpected response code #{response.code}: #{parse_error_message(response)}"
    end
  end

  def parse_error_message(response)
    return response.reason_phrase if response.body.nil? || response.body.empty?
    
    error_body = response.body
    return error_body['fault'].first['faultString'] if error_body['fault']&.first

    response.reason_phrase
  rescue StandardError
    response.reason_phrase
  end
end