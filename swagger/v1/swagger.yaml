---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/switch_users":
    get:
      summary: Creates a switch
      tags:
      - Switch Users
      parameters: []
      responses:
        '201':
          description: swtich user created
        '422':
          description: invalid request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                address:
                  type: string
              required:
              - email
              - address
servers:
- url: https://{defaultHost}
  variables:
    defaultHost:
      default: www.example.com
